<div class="register-container">
  <div class="register-card">
    <div class="register-header">
      <h1>Inscription Fournisseur</h1>
      <p><PERSON><PERSON>ez votre compte fournisseur</p>
    </div>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
      
      <!-- Informations personnelles -->
      <div class="form-section">
        <h3>Informations personnelles</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="prenom">Prénom *</label>
            <input
              type="text"
              id="prenom"
              formControlName="prenom"
              class="form-control"
              [class.is-invalid]="prenom?.invalid && prenom?.touched"
              placeholder="Votre prénom"
            />
            <div class="invalid-feedback" *ngIf="prenom?.invalid && prenom?.touched">
              <div *ngIf="prenom?.errors?.['required']">Le prénom est requis</div>
              <div *ngIf="prenom?.errors?.['minlength']">Le prénom doit contenir au moins 2 caractères</div>
            </div>
          </div>

          <div class="form-group">
            <label for="nom">Nom *</label>
            <input
              type="text"
              id="nom"
              formControlName="nom"
              class="form-control"
              [class.is-invalid]="nom?.invalid && nom?.touched"
              placeholder="Votre nom"
            />
            <div class="invalid-feedback" *ngIf="nom?.invalid && nom?.touched">
              <div *ngIf="nom?.errors?.['required']">Le nom est requis</div>
              <div *ngIf="nom?.errors?.['minlength']">Le nom doit contenir au moins 2 caractères</div>
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="email">Email *</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="form-control"
              [class.is-invalid]="email?.invalid && email?.touched"
              placeholder="<EMAIL>"
            />
            <div class="invalid-feedback" *ngIf="email?.invalid && email?.touched">
              <div *ngIf="email?.errors?.['required']">L'email est requis</div>
              <div *ngIf="email?.errors?.['email']">Format d'email invalide</div>
            </div>
          </div>

          <div class="form-group">
            <label for="phoneNumber">Téléphone *</label>
            <input
              type="tel"
              id="phoneNumber"
              formControlName="phoneNumber"
              class="form-control"
              [class.is-invalid]="phoneNumber?.invalid && phoneNumber?.touched"
              placeholder="0123456789"
            />
            <div class="invalid-feedback" *ngIf="phoneNumber?.invalid && phoneNumber?.touched">
              <div *ngIf="phoneNumber?.errors?.['required']">Le téléphone est requis</div>
              <div *ngIf="phoneNumber?.errors?.['pattern']">Le téléphone doit contenir entre 8 et 15 chiffres</div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="dateNaissance">Date de naissance *</label>
          <input
            type="date"
            id="dateNaissance"
            formControlName="dateNaissance"
            class="form-control"
            [class.is-invalid]="dateNaissance?.invalid && dateNaissance?.touched"
          />
          <div class="invalid-feedback" *ngIf="dateNaissance?.invalid && dateNaissance?.touched">
            <div *ngIf="dateNaissance?.errors?.['required']">La date de naissance est requise</div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="password">Mot de passe *</label>
            <input
              type="password"
              id="password"
              formControlName="password"
              class="form-control"
              [class.is-invalid]="password?.invalid && password?.touched"
              placeholder="Votre mot de passe"
            />
            <div class="invalid-feedback" *ngIf="password?.invalid && password?.touched">
              <div *ngIf="password?.errors?.['required']">Le mot de passe est requis</div>
              <div *ngIf="password?.errors?.['minlength']">Le mot de passe doit contenir au moins 6 caractères</div>
            </div>
          </div>

          <div class="form-group">
            <label for="confirmPassword">Confirmer le mot de passe *</label>
            <input
              type="password"
              id="confirmPassword"
              formControlName="confirmPassword"
              class="form-control"
              [class.is-invalid]="confirmPassword?.invalid && confirmPassword?.touched"
              placeholder="Confirmez votre mot de passe"
            />
            <div class="invalid-feedback" *ngIf="confirmPassword?.invalid && confirmPassword?.touched">
              <div *ngIf="confirmPassword?.errors?.['required']">La confirmation est requise</div>
              <div *ngIf="confirmPassword?.errors?.['passwordMismatch']">Les mots de passe ne correspondent pas</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Informations entreprise -->
      <div class="form-section">
        <h3>Informations entreprise</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="matriculeFiscale">Matricule Fiscale *</label>
            <input
              type="text"
              id="matriculeFiscale"
              formControlName="matriculeFiscale"
              class="form-control"
              [class.is-invalid]="matriculeFiscale?.invalid && matriculeFiscale?.touched"
              placeholder="12345678"
              maxlength="8"
            />
            <div class="invalid-feedback" *ngIf="matriculeFiscale?.invalid && matriculeFiscale?.touched">
              <div *ngIf="matriculeFiscale?.errors?.['required']">Le matricule fiscale est requis</div>
              <div *ngIf="matriculeFiscale?.errors?.['minlength'] || matriculeFiscale?.errors?.['maxlength']">Le matricule fiscale doit contenir exactement 8 caractères</div>
            </div>
          </div>

          <div class="form-group">
            <label for="raisonSociale">Raison Sociale *</label>
            <input
              type="text"
              id="raisonSociale"
              formControlName="raisonSociale"
              class="form-control"
              [class.is-invalid]="raisonSociale?.invalid && raisonSociale?.touched"
              placeholder="Nom de votre entreprise"
              maxlength="200"
            />
            <div class="invalid-feedback" *ngIf="raisonSociale?.invalid && raisonSociale?.touched">
              <div *ngIf="raisonSociale?.errors?.['required']">La raison sociale est requise</div>
              <div *ngIf="raisonSociale?.errors?.['minlength']">La raison sociale doit contenir au moins 2 caractères</div>
              <div *ngIf="raisonSociale?.errors?.['maxlength']">La raison sociale ne peut pas dépasser 200 caractères</div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="description">Description (optionnel)</label>
          <textarea
            id="description"
            formControlName="description"
            class="form-control"
            [class.is-invalid]="description?.invalid && description?.touched"
            placeholder="Description de votre entreprise"
            rows="3"
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="rib">RIB *</label>
            <input
              type="text"
              id="rib"
              formControlName="rib"
              class="form-control"
              [class.is-invalid]="rib?.invalid && rib?.touched"
              placeholder="12345678901234567890"
              maxlength="20"
            />
            <div class="invalid-feedback" *ngIf="rib?.invalid && rib?.touched">
              <div *ngIf="rib?.errors?.['required']">Le RIB est requis</div>
              <div *ngIf="rib?.errors?.['minlength'] || rib?.errors?.['maxlength']">Le RIB doit contenir exactement 20 caractères</div>
            </div>
          </div>

          <div class="form-group">
            <label for="codeBanque">Code Banque *</label>
            <input
              type="text"
              id="codeBanque"
              formControlName="codeBanque"
              class="form-control"
              [class.is-invalid]="codeBanque?.invalid && codeBanque?.touched"
              placeholder="123"
              maxlength="3"
            />
            <div class="invalid-feedback" *ngIf="codeBanque?.invalid && codeBanque?.touched">
              <div *ngIf="codeBanque?.errors?.['required']">Le code banque est requis</div>
              <div *ngIf="codeBanque?.errors?.['minlength'] || codeBanque?.errors?.['maxlength']">Le code banque doit contenir exactement 3 caractères</div>
            </div>
          </div>
        </div>

        <!-- Paramètres commerciaux -->
        <div class="form-row">
          <div class="form-group">
            <label for="commission">Commission (%) *</label>
            <input
              type="number"
              id="commission"
              formControlName="commission"
              class="form-control"
              [class.is-invalid]="commission?.invalid && commission?.touched"
              placeholder="0.75"
              min="0.5"
              max="1"
              step="0.01"
            />
            <div class="invalid-feedback" *ngIf="commission?.invalid && commission?.touched">
              <div *ngIf="commission?.errors?.['required']">La commission est requise</div>
              <div *ngIf="commission?.errors?.['min']">La commission doit être au minimum 50% (0.5)</div>
              <div *ngIf="commission?.errors?.['max']">La commission doit être au maximum 100% (1.0)</div>
            </div>
            <small class="form-text text-muted">Commission sur les ventes (entre 50% et 100%)</small>
          </div>

          <div class="form-group">
            <label for="delaiPreparationJours">Délai de préparation (jours) *</label>
            <input
              type="number"
              id="delaiPreparationJours"
              formControlName="delaiPreparationJours"
              class="form-control"
              [class.is-invalid]="delaiPreparationJours?.invalid && delaiPreparationJours?.touched"
              placeholder="2"
              min="1"
              max="30"
            />
            <div class="invalid-feedback" *ngIf="delaiPreparationJours?.invalid && delaiPreparationJours?.touched">
              <div *ngIf="delaiPreparationJours?.errors?.['required']">Le délai de préparation est requis</div>
              <div *ngIf="delaiPreparationJours?.errors?.['min']">Le délai doit être d'au moins 1 jour</div>
            </div>
            <small class="form-text text-muted">Nombre de jours pour préparer une commande</small>
          </div>
        </div>

        <div class="form-group">
          <label for="fraisLivraisonBase">Frais de livraison de base (€) *</label>
          <input
            type="number"
            id="fraisLivraisonBase"
            formControlName="fraisLivraisonBase"
            class="form-control"
            [class.is-invalid]="fraisLivraisonBase?.invalid && fraisLivraisonBase?.touched"
            placeholder="9.99"
            min="0"
            step="0.01"
          />
          <div class="invalid-feedback" *ngIf="fraisLivraisonBase?.invalid && fraisLivraisonBase?.touched">
            <div *ngIf="fraisLivraisonBase?.errors?.['required']">Les frais de livraison sont requis</div>
            <div *ngIf="fraisLivraisonBase?.errors?.['min']">Les frais de livraison doivent être positifs</div>
          </div>
          <small class="form-text text-muted">Frais de livraison de base pour vos produits</small>
        </div>

        <!-- Adresse de l'entreprise -->
        <h3 class="section-title">📍 Adresse de l'entreprise</h3>

        <div class="form-group">
          <label for="rue">Rue *</label>
          <input
            type="text"
            id="rue"
            formControlName="rue"
            class="form-control"
            [class.is-invalid]="rue?.invalid && rue?.touched"
            placeholder="123 Rue de la République"
          />
          <div class="invalid-feedback" *ngIf="rue?.invalid && rue?.touched">
            <div *ngIf="rue?.errors?.['required']">La rue est requise</div>
            <div *ngIf="rue?.errors?.['minlength']">La rue doit contenir au moins 5 caractères</div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="codePostal">Code postal *</label>
            <input
              type="text"
              id="codePostal"
              formControlName="codePostal"
              class="form-control"
              [class.is-invalid]="codePostal?.invalid && codePostal?.touched"
              placeholder="1000"
              maxlength="4"
            />
            <div class="invalid-feedback" *ngIf="codePostal?.invalid && codePostal?.touched">
              <div *ngIf="codePostal?.errors?.['required']">Le code postal est requis</div>
              <div *ngIf="codePostal?.errors?.['pattern']">Le code postal doit contenir 4 chiffres</div>
            </div>
          </div>

          <div class="form-group">
            <label for="ville">Ville *</label>
            <input
              type="text"
              id="ville"
              formControlName="ville"
              class="form-control"
              [class.is-invalid]="ville?.invalid && ville?.touched"
              placeholder="Paris"
            />
            <div class="invalid-feedback" *ngIf="ville?.invalid && ville?.touched">
              <div *ngIf="ville?.errors?.['required']">La ville est requise</div>
              <div *ngIf="ville?.errors?.['minlength']">La ville doit contenir au moins 2 caractères</div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="pays">Pays *</label>
          <input
            type="text"
            id="pays"
            formControlName="pays"
            class="form-control"
            value="Tunisie"
            readonly
            style="background-color: #f8f9fa; cursor: not-allowed;"
          />
          <small class="form-text text-muted">Le pays est automatiquement défini sur Tunisie</small>
        </div>

        <div class="form-group">
          <label for="logoFile">Logo de l'entreprise (optionnel)</label>
          <input
            type="file"
            id="logoFile"
            class="form-control"
            [class.is-invalid]="logoFile?.invalid && logoFile?.touched"
            accept="image/*"
            (change)="onFileSelected($event)"
          />
          <small class="form-text text-muted">Si aucun logo n'est sélectionné, un logo par défaut sera utilisé.</small>
        </div>
      </div>

      <!-- Messages -->
      <div class="alert alert-danger" *ngIf="errorMessage">
        <div style="white-space: pre-line;">{{ errorMessage }}</div>
      </div>

      <div class="alert alert-success" *ngIf="successMessage">
        {{ successMessage }}
      </div>

      <!-- Boutons -->
      <div class="form-actions">
        <button
          type="button"
          class="btn btn-secondary"
          (click)="goToLogin()"
          [disabled]="isLoading"
        >
          Retour à la connexion
        </button>

        <button
          type="submit"
          class="btn btn-primary"
          [disabled]="isLoading"
        >
          <span *ngIf="isLoading" class="spinner"></span>
          {{ isLoading ? 'Inscription...' : 'S\'inscrire' }}
        </button>
      </div>
    </form>
  </div>
</div>
