import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { AdminService, StatistiquesAdmin } from '../../../services/admin.service';
import { AdminTestService } from '../../../services/admin-test.service';
import { AdminAuthService } from '../../../services/admin-auth.service';
import { UserManagementComponent } from '../user-management/user-management.component';
import { CategoryManagementComponent } from '../category-management/category-management.component';
import { OrderManagementComponent } from '../order-management/order-management.component';
import { ProductManagementComponent } from '../product-management/product-management.component';
import { NotificationIconComponent } from '../../notification-icon/notification-icon.component';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    UserManagementComponent,
    CategoryManagementComponent,
    OrderManagementComponent,
    ProductManagementComponent,
    NotificationIconComponent
  ],
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.css']
})
export class AdminDashboardComponent implements OnInit {
  // Angular 19: Signals
  activeSection = signal<'dashboard' | 'users' | 'categories' | 'orders' | 'products'>('dashboard');
  statistiques = signal<StatistiquesAdmin | null>(null);
  isLoading = signal(false);
  error = signal('');

  // Computed signals
  dashboardCards = computed(() => {
    const stats = this.statistiques();
    if (!stats) return [];

    return [
      {
        title: 'Utilisateurs',
        value: stats.nombreUtilisateurs,
        icon: 'icon-users',
        color: 'blue',
        description: `${stats.nombreFournisseurs} fournisseurs, ${stats.nombreClients} clients`
      },
      {
        title: 'Produits',
        value: stats.nombreProduits || 0,
        icon: 'icon-package',
        color: 'green',
        description: 'Produits disponibles'
      },
      {
        title: 'Commandes',
        value: stats.nombreCommandes,
        icon: 'icon-shopping-cart',
        color: 'orange',
        description: `${stats.nombreCommandesAnnulees || 0} annulées`
      },
      {
        title: 'Ventes',
        value: stats.nombreVentes || 0,
        icon: 'icon-trending-up',
        color: 'purple',
        description: 'Total des ventes'
      }
    ];
  });

  menuItems = [
    {
      id: 'dashboard',
      label: 'Tableau de bord',
      icon: 'icon-home',
      description: 'Vue d\'ensemble et statistiques'
    },
    {
      id: 'users',
      label: 'Utilisateurs',
      icon: 'icon-users',
      description: 'Gestion des comptes utilisateurs'
    },
    {
      id: 'categories',
      label: 'Catégories',
      icon: 'icon-folder',
      description: 'Gestion des catégories et sous-catégories'
    },
    {
      id: 'orders',
      label: 'Commandes',
      icon: 'icon-shopping-cart',
      description: 'Gestion des commandes'
    },
    {
      id: 'products',
      label: 'Produits',
      icon: 'icon-package',
      description: 'Validation et mise en avant des produits'
    }
  ];

  constructor(
    private adminService: AdminService,
    private adminTestService: AdminTestService,
    private adminAuthService: AdminAuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadStatistiques();
  }

  loadStatistiques(): void {
    this.isLoading.set(true);
    this.error.set('');

    this.adminService.getStatistiques().subscribe({
      next: (stats) => {
        console.log('📊 Statistiques reçues:', stats);
        this.statistiques.set(stats);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des statistiques:', error);
        this.error.set('Erreur lors du chargement des statistiques');
        this.isLoading.set(false);
      }
    });
  }

  setActiveSection(section: string): void {
    this.activeSection.set(section as 'dashboard' | 'users' | 'categories' | 'orders' | 'products');
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('fr-FR').format(num);
  }

  refresh(): void {
    this.loadStatistiques();
  }

  getCardClass(color: string): string {
    return `stat-card stat-card-${color}`;
  }

  getMenuItemClass(itemId: string): string {
    return this.activeSection() === itemId ? 'menu-item active' : 'menu-item';
  }

  testBackendConnection(): void {
    console.log('🧪 Démarrage du test de connectivité backend...');

    this.adminTestService.runFullTest().subscribe({
      next: (results) => {
        console.log('📊 Résultats des tests:', results);

        const { summary, details } = results;
        const message = `Tests terminés: ${summary.passed}/${summary.total} réussis\n\n` +
          details.map((test: any) =>
            `${test.success ? '✅' : '❌'} ${test.name}: ${test.success ? 'OK' : 'ERREUR'}`
          ).join('\n');

        alert(message);
      },
      error: (error) => {
        console.error('❌ Erreur lors des tests:', error);
        alert('Erreur lors des tests de connectivité');
      }
    });
  }

  /**
   * Déconnexion admin
   */
  logout(): void {
    this.adminAuthService.logout().subscribe({
      next: () => {
        console.log('✅ Déconnexion admin réussie');
        this.router.navigate(['/adminOptiLet']);
      },
      error: (error) => {
        console.error('❌ Erreur lors de la déconnexion:', error);
        // Forcer la redirection même en cas d'erreur
        this.router.navigate(['/adminOptiLet']);
      }
    });
  }
}
