﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.Models.Entity
{
    public class Notification
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Titre { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        public string Message { get; set; } = string.Empty;

        [Required]
        public TypeNotification Type { get; set; } = TypeNotification.General;

        public DateTime DateEnvoi { get; set; }
        public bool EstLue { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        [ForeignKey(nameof(UtilisateurId))]
        [JsonIgnore]
        public virtual Utilisateur Utilisateur { get; set; } = null!;

        // Propriété de compatibilité pour l'ancien système
        [NotMapped]
        public string Contenu
        {
            get => Message;
            set => Message = value;
        }
    }

}
