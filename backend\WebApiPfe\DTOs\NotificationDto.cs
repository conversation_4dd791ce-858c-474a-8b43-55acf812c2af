using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs
{
    public class NotificationDto
    {
        public int Id { get; set; }
        public string Titre { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public TypeNotification Type { get; set; }
        public DateTime DateEnvoi { get; set; }
        public bool EstLue { get; set; }
        public int UtilisateurId { get; set; }
        
        // Propriété de compatibilité
        public string Contenu => Message;
    }

    public class CreateNotificationDto
    {
        public string Titre { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public TypeNotification Type { get; set; } = TypeNotification.General;
        public int UtilisateurId { get; set; }
        
        // Propriété de compatibilité
        public string Contenu 
        { 
            get => Message; 
            set => Message = value; 
        }
    }
}
