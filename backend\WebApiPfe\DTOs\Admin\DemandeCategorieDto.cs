using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs.Admin
{
    public class DemandeCategorieDto
    {
        public int Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int FournisseurId { get; set; }
        public string NomFournisseur { get; set; } = string.Empty;
        public StatutDemande Statut { get; set; }
        public DateTime DateDemande { get; set; }
        public DateTime? DateTraitement { get; set; }
        public int? AdminTraitantId { get; set; }
        public string? NomAdminTraitant { get; set; }
        public string? CommentaireAdmin { get; set; }
        public int? CategorieCreeeId { get; set; }
        public string? NomCategorieCreee { get; set; }
    }

    public class CreateDemandeCategorieDto
    {
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class TraiterDemandeCategorieDto
    {
        public StatutDemande Statut { get; set; }
        public string? CommentaireAdmin { get; set; }
    }
}
