import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface Adresse {
  id: number;
  nom: string;
  prenom: string;
  entreprise?: string;
  adresse1: string;
  adresse2?: string;
  ville: string;
  codePostal: string;
  pays: string;
  telephone?: string;
  estPrincipale: boolean;
  estFacturation: boolean;
  estLivraison: boolean;
  entityId: number;
  entityType: 'Client' | 'Fournisseur';
  dateCreation: Date;
  dateModification: Date;
}

export interface AdresseCreate {
  nom: string;
  prenom: string;
  entreprise?: string;
  adresse1: string;
  adresse2?: string;
  ville: string;
  codePostal: string;
  pays: string;
  telephone?: string;
  estPrincipale?: boolean;
  estFacturation?: boolean;
  estLivraison?: boolean;
  entityId: number;
  entityType: 'Client' | 'Fournisseur';
}

export interface AdresseUpdate {
  nom?: string;
  prenom?: string;
  entreprise?: string;
  adresse1?: string;
  adresse2?: string;
  ville?: string;
  codePostal?: string;
  pays?: string;
  telephone?: string;
  estPrincipale?: boolean;
  estFacturation?: boolean;
  estLivraison?: boolean;
}

export interface AdresseResponse {
  success: boolean;
  data?: Adresse;
  message?: string;
}

export interface AdresseListResponse {
  success: boolean;
  data?: Adresse[];
  total?: number;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AdresseService {
  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/Adresses`;

  constructor(private http: HttpClient) {}

  /**
   * GET /api/Adresses/{id} - Obtenir une adresse par ID
   */
  getById(id: number): Observable<AdresseResponse> {
    console.log('🏠 Récupération de l\'adresse ID:', id);

    return this.http.get<AdresseResponse>(`${this.API_URL}/${id}`)
      .pipe(
        tap(response => console.log('✅ Adresse récupérée:', response))
      );
  }

  /**
   * PUT /api/Adresses/{id} - Mettre à jour une adresse
   */
  update(id: number, adresse: AdresseUpdate): Observable<AdresseResponse> {
    console.log('✏️ Mise à jour de l\'adresse ID:', id, adresse);

    return this.http.put<AdresseResponse>(`${this.API_URL}/${id}`, adresse)
      .pipe(
        tap(response => console.log('✅ Adresse mise à jour:', response))
      );
  }

  /**
   * DELETE /api/Adresses/{id} - Supprimer une adresse
   */
  delete(id: number): Observable<{ success: boolean; message?: string }> {
    console.log('🗑️ Suppression de l\'adresse ID:', id);

    return this.http.delete<{ success: boolean; message?: string }>(`${this.API_URL}/${id}`)
      .pipe(
        tap(response => console.log('✅ Adresse supprimée:', response))
      );
  }

  /**
   * GET /api/Adresses/entity/{entityId} - Obtenir les adresses d'une entité
   */
  getByEntity(entityId: number, entityType?: 'Client' | 'Fournisseur'): Observable<AdresseListResponse> {
    console.log('🏠 Récupération des adresses pour l\'entité ID:', entityId);

    let url = `${this.API_URL}/entity/${entityId}`;
    if (entityType) {
      url += `?entityType=${entityType}`;
    }

    return this.http.get<AdresseListResponse>(url)
      .pipe(
        tap(response => console.log('✅ Adresses de l\'entité récupérées:', response))
      );
  }

  /**
   * POST /api/Adresses - Créer une nouvelle adresse
   */
  create(adresse: AdresseCreate): Observable<AdresseResponse> {
    console.log('➕ Création d\'une nouvelle adresse:', adresse);

    return this.http.post<AdresseResponse>(this.API_URL, adresse)
      .pipe(
        tap(response => console.log('✅ Adresse créée:', response))
      );
  }

  /**
   * PATCH /api/Adresses/{id}/principale/{entityId} - Définir comme adresse principale
   */
  setAsPrincipale(id: number, entityId: number): Observable<AdresseResponse> {
    console.log('⭐ Définition de l\'adresse principale ID:', id, 'pour l\'entité:', entityId);

    return this.http.patch<AdresseResponse>(`${this.API_URL}/${id}/principale/${entityId}`, {})
      .pipe(
        tap(response => console.log('✅ Adresse définie comme principale:', response))
      );
  }

  /**
   * Obtenir l'adresse principale d'une entité
   */
  getPrincipalByEntityId(entityId: number, entityType?: 'Client' | 'Fournisseur'): Observable<AdresseResponse> {
    return new Observable(observer => {
      this.getByEntity(entityId, entityType).subscribe({
        next: (response) => {
          if (response.success && response.data) {
            const adressePrincipale = response.data.find(addr => addr.estPrincipale);
            if (adressePrincipale) {
              observer.next({
                success: true,
                data: adressePrincipale,
                message: 'Adresse principale trouvée'
              });
            } else {
              observer.next({
                success: false,
                message: 'Aucune adresse principale trouvée'
              });
            }
          } else {
            observer.next({
              success: false,
              message: response.message || 'Erreur lors de la récupération des adresses'
            });
          }
          observer.complete();
        },
        error: (error) => observer.error(error)
      });
    });
  }

  /**
   * Formater une adresse pour l'affichage
   */
  formatAdresse(adresse: Adresse, includeNom: boolean = true): string {
    const parts: string[] = [];

    if (includeNom) {
      parts.push(`${adresse.prenom} ${adresse.nom}`);
      if (adresse.entreprise) {
        parts.push(adresse.entreprise);
      }
    }

    parts.push(adresse.adresse1);
    if (adresse.adresse2) {
      parts.push(adresse.adresse2);
    }
    parts.push(`${adresse.codePostal} ${adresse.ville}`);
    parts.push(adresse.pays);

    return parts.join('\n');
  }

  /**
   * Valider une adresse
   */
  validateAdresse(adresse: AdresseCreate | AdresseUpdate): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if ('nom' in adresse && (!adresse.nom || adresse.nom.trim().length < 2)) {
      errors.push('Le nom doit contenir au moins 2 caractères');
    }

    if ('prenom' in adresse && (!adresse.prenom || adresse.prenom.trim().length < 2)) {
      errors.push('Le prénom doit contenir au moins 2 caractères');
    }

    if ('adresse1' in adresse && (!adresse.adresse1 || adresse.adresse1.trim().length < 5)) {
      errors.push('L\'adresse doit contenir au moins 5 caractères');
    }

    if ('ville' in adresse && (!adresse.ville || adresse.ville.trim().length < 2)) {
      errors.push('La ville doit contenir au moins 2 caractères');
    }

    if ('codePostal' in adresse && (!adresse.codePostal || !/^\d{4}$/.test(adresse.codePostal))) {
      errors.push('Le code postal doit contenir exactement 4 chiffres');
    }

    if ('pays' in adresse && (!adresse.pays || adresse.pays.trim().length < 2)) {
      errors.push('Le pays doit contenir au moins 2 caractères');
    }

    if ('telephone' in adresse && adresse.telephone && !/^(\+33|0)[1-9](\d{8})$/.test(adresse.telephone.replace(/\s/g, ''))) {
      errors.push('Le numéro de téléphone n\'est pas valide');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
