using System.ComponentModel.DataAnnotations;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs.UpdateDTOs
{
    public class TraiterDemandeDto
    {
        [Required(ErrorMessage = "Le statut est obligatoire")]
        public StatutDemande Statut { get; set; }

        [StringLength(500, ErrorMessage = "Le commentaire ne peut pas dépasser 500 caractères")]
        public string? CommentaireAdmin { get; set; }
    }
}
