﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class FournisseurDto : UtilisateurReadDto
    {
        
        public required string MatriculeFiscale { get; set; }
        public required string RaisonSociale { get; set; }
        public string? Description { get; set; }
        public required string RIBMasque { get; set; }
        [Required]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Le code banque doit contenir 3 chiffres.")]
        [RegularExpression(@"^[0-9]+$", ErrorMessage = "Seuls les chiffres sont autorisés.")]
        public required string CodeBanque { get; set; }
        [Range(0.5, 1, ErrorMessage = "La commission doit être entre 50% et 100%")]
        public decimal Commission { get; set; }
        public int DelaiPreparationJours { get; set; }
        public decimal FraisLivraisonBase { get; set; }
        public required string LogoFile { get; set; } 
    }    
}
