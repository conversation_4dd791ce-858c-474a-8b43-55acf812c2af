﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WebApiPfe.Migrations
{
    /// <inheritdoc />
    public partial class AjoutDemandesCategoriesSousCategories : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CommentaireModeration",
                table: "Avis",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateModeration",
                table: "Avis",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ModerePar",
                table: "Avis",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Statut",
                table: "Avis",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.CreateTable(
                name: "DemandesCategories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FournisseurId = table.Column<int>(type: "int", nullable: false),
                    Statut = table.Column<int>(type: "int", nullable: false),
                    DateDemande = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateTraitement = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AdminTraitantId = table.Column<int>(type: "int", nullable: true),
                    CommentaireAdmin = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CategorieCreeeId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DemandesCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DemandesCategories_AspNetUsers_AdminTraitantId",
                        column: x => x.AdminTraitantId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DemandesCategories_AspNetUsers_FournisseurId",
                        column: x => x.FournisseurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DemandesCategories_Categories_CategorieCreeeId",
                        column: x => x.CategorieCreeeId,
                        principalTable: "Categories",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DemandesSousCategories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CategorieId = table.Column<int>(type: "int", nullable: false),
                    FournisseurId = table.Column<int>(type: "int", nullable: false),
                    Statut = table.Column<int>(type: "int", nullable: false),
                    DateDemande = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateTraitement = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AdminTraitantId = table.Column<int>(type: "int", nullable: true),
                    CommentaireAdmin = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    SousCategorieCreeeId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DemandesSousCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DemandesSousCategories_AspNetUsers_AdminTraitantId",
                        column: x => x.AdminTraitantId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DemandesSousCategories_AspNetUsers_FournisseurId",
                        column: x => x.FournisseurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DemandesSousCategories_Categories_CategorieId",
                        column: x => x.CategorieId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DemandesSousCategories_SousCategories_SousCategorieCreeeId",
                        column: x => x.SousCategorieCreeeId,
                        principalTable: "SousCategories",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PromotionGestions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Nom = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Valeur = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    DateDebut = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateFin = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EstActive = table.Column<bool>(type: "bit", nullable: false),
                    UtilisationsMax = table.Column<int>(type: "int", nullable: true),
                    UtilisationsActuelles = table.Column<int>(type: "int", nullable: false),
                    MontantMinimum = table.Column<decimal>(type: "decimal(10,2)", nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    FournisseurId = table.Column<int>(type: "int", nullable: true),
                    ProduitId = table.Column<int>(type: "int", nullable: true),
                    MontantTotalEconomise = table.Column<decimal>(type: "decimal(15,2)", nullable: false),
                    NombreCommandesImpactees = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PromotionGestions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PromotionGestions_AspNetUsers_FournisseurId",
                        column: x => x.FournisseurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_PromotionGestions_Produits_ProduitId",
                        column: x => x.ProduitId,
                        principalTable: "Produits",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "Reclamations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CommandeId = table.Column<int>(type: "int", nullable: false),
                    ClientId = table.Column<int>(type: "int", nullable: false),
                    Objet = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Statut = table.Column<int>(type: "int", nullable: false),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateTraitement = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateResolution = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ReponseAdmin = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ReponseFournisseur = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    TraitePar = table.Column<int>(type: "int", nullable: true),
                    PiecesJointes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Reclamations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Reclamations_AspNetUsers_ClientId",
                        column: x => x.ClientId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Reclamations_AspNetUsers_TraitePar",
                        column: x => x.TraitePar,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reclamations_Commandes_CommandeId",
                        column: x => x.CommandeId,
                        principalTable: "Commandes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PromotionUtiliseesGestion",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PromotionId = table.Column<int>(type: "int", nullable: false),
                    CommandeId = table.Column<int>(type: "int", nullable: false),
                    CodePromoUtilise = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    MontantReduction = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    DateUtilisation = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PromotionUtiliseesGestion", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PromotionUtiliseesGestion_Commandes_CommandeId",
                        column: x => x.CommandeId,
                        principalTable: "Commandes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PromotionUtiliseesGestion_PromotionGestions_PromotionId",
                        column: x => x.PromotionId,
                        principalTable: "PromotionGestions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "HistoriqueReclamations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ReclamationId = table.Column<int>(type: "int", nullable: false),
                    UtilisateurId = table.Column<int>(type: "int", nullable: false),
                    Action = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Commentaire = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DateAction = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HistoriqueReclamations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HistoriqueReclamations_AspNetUsers_UtilisateurId",
                        column: x => x.UtilisateurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_HistoriqueReclamations_Reclamations_ReclamationId",
                        column: x => x.ReclamationId,
                        principalTable: "Reclamations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Avis_ModerePar",
                table: "Avis",
                column: "ModerePar");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesCategories_AdminTraitantId",
                table: "DemandesCategories",
                column: "AdminTraitantId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesCategories_CategorieCreeeId",
                table: "DemandesCategories",
                column: "CategorieCreeeId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesCategories_FournisseurId",
                table: "DemandesCategories",
                column: "FournisseurId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesSousCategories_AdminTraitantId",
                table: "DemandesSousCategories",
                column: "AdminTraitantId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesSousCategories_CategorieId",
                table: "DemandesSousCategories",
                column: "CategorieId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesSousCategories_FournisseurId",
                table: "DemandesSousCategories",
                column: "FournisseurId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesSousCategories_SousCategorieCreeeId",
                table: "DemandesSousCategories",
                column: "SousCategorieCreeeId");

            migrationBuilder.CreateIndex(
                name: "IX_HistoriqueReclamations_ReclamationId",
                table: "HistoriqueReclamations",
                column: "ReclamationId");

            migrationBuilder.CreateIndex(
                name: "IX_HistoriqueReclamations_UtilisateurId",
                table: "HistoriqueReclamations",
                column: "UtilisateurId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionGestions_Code",
                table: "PromotionGestions",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PromotionGestions_FournisseurId",
                table: "PromotionGestions",
                column: "FournisseurId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionGestions_ProduitId",
                table: "PromotionGestions",
                column: "ProduitId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionUtiliseesGestion_CommandeId",
                table: "PromotionUtiliseesGestion",
                column: "CommandeId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionUtiliseesGestion_PromotionId",
                table: "PromotionUtiliseesGestion",
                column: "PromotionId");

            migrationBuilder.CreateIndex(
                name: "IX_Reclamations_ClientId",
                table: "Reclamations",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_Reclamations_CommandeId",
                table: "Reclamations",
                column: "CommandeId");

            migrationBuilder.CreateIndex(
                name: "IX_Reclamations_TraitePar",
                table: "Reclamations",
                column: "TraitePar");

            migrationBuilder.AddForeignKey(
                name: "FK_Avis_AspNetUsers_ModerePar",
                table: "Avis",
                column: "ModerePar",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Avis_AspNetUsers_ModerePar",
                table: "Avis");

            migrationBuilder.DropTable(
                name: "DemandesCategories");

            migrationBuilder.DropTable(
                name: "DemandesSousCategories");

            migrationBuilder.DropTable(
                name: "HistoriqueReclamations");

            migrationBuilder.DropTable(
                name: "PromotionUtiliseesGestion");

            migrationBuilder.DropTable(
                name: "Reclamations");

            migrationBuilder.DropTable(
                name: "PromotionGestions");

            migrationBuilder.DropIndex(
                name: "IX_Avis_ModerePar",
                table: "Avis");

            migrationBuilder.DropColumn(
                name: "CommentaireModeration",
                table: "Avis");

            migrationBuilder.DropColumn(
                name: "DateModeration",
                table: "Avis");

            migrationBuilder.DropColumn(
                name: "ModerePar",
                table: "Avis");

            migrationBuilder.DropColumn(
                name: "Statut",
                table: "Avis");
        }
    }
}
