# Test de connexion
$uri = "http://localhost:5014/api/Auth/login"

# Tester avec différents comptes
$testAccounts = @(
    @{ email = "<EMAIL>"; password = "Admin123!" },
    @{ email = "<EMAIL>"; password = "Admin123!" },
    @{ email = "<EMAIL>"; password = "Test123!" }
)

foreach ($account in $testAccounts) {
    Write-Host "Test de connexion avec: $($account.email)"
    
    $body = @{
        email = $account.email
        password = $account.password
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri $uri -Method POST -ContentType "application/json" -Body $body
        Write-Host "Connexion reussie pour $($account.email)!"
        Write-Host "Token: $($response.token)"
        Write-Host "Utilisateur: $($response.utilisateur | ConvertTo-Json)"
        break
    } catch {
        Write-Host "Echec de connexion pour $($account.email): $($_.Exception.Message)"
    }
    
    Write-Host "---"
}
