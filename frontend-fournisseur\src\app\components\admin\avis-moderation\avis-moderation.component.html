<div class="avis-moderation-container">
  <div class="header-section">
    <h2><i class="bi bi-chat-square-text"></i> Modération des Avis</h2>
    <p class="text-muted"><PERSON><PERSON><PERSON> et modérez les avis clients sur les produits</p>
  </div>

  <!-- Filtres -->
  <div class="filters-section card">
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-3">
          <label class="form-label">Statut</label>
          <select class="form-select" [(ngModel)]="filter.statut" (change)="onFilterChange()">
            <option [value]="undefined">Tous les statuts</option>
            <option [value]="StatutAvis.EnAttente">En attente</option>
            <option [value]="StatutAvis.Approuve">Approuvé</option>
            <option [value]="StatutAvis.Rejete">Rejeté</option>
            <option [value]="StatutAvis.Signale">Signalé</option>
          </select>
        </div>
        
        <div class="col-md-3">
          <label class="form-label">Recherche</label>
          <input type="text" class="form-control" placeholder="Produit, client, commentaire..." 
                 [(ngModel)]="filter.recherche" (input)="onFilterChange()">
        </div>
        
        <div class="col-md-2">
          <label class="form-label">Date début</label>
          <input type="date" class="form-control" [(ngModel)]="filter.dateDebut" (change)="onFilterChange()">
        </div>
        
        <div class="col-md-2">
          <label class="form-label">Date fin</label>
          <input type="date" class="form-control" [(ngModel)]="filter.dateFin" (change)="onFilterChange()">
        </div>
        
        <div class="col-md-2">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="resetFilters()">
              <i class="bi bi-arrow-clockwise"></i>
            </button>
            <button class="btn btn-outline-primary" (click)="exportAvis()">
              <i class="bi bi-download"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger">
    <i class="bi bi-exclamation-triangle"></i> {{ error }}
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
  </div>

  <!-- Liste des avis -->
  <div *ngIf="!loading" class="avis-list">
    <div class="card">
      <div class="card-header">
        <div class="row align-items-center">
          <div class="col">
            <h5 class="mb-0">Avis à modérer ({{ avis.length }})</h5>
          </div>
          <div class="col-auto">
            <div class="btn-group btn-group-sm">
              <button class="btn btn-outline-secondary" 
                      [class.active]="filter.sortBy === 'datePublication'"
                      (click)="onSortChange('datePublication')">
                Date
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'datePublication' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'datePublication' && filter.sortDesc"></i>
              </button>
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'note'"
                      (click)="onSortChange('note')">
                Note
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'note' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'note' && filter.sortDesc"></i>
              </button>
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'statut'"
                      (click)="onSortChange('statut')">
                Statut
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'statut' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'statut' && filter.sortDesc"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="card-body p-0">
        <div *ngIf="avis.length === 0" class="text-center py-5 text-muted">
          <i class="bi bi-chat-square-text fs-1"></i>
          <p class="mt-2">Aucun avis trouvé</p>
        </div>
        
        <div *ngFor="let avisItem of avis" class="avis-item border-bottom">
          <div class="row g-3 p-3">
            <!-- Informations produit et client -->
            <div class="col-md-4">
              <div class="product-info">
                <h6 class="mb-1">{{ avisItem.produitNom }}</h6>
                <small class="text-muted">Réf: {{ avisItem.produitReference }}</small>
                <div class="mt-1">
                  <small class="text-muted">
                    <i class="bi bi-building"></i> {{ avisItem.fournisseurRaisonSociale }}
                  </small>
                </div>
              </div>
              <div class="client-info mt-2">
                <small class="text-muted">
                  <i class="bi bi-person"></i> {{ avisItem.clientPrenom }} {{ avisItem.clientNom }}
                </small>
              </div>
            </div>
            
            <!-- Avis -->
            <div class="col-md-5">
              <div class="rating mb-2">
                <span *ngFor="let star of getStars(avisItem.note)" 
                      class="bi" [class]="'bi-' + star" style="color: #ffc107;"></span>
                <span class="ms-2 text-muted">{{ avisItem.note }}/5</span>
              </div>
              <p class="mb-2" [title]="avisItem.commentaire">
                {{ (avisItem.commentaire || '').length > 100 ? 
                   (avisItem.commentaire || '').substring(0, 100) + '...' : 
                   (avisItem.commentaire || 'Aucun commentaire') }}
              </p>
              <small class="text-muted">
                <i class="bi bi-calendar"></i> {{ avisItem.datePublication | date:'dd/MM/yyyy HH:mm' }}
              </small>
            </div>
            
            <!-- Statut et actions -->
            <div class="col-md-3 text-end">
              <div class="mb-2">
                <span class="badge" [class]="'bg-' + getStatutColor(avisItem.statut)">
                  <i class="bi" [class]="'bi-' + getStatutIcon(avisItem.statut)"></i>
                  {{ getStatutLibelle(avisItem.statut) }}
                </span>
              </div>
              
              <div *ngIf="avisItem.dateModeration" class="mb-2">
                <small class="text-muted">
                  Modéré le {{ avisItem.dateModeration | date:'dd/MM/yyyy' }}
                  <span *ngIf="avisItem.nomModerateur">par {{ avisItem.nomModerateur }}</span>
                </small>
              </div>
              
              <button class="btn btn-sm btn-outline-primary" (click)="openModerationModal(avisItem)">
                <i class="bi bi-pencil"></i> Modérer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="avis.length > 0" class="pagination-section mt-3">
    <nav>
      <ul class="pagination justify-content-center">
        <li class="page-item" [class.disabled]="filter.page === 1">
          <button class="page-link" (click)="previousPage()">Précédent</button>
        </li>
        <li class="page-item active">
          <span class="page-link">{{ filter.page }}</span>
        </li>
        <li class="page-item">
          <button class="page-link" (click)="nextPage()">Suivant</button>
        </li>
      </ul>
    </nav>
  </div>
</div>

<!-- Modal de modération -->
<!-- Modal de modération -->
<div *ngIf="selectedAvis" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-chat-square-text"></i> Modérer l'avis
        </h5>
        <button type="button" class="btn-close" (click)="closeModerationModal()"></button>
      </div>

      <div class="modal-body">
        <!-- Détails de l'avis -->
        <div class="avis-details mb-4">
          <div class="row">
            <div class="col-md-6">
              <h6>Produit</h6>
              <p>{{ selectedAvis.produitNom }}</p>
              <small class="text-muted">Réf: {{ selectedAvis.produitReference }}</small>
            </div>
            <div class="col-md-6">
              <h6>Client</h6>
              <p>{{ selectedAvis.clientPrenom }} {{ selectedAvis.clientNom }}</p>
              <small class="text-muted">{{ selectedAvis.clientEmail }}</small>
            </div>
          </div>

          <div class="mt-3">
            <h6>Avis</h6>
            <div class="rating mb-2">
              <span *ngFor="let star of getStars(selectedAvis.note)"
                    class="bi" [class]="'bi-' + star" style="color: #ffc107;"></span>
              <span class="ms-2">{{ selectedAvis.note }}/5</span>
            </div>
            <p class="border p-3 rounded bg-light">{{ selectedAvis.commentaire || 'Aucun commentaire' }}</p>
          </div>
        </div>

        <!-- Formulaire de modération -->
        <form (ngSubmit)="modererAvis()">
          <div class="mb-3">
            <label class="form-label">Statut</label>
            <select class="form-select" [(ngModel)]="moderationForm.statut" name="statut" required>
              <option [value]="StatutAvis.EnAttente">En attente</option>
              <option [value]="StatutAvis.Approuve">Approuver</option>
              <option [value]="StatutAvis.Rejete">Rejeter</option>
              <option [value]="StatutAvis.Signale">Signaler</option>
            </select>
          </div>

          <div class="mb-3">
            <label class="form-label">Commentaire de modération (optionnel)</label>
            <textarea class="form-control" rows="3"
                      [(ngModel)]="moderationForm.commentaireModeration"
                      name="commentaireModeration"
                      placeholder="Raison du rejet, commentaire interne..."></textarea>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeModerationModal()">
          Annuler
        </button>
        <button type="button" class="btn btn-primary" (click)="modererAvis()">
          <i class="bi bi-check"></i> Modérer
        </button>
      </div>
    </div>
  </div>
</div>
