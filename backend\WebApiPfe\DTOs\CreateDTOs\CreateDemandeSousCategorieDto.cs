using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class CreateDemandeSousCategorieDto
    {
        [Required(ErrorMessage = "Le nom de la sous-catégorie est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom ne peut pas dépasser 100 caractères")]
        public string Nom { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "La description ne peut pas dépasser 500 caractères")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "L'ID de la catégorie est obligatoire")]
        public int CategorieId { get; set; }
    }
}
