import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DemandeService, DemandeCategorieDto, DemandeSousCategorieDto, StatutDemande, TraiterDemandeCategorieDto, TraiterDemandeSousCategorieDto } from '../../../services/demande.service';

@Component({
  selector: 'app-demandes-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="demandes-management">
      <div class="page-header">
        <h1>🔔 Gestion des Demandes</h1>
        <p>Gérez les demandes de création de catégories et sous-catégories</p>
      </div>

      <div class="tabs">
        <button 
          class="tab-btn" 
          [class.active]="activeTab === 'categories'"
          (click)="activeTab = 'categories'; loadDemandesCategories()">
          Demandes de Catégories
          <span *ngIf="demandesCategories.length > 0" class="badge">{{ demandesCategories.length }}</span>
        </button>
        <button 
          class="tab-btn" 
          [class.active]="activeTab === 'sous-categories'"
          (click)="activeTab = 'sous-categories'; loadDemandesSousCategories()">
          Demandes de Sous-catégories
          <span *ngIf="demandesSousCategories.length > 0" class="badge">{{ demandesSousCategories.length }}</span>
        </button>
      </div>

      <!-- Demandes de catégories -->
      <div *ngIf="activeTab === 'categories'" class="tab-content">
        <div class="filters">
          <select [(ngModel)]="filtreStatutCategories" (change)="loadDemandesCategories()">
            <option value="">Tous les statuts</option>
            <option value="0">En attente</option>
            <option value="1">Approuvées</option>
            <option value="2">Rejetées</option>
          </select>
        </div>

        <div *ngIf="demandesCategories.length === 0" class="empty-state">
          <p>Aucune demande de catégorie trouvée</p>
        </div>

        <div class="demandes-grid">
          <div *ngFor="let demande of demandesCategories" class="demande-card">
            <div class="demande-header">
              <h3>{{ demande.nom }}</h3>
              <span class="statut-badge" [style.background-color]="getStatutColor(demande.statut)">
                {{ getStatutLabel(demande.statut) }}
              </span>
            </div>
            
            <div class="demande-content">
              <p><strong>Description:</strong> {{ demande.description }}</p>
              <p><strong>Demandé par:</strong> {{ demande.fournisseurNom }}</p>
              <p><strong>Date de demande:</strong> {{ formatDate(demande.dateDemande) }}</p>
              
              <div *ngIf="demande.imageUrl" class="image-preview">
                <img [src]="demande.imageUrl" alt="Image de la catégorie" />
              </div>
            </div>

            <div *ngIf="demande.statut === 0" class="demande-actions">
              <button class="btn btn-success" (click)="traiterDemandeCategorie(demande.id, 1)">
                ✅ Approuver
              </button>
              <button class="btn btn-danger" (click)="openRejectModal(demande, 'categorie')">
                ❌ Rejeter
              </button>
            </div>

            <div *ngIf="demande.statut !== 0 && demande.commentaireAdmin" class="admin-comment">
              <p><strong>Commentaire admin:</strong> {{ demande.commentaireAdmin }}</p>
              <p><strong>Traité le:</strong> {{ formatDate(demande.dateTraitement!) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Demandes de sous-catégories -->
      <div *ngIf="activeTab === 'sous-categories'" class="tab-content">
        <div class="filters">
          <select [(ngModel)]="filtreStatutSousCategories" (change)="loadDemandesSousCategories()">
            <option value="">Tous les statuts</option>
            <option value="0">En attente</option>
            <option value="1">Approuvées</option>
            <option value="2">Rejetées</option>
          </select>
        </div>

        <div *ngIf="demandesSousCategories.length === 0" class="empty-state">
          <p>Aucune demande de sous-catégorie trouvée</p>
        </div>

        <div class="demandes-grid">
          <div *ngFor="let demande of demandesSousCategories" class="demande-card">
            <div class="demande-header">
              <h3>{{ demande.nom }}</h3>
              <span class="statut-badge" [style.background-color]="getStatutColor(demande.statut)">
                {{ getStatutLabel(demande.statut) }}
              </span>
            </div>
            
            <div class="demande-content">
              <p><strong>Description:</strong> {{ demande.description }}</p>
              <p><strong>Catégorie parent:</strong> {{ demande.categorieNom }}</p>
              <p><strong>Demandé par:</strong> {{ demande.fournisseurNom }}</p>
              <p><strong>Date de demande:</strong> {{ formatDate(demande.dateDemande) }}</p>
              
              <div *ngIf="demande.imageUrl" class="image-preview">
                <img [src]="demande.imageUrl" alt="Image de la sous-catégorie" />
              </div>
            </div>

            <div *ngIf="demande.statut === 0" class="demande-actions">
              <button class="btn btn-success" (click)="traiterDemandeSousCategorie(demande.id, 1)">
                ✅ Approuver
              </button>
              <button class="btn btn-danger" (click)="openRejectModal(demande, 'sous-categorie')">
                ❌ Rejeter
              </button>
            </div>

            <div *ngIf="demande.statut !== 0 && demande.commentaireAdmin" class="admin-comment">
              <p><strong>Commentaire admin:</strong> {{ demande.commentaireAdmin }}</p>
              <p><strong>Traité le:</strong> {{ formatDate(demande.dateTraitement!) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal de rejet -->
      <div *ngIf="showRejectModal" class="modal-overlay" (click)="closeRejectModal()">
        <div class="modal" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h3>Rejeter la demande</h3>
            <button class="close-btn" (click)="closeRejectModal()">×</button>
          </div>
          <div class="modal-content">
            <p>Êtes-vous sûr de vouloir rejeter cette demande ?</p>
            <textarea 
              [(ngModel)]="commentaireRejet" 
              placeholder="Commentaire (optionnel)"
              rows="3">
            </textarea>
          </div>
          <div class="modal-actions">
            <button class="btn btn-secondary" (click)="closeRejectModal()">Annuler</button>
            <button class="btn btn-danger" (click)="confirmReject()">Rejeter</button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .demandes-management {
      padding: 2rem;
    }

    .page-header {
      margin-bottom: 2rem;
    }

    .page-header h1 {
      margin: 0 0 0.5rem 0;
      color: #1e293b;
    }

    .page-header p {
      margin: 0;
      color: #64748b;
    }

    .tabs {
      display: flex;
      border-bottom: 2px solid #e2e8f0;
      margin-bottom: 2rem;
    }

    .tab-btn {
      padding: 1rem 2rem;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 1rem;
      color: #64748b;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
      position: relative;
    }

    .tab-btn.active {
      color: #3b82f6;
      border-bottom-color: #3b82f6;
    }

    .badge {
      background: #ef4444;
      color: white;
      border-radius: 50%;
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      margin-left: 0.5rem;
    }

    .filters {
      margin-bottom: 1.5rem;
    }

    .filters select {
      padding: 0.5rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      background: white;
    }

    .empty-state {
      text-align: center;
      padding: 3rem;
      color: #64748b;
    }

    .demandes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 1.5rem;
    }

    .demande-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
    }

    .demande-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .demande-header h3 {
      margin: 0;
      color: #1e293b;
    }

    .statut-badge {
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      color: white;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .demande-content p {
      margin: 0.5rem 0;
      color: #475569;
    }

    .image-preview {
      margin: 1rem 0;
    }

    .image-preview img {
      max-width: 100px;
      max-height: 100px;
      border-radius: 8px;
      object-fit: cover;
    }

    .demande-actions {
      display: flex;
      gap: 0.5rem;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #e2e8f0;
    }

    .admin-comment {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #e2e8f0;
      background: #f8fafc;
      padding: 1rem;
      border-radius: 8px;
    }

    .btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: all 0.3s ease;
    }

    .btn-success {
      background: #10b981;
      color: white;
    }

    .btn-success:hover {
      background: #059669;
    }

    .btn-danger {
      background: #ef4444;
      color: white;
    }

    .btn-danger:hover {
      background: #dc2626;
    }

    .btn-secondary {
      background: #6b7280;
      color: white;
    }

    .btn-secondary:hover {
      background: #4b5563;
    }

    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      border-bottom: 1px solid #e2e8f0;
    }

    .modal-header h3 {
      margin: 0;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #6b7280;
    }

    .modal-content {
      padding: 1.5rem;
    }

    .modal-content textarea {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      margin-top: 1rem;
      resize: vertical;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;
      padding: 1.5rem;
      border-top: 1px solid #e2e8f0;
    }
  `]
})
export class DemandesManagementComponent implements OnInit {
  activeTab: 'categories' | 'sous-categories' = 'categories';
  demandesCategories: DemandeCategorieDto[] = [];
  demandesSousCategories: DemandeSousCategorieDto[] = [];
  filtreStatutCategories = '';
  filtreStatutSousCategories = '';
  
  showRejectModal = false;
  currentDemande: any = null;
  currentType: 'categorie' | 'sous-categorie' = 'categorie';
  commentaireRejet = '';

  constructor(private demandeService: DemandeService) {}

  ngOnInit() {
    this.loadDemandesCategories();
  }

  loadDemandesCategories() {
    if (this.filtreStatutCategories) {
      const statut = parseInt(this.filtreStatutCategories) as StatutDemande;
      this.demandeService.getDemandesCategoriesByStatut(statut).subscribe(
        demandes => this.demandesCategories = demandes
      );
    } else {
      this.demandeService.getAllDemandesCategories().subscribe(
        demandes => this.demandesCategories = demandes
      );
    }
  }

  loadDemandesSousCategories() {
    if (this.filtreStatutSousCategories) {
      const statut = parseInt(this.filtreStatutSousCategories) as StatutDemande;
      this.demandeService.getDemandesSousCategoriesByStatut(statut).subscribe(
        demandes => this.demandesSousCategories = demandes
      );
    } else {
      this.demandeService.getAllDemandesSousCategories().subscribe(
        demandes => this.demandesSousCategories = demandes
      );
    }
  }

  traiterDemandeCategorie(id: number, statut: StatutDemande) {
    const traitement: TraiterDemandeCategorieDto = { statut };
    this.demandeService.traiterDemandeCategorie(id, traitement).subscribe(() => {
      this.loadDemandesCategories();
    });
  }

  traiterDemandeSousCategorie(id: number, statut: StatutDemande) {
    const traitement: TraiterDemandeSousCategorieDto = { statut };
    this.demandeService.traiterDemandeSousCategorie(id, traitement).subscribe(() => {
      this.loadDemandesSousCategories();
    });
  }

  openRejectModal(demande: any, type: 'categorie' | 'sous-categorie') {
    this.currentDemande = demande;
    this.currentType = type;
    this.commentaireRejet = '';
    this.showRejectModal = true;
  }

  closeRejectModal() {
    this.showRejectModal = false;
    this.currentDemande = null;
    this.commentaireRejet = '';
  }

  confirmReject() {
    if (this.currentDemande) {
      const traitement = {
        statut: StatutDemande.Rejetee,
        commentaireAdmin: this.commentaireRejet || undefined
      };

      if (this.currentType === 'categorie') {
        this.demandeService.traiterDemandeCategorie(this.currentDemande.id, traitement).subscribe(() => {
          this.loadDemandesCategories();
          this.closeRejectModal();
        });
      } else {
        this.demandeService.traiterDemandeSousCategorie(this.currentDemande.id, traitement).subscribe(() => {
          this.loadDemandesSousCategories();
          this.closeRejectModal();
        });
      }
    }
  }

  getStatutLabel(statut: StatutDemande): string {
    return this.demandeService.getStatutLabel(statut);
  }

  getStatutColor(statut: StatutDemande): string {
    return this.demandeService.getStatutColor(statut);
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
