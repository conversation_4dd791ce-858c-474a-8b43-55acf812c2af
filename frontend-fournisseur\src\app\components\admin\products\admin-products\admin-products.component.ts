import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ProduitService } from '../../../../services/produit.service';
import { AdminService } from '../../../../services/admin.service';

interface ProduitAdmin {
  id: number;
  nom: string;
  reference: string;
  prix: number;
  stock: number;
  fournisseurNom: string;
  categorieNom: string;
  sousCategorieNom: string;
  estValide: boolean;
  estEnAvant: boolean;
  dateCreation: string;
  nombreVues: number;
  nombreVentes: number;
  images: string[];
}

@Component({
  selector: 'app-admin-products',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './admin-products.component.html',
  styleUrls: ['./admin-products.component.css']
})
export class AdminProductsComponent implements OnInit {
  // Angular 19: Signals
  produits = signal<ProduitAdmin[]>([]);
  isLoading = signal(false);
  error = signal('');
  searchTerm = signal('');
  selectedCategory = signal('');
  selectedStatus = signal('');
  currentPage = signal(1);
  pageSize = signal(10);
  totalItems = signal(0);

  // Computed signals
  filteredProduits = computed(() => {
    const products = this.produits();
    const search = this.searchTerm().toLowerCase();
    const category = this.selectedCategory();
    const status = this.selectedStatus();

    return products.filter(product => {
      const matchesSearch = !search ||
        product.nom.toLowerCase().includes(search) ||
        product.reference.toLowerCase().includes(search) ||
        product.fournisseurNom.toLowerCase().includes(search);

      const matchesCategory = !category || product.categorieNom === category;
      const matchesStatus = !status ||
        (status === 'valide' && product.estValide) ||
        (status === 'invalide' && !product.estValide) ||
        (status === 'enavant' && product.estEnAvant);

      return matchesSearch && matchesCategory && matchesStatus;
    });
  });

  totalPages = computed(() => Math.ceil(this.totalItems() / this.pageSize()));

  constructor(
    private produitService: ProduitService,
    private adminService: AdminService
  ) {}

  ngOnInit(): void {
    this.loadProduits();
  }

  loadProduits(): void {
    this.isLoading.set(true);
    this.error.set('');

    this.produitService.getAll().subscribe({
      next: (produits) => {
        console.log('✅ Produits reçus:', produits);

        // Transformer les données pour l'interface admin
        const produitsAdmin: ProduitAdmin[] = produits.map(prod => ({
          id: prod.id,
          nom: prod.nom,
          reference: prod.referenceOriginal,
          prix: prod.prixVenteTTC,
          stock: prod.stock,
          fournisseurNom: prod.fournisseur?.raisonSociale || 'Non défini',
          categorieNom: 'Non défini', // À récupérer via une autre requête si nécessaire
          sousCategorieNom: prod.sousCategorie?.nom || 'Non défini',
          estValide: true, // Par défaut validé (à ajuster selon la logique métier)
          estEnAvant: false, // Par défaut pas en avant (à ajuster selon la logique métier)
          dateCreation: prod.dateAjout?.toString() || new Date().toISOString(),
          nombreVues: 0, // À récupérer si disponible
          nombreVentes: 0, // À récupérer si disponible
          images: prod.images?.map(img => img.imageUrl) || []
        }));

        this.produits.set(produitsAdmin);
        this.totalItems.set(produitsAdmin.length);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des produits:', error);
        this.error.set('Erreur lors du chargement des produits');
        this.isLoading.set(false);
      }
    });
  }

  // Actions admin
  toggleValidation(produit: ProduitAdmin): void {
    // Logique de validation/invalidation
    console.log('Toggle validation pour produit:', produit.id);
  }

  toggleEnAvant(produit: ProduitAdmin): void {
    this.adminService.mettreEnAvantProduit(produit.id).subscribe({
      next: () => {
        // Mettre à jour localement
        const produits = this.produits();
        const index = produits.findIndex(p => p.id === produit.id);
        if (index !== -1) {
          produits[index] = { ...produits[index], estEnAvant: !produits[index].estEnAvant };
          this.produits.set([...produits]);
        }
        console.log('✅ Produit mis en avant avec succès');
      },
      error: (error) => {
        console.error('❌ Erreur lors de la mise en avant:', error);
      }
    });
  }

  deleteProduit(produit: ProduitAdmin): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le produit "${produit.nom}" ?\n\nCette action est irréversible.`)) {
      // Logique de suppression
      console.log('Suppression du produit:', produit.id);
    }
  }

  refresh(): void {
    this.loadProduits();
  }

  // Utilitaires
  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(price);
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  getStatusClass(produit: ProduitAdmin): string {
    if (produit.estEnAvant) return 'status-featured';
    if (produit.estValide) return 'status-valid';
    return 'status-invalid';
  }

  getStatusText(produit: ProduitAdmin): string {
    if (produit.estEnAvant) return 'En avant';
    if (produit.estValide) return 'Validé';
    return 'En attente';
  }
}
