using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace WebApiPfe.Models.Entity
{
    public enum TypePromotionGestion
    {
        Pourcentage = 1,
        MontantFixe = 2,
        Outlet = 3
    }

    public class PromotionGestion
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Nom { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        public TypePromotionGestion Type { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Valeur { get; set; }

        [Required]
        public DateTime DateDebut { get; set; }

        [Required]
        public DateTime DateFin { get; set; }

        [Required]
        public bool EstActive { get; set; } = true;

        public int? UtilisationsMax { get; set; }

        public int UtilisationsActuelles { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal? MontantMinimum { get; set; }

        [Required]
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;

        // Relations
        public int? FournisseurId { get; set; }
        [ForeignKey(nameof(FournisseurId))]
        [JsonIgnore]
        public virtual Fournisseur? Fournisseur { get; set; }

        public int? ProduitId { get; set; }
        [ForeignKey(nameof(ProduitId))]
        [JsonIgnore]
        public virtual Produit? Produit { get; set; }

        // Statistiques
        [Column(TypeName = "decimal(15,2)")]
        public decimal MontantTotalEconomise { get; set; } = 0;

        public int NombreCommandesImpactees { get; set; } = 0;

        // Navigation properties
        [JsonIgnore]
        public virtual ICollection<PromotionUtiliseeGestion> PromotionsUtilisees { get; set; } = new List<PromotionUtiliseeGestion>();

        // Méthodes utilitaires
        public bool EstValide()
        {
            var now = DateTime.UtcNow;
            return EstActive && DateDebut <= now && DateFin >= now;
        }

        public bool EstExpiree()
        {
            return DateTime.UtcNow > DateFin;
        }

        public bool EstEnAttente()
        {
            return DateTime.UtcNow < DateDebut;
        }

        public bool PeutEtreUtilisee()
        {
            if (!EstValide()) return false;
            if (UtilisationsMax.HasValue && UtilisationsActuelles >= UtilisationsMax.Value) return false;
            return true;
        }

        public decimal CalculerReduction(decimal montantCommande)
        {
            if (!PeutEtreUtilisee()) return 0;
            if (MontantMinimum.HasValue && montantCommande < MontantMinimum.Value) return 0;

            return Type switch
            {
                TypePromotionGestion.Pourcentage => montantCommande * (Valeur / 100),
                TypePromotionGestion.MontantFixe => Math.Min(Valeur, montantCommande),
                TypePromotionGestion.Outlet => montantCommande * (Valeur / 100),
                _ => 0
            };
        }

        public void IncrementUtilisation(decimal montantReduction)
        {
            UtilisationsActuelles++;
            MontantTotalEconomise += montantReduction;
            NombreCommandesImpactees++;
        }
    }

    public class PromotionUtiliseeGestion
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int PromotionId { get; set; }
        [ForeignKey(nameof(PromotionId))]
        [JsonIgnore]
        public virtual PromotionGestion Promotion { get; set; } = null!;

        [Required]
        public int CommandeId { get; set; }
        [ForeignKey(nameof(CommandeId))]
        [JsonIgnore]
        public virtual Commande Commande { get; set; } = null!;

        [Required]
        [StringLength(100)]
        public string CodePromoUtilise { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        public decimal MontantReduction { get; set; }

        [Required]
        public DateTime DateUtilisation { get; set; } = DateTime.UtcNow;
    }
}
