@import '../../../../styles.scss';

.promotions-admin-container {
  padding: 1.5rem;
  
  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    
    h2 {
      color: $primary-color;
      font-weight: 600;
      margin-bottom: 0.5rem;
      
      i {
        margin-right: 0.5rem;
      }
    }
    
    .btn-primary {
      background-color: $primary-color;
      border-color: $primary-color;
      
      &:hover {
        background-color: darken($primary-color, 10%);
        border-color: darken($primary-color, 10%);
      }
    }
  }
  
  .stats-section {
    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border: 1px solid #e0e0e0;
      height: 100%;
      display: flex;
      align-items: center;
      
      .stat-icon {
        font-size: 2rem;
        color: $primary-color;
        margin-right: 1rem;
        
        &.text-success {
          color: #28a745 !important;
        }
        
        &.text-warning {
          color: #ffc107 !important;
        }
        
        &.text-danger {
          color: #dc3545 !important;
        }
      }
      
      .stat-content {
        h3 {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 0.25rem;
          color: $text-color;
        }
        
        p {
          margin: 0;
          color: #6c757d;
          font-size: 0.9rem;
        }
      }
    }
    
    .usage-stats {
      .usage-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e0e0e0;
        text-align: center;
        
        h5 {
          color: $text-color;
          font-weight: 600;
          margin-bottom: 1rem;
          
          i {
            margin-right: 0.5rem;
            color: $primary-color;
          }
        }
        
        .usage-number {
          font-size: 2rem;
          font-weight: 700;
          color: $primary-color;
          margin-bottom: 0.5rem;
        }
      }
    }
  }
  
  .alert-warning {
    border: none;
    border-radius: 8px;
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    
    h6 {
      color: #856404;
      font-weight: 600;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .tabs-section {
    margin-bottom: 1.5rem;
    
    .nav-tabs {
      border-bottom: 2px solid #e0e0e0;
      
      .nav-item {
        .nav-link {
          border: none;
          color: #6c757d;
          font-weight: 500;
          padding: 1rem 1.5rem;
          border-radius: 0;
          border-bottom: 3px solid transparent;
          
          &:hover {
            border-color: transparent;
            color: $primary-color;
          }
          
          &.active {
            color: $primary-color;
            background-color: transparent;
            border-bottom-color: $primary-color;
          }
          
          i {
            margin-right: 0.5rem;
          }
        }
      }
    }
  }
  
  .tab-content {
    .tab-pane {
      .filters-section {
        margin-bottom: 1.5rem;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        
        .card-body {
          padding: 1.5rem;
        }
        
        .form-label {
          font-weight: 500;
          color: $text-color;
          margin-bottom: 0.5rem;
        }
        
        .form-select,
        .form-control {
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          
          &:focus {
            border-color: $primary-color;
            box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
          }
        }
      }
      
      .promotions-list {
        .card {
          border: none;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          border-radius: 8px;
          
          .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            padding: 1rem 1.5rem;
            
            h5 {
              color: $text-color;
              font-weight: 600;
            }
            
            .btn-group {
              .btn {
                border-color: #e0e0e0;
                
                &.active {
                  background-color: $primary-color;
                  border-color: $primary-color;
                  color: white;
                }
                
                &:hover:not(.active) {
                  background-color: #f8f9fa;
                }
              }
            }
          }
        }
        
        .promotion-item {
          transition: background-color 0.2s ease;
          
          &:hover {
            background-color: #f8f9fa;
          }
          
          &:last-child {
            border-bottom: none !important;
          }
          
          .promotion-info {
            h6 {
              color: $text-color;
              font-weight: 600;
              margin-bottom: 0.25rem;
            }
            
            .badge {
              font-size: 0.75rem;
              padding: 0.4rem 0.6rem;
              
              i {
                margin-right: 0.25rem;
              }
            }
            
            .promotion-details {
              margin-top: 0.5rem;
              
              small {
                margin-bottom: 0.25rem;
                
                i {
                  margin-right: 0.25rem;
                }
              }
            }
          }
          
          .promotion-value {
            .value-display {
              .value-number {
                font-size: 2rem;
                font-weight: 700;
                color: $primary-color;
              }
            }
            
            .usage-info {
              .progress {
                height: 6px;
                background-color: #f0f0f0;
                
                .progress-bar {
                  transition: width 0.3s ease;
                }
              }
            }
          }
          
          .promotion-stats {
            small {
              margin-bottom: 0.25rem;
            }
          }
          
          .btn-group-vertical {
            .btn {
              border-radius: 4px;
              margin-bottom: 0.25rem;
              font-size: 0.875rem;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              &.btn-outline-info {
                border-color: #17a2b8;
                color: #17a2b8;
                
                &:hover {
                  background-color: #17a2b8;
                  border-color: #17a2b8;
                }
              }
              
              &.btn-outline-primary {
                border-color: $primary-color;
                color: $primary-color;
                
                &:hover {
                  background-color: $primary-color;
                  border-color: $primary-color;
                }
              }
              
              &.btn-outline-warning {
                border-color: #ffc107;
                color: #ffc107;
                
                &:hover {
                  background-color: #ffc107;
                  border-color: #ffc107;
                  color: #000;
                }
              }
              
              &.btn-outline-success {
                border-color: #28a745;
                color: #28a745;
                
                &:hover {
                  background-color: #28a745;
                  border-color: #28a745;
                }
              }
              
              &.btn-outline-danger {
                border-color: #dc3545;
                color: #dc3545;
                
                &:hover {
                  background-color: #dc3545;
                  border-color: #dc3545;
                }
              }
            }
          }
        }
      }
      
      // Onglet statistiques
      .type-stat {
        .progress {
          height: 8px;
          background-color: #f0f0f0;
          
          .progress-bar {
            transition: width 0.3s ease;
          }
        }
      }
      
      .popular-item {
        .rank-badge {
          background-color: $primary-color;
          color: white;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          font-size: 0.8rem;
          font-weight: 600;
        }
      }
      
      .table {
        th {
          border-top: none;
          font-weight: 600;
          color: $text-color;
        }
        
        td {
          vertical-align: middle;
        }
        
        code {
          background-color: #f8f9fa;
          color: $primary-color;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
        }
      }
    }
  }
  
  .pagination-section {
    .pagination {
      .page-link {
        color: $primary-color;
        border-color: #e0e0e0;
        
        &:hover {
          background-color: #f8f9fa;
          border-color: $primary-color;
        }
      }
      
      .page-item.active .page-link {
        background-color: $primary-color;
        border-color: $primary-color;
      }
      
      .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #e0e0e0;
      }
    }
  }
  
  .alert {
    border: none;
    border-radius: 6px;
    
    &.alert-danger {
      background-color: #f8d7da;
      color: #721c24;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

// Modal styles
.modal {
  .modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  }
  
  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    
    .modal-title {
      color: $text-color;
      font-weight: 600;
      
      i {
        margin-right: 0.5rem;
        color: $primary-color;
      }
    }
  }
  
  .modal-body {
    padding: 1.5rem;
    
    .form-label {
      font-weight: 500;
      color: $text-color;
    }
    
    .form-control,
    .form-select {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
    
    .form-text {
      color: #6c757d;
      font-size: 0.875rem;
    }
    
    .text-danger {
      font-size: 0.875rem;
    }
    
    .input-group-text {
      background-color: #f8f9fa;
      border-color: #e0e0e0;
    }
    
    .form-check-input {
      &:checked {
        background-color: $primary-color;
        border-color: $primary-color;
      }
      
      &:focus {
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
    
    .table {
      th, td {
        padding: 0.5rem;
        border-top: 1px solid #e0e0e0;
      }
      
      th {
        font-weight: 600;
        color: $text-color;
      }
    }
    
    .stat-box {
      text-align: center;
      padding: 1rem;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      background-color: #f8f9fa;
      
      .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: $primary-color;
      }
      
      .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
      }
    }
    
    .bg-light {
      background-color: #f8f9fa !important;
    }
  }
  
  .modal-footer {
    border-top: 1px solid #e0e0e0;
    padding: 1rem 1.5rem;
    
    .btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.btn-primary {
        background-color: $primary-color;
        border-color: $primary-color;
        
        &:hover:not(:disabled) {
          background-color: darken($primary-color, 10%);
          border-color: darken($primary-color, 10%);
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .promotions-admin-container {
    padding: 1rem;
    
    .header-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .stats-section {
      .stat-card {
        margin-bottom: 1rem;
      }
    }
    
    .filters-section .card-body {
      padding: 1rem;
    }
    
    .promotion-item {
      .row {
        flex-direction: column;
      }
      
      .col-md-2 {
        text-align: left !important;
        margin-top: 1rem;
      }
      
      .btn-group-vertical {
        flex-direction: row;
        
        .btn {
          margin-bottom: 0;
          margin-right: 0.25rem;
          
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    
    .tabs-section {
      .nav-tabs {
        .nav-item {
          .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
          }
        }
      }
    }
  }
  
  .modal-dialog {
    margin: 1rem;
  }
}
