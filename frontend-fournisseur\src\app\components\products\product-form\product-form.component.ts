import { Component, OnInit, Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Produit, ProduitCreate, ProduitUpdate } from '../../../models';
import { CategorieService } from '../../../services/categorie.service';
import { SousCategorieService } from '../../../services/sous-categorie.service';
import { MarqueService } from '../../../services/marque.service';
import { FormeService } from '../../../services/forme.service';
import { TauxTVAService } from '../../../services/taux-tva.service';

@Component({
  selector: 'app-product-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './product-form.component.html',
  styleUrls: ['./product-form.component.css']
})
export class ProductFormComponent implements OnInit {
  @Input() product: Produit | null = null;
  @Input() isEdit = false;
  @Output() save = new EventEmitter<ProduitCreate | ProduitUpdate>();
  @Output() cancel = new EventEmitter<void>();

  productForm: FormGroup;
  isSubmitting = false;

  // Gestion des images multiples (max 10)
  selectedImageFiles: File[] = [];
  imagePreviewUrls: string[] = [];
  maxImages = 10;

  // Dropdowns pour les sélects
  categoriesDropdown: any[] = [];
  sousCategoriesDropdown: any[] = [];
  marquesDropdown: any[] = [];
  formesDropdown: any[] = [];
  tauxTVADropdown: any[] = [];

  // Services injectés
  private categorieService = inject(CategorieService);
  private sousCategorieService = inject(SousCategorieService);
  private marqueService = inject(MarqueService);
  private formeService = inject(FormeService);
  private tauxTVAService = inject(TauxTVAService);

  // Options pour les sélects - Lunettes
  categories = [
    { value: 'Lunettes de soleil', label: 'Lunettes de soleil' },
    { value: 'Lunettes de vue', label: 'Lunettes de vue' },
    { value: 'Lunettes de sport', label: 'Lunettes de sport' },
    { value: 'Lunettes de luxe', label: 'Lunettes de luxe' },
    { value: 'Lunettes de lecture', label: 'Lunettes de lecture' },
    { value: 'Lunettes spécialisées', label: 'Lunettes spécialisées' }
  ];

  brands = [
    { value: 'Ray-Ban', label: 'Ray-Ban' },
    { value: 'Oakley', label: 'Oakley' },
    { value: 'Tom Ford', label: 'Tom Ford' },
    { value: 'Gucci', label: 'Gucci' },
    { value: 'Prada', label: 'Prada' },
    { value: 'Persol', label: 'Persol' },
    { value: 'Versace', label: 'Versace' },
    { value: 'Carrera', label: 'Carrera' },
    { value: 'Essilor', label: 'Essilor' },
    { value: 'Zeiss', label: 'Zeiss' },
    { value: 'Hoya', label: 'Hoya' },
    { value: 'SafeVision', label: 'SafeVision' },
    { value: 'autre', label: 'Autre' }
  ];

  // Options spécifiques aux lunettes
  formes = [
    { value: 'Aviateur', label: 'Aviateur' },
    { value: 'Wayfarer', label: 'Wayfarer' },
    { value: 'Rectangulaire', label: 'Rectangulaire' },
    { value: 'Rond', label: 'Rond' },
    { value: 'Cat-eye', label: 'Cat-eye' },
    { value: 'Papillon', label: 'Papillon' },
    { value: 'Sport', label: 'Sport' },
    { value: 'Pilote', label: 'Pilote' }
  ];

  materiaux = [
    { value: 'Métal', label: 'Métal' },
    { value: 'Acétate', label: 'Acétate' },
    { value: 'TR90', label: 'TR90 (Flexible)' },
    { value: 'Titane', label: 'Titane' },
    { value: 'Plastique', label: 'Plastique' },
    { value: 'Bois', label: 'Bois' },
    { value: 'Carbone', label: 'Fibre de carbone' }
  ];

  typesVerres = [
    { value: 'Solaire', label: 'Solaire' },
    { value: 'Correcteur', label: 'Correcteur' },
    { value: 'Polarisé', label: 'Polarisé' },
    { value: 'Photochromique', label: 'Photochromique' },
    { value: 'Anti-lumière bleue', label: 'Anti-lumière bleue' },
    { value: 'Miroir', label: 'Miroir' },
    { value: 'Dégradé', label: 'Dégradé' }
  ];

  constructor(private formBuilder: FormBuilder) {
    this.productForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadDropdownData();

    if (this.product && this.isEdit) {
      this.loadProductData();
    }
  }

  /**
   * Créer le formulaire selon ProduitCreateDto backend
   */
  createForm(): FormGroup {
    return this.formBuilder.group({
      // Champs obligatoires selon backend
      nom: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      prixAchatHT: ['', [Validators.required, Validators.min(0.01)]],
      prixVenteHT: ['', [Validators.required, Validators.min(0.01)]],
      stock: ['', [Validators.required, Validators.min(0)]],
      categorieId: ['', Validators.required], // Ajout du champ catégorie pour le filtrage
      sousCategorieId: ['', Validators.required],
      marqueId: ['', Validators.required],
      formeId: ['', Validators.required],
      tauxTVAId: ['', Validators.required],

      // Champs optionnels
      referenceFournisseur: [''],
      codeABarre: [''],
      pourcentageRemise: ['', [Validators.min(0), Validators.max(100)]]
    });
  }

  /**
   * Charger les données des dropdowns
   */
  loadDropdownData(): void {
    // Charger les catégories
    this.categorieService.getDropdown().subscribe({
      next: (categories) => {
        this.categoriesDropdown = categories;
      },
      error: (error) => console.error('❌ Erreur chargement catégories:', error)
    });

    // Charger les marques
    this.marqueService.getDropdown().subscribe({
      next: (marques) => {
        this.marquesDropdown = marques;
      },
      error: (error) => console.error('❌ Erreur chargement marques:', error)
    });

    // Charger les taux TVA
    this.tauxTVAService.getDropdown().subscribe({
      next: (tauxTVA) => {
        this.tauxTVADropdown = tauxTVA;
      },
      error: (error) => console.error('❌ Erreur chargement taux TVA:', error)
    });
  }

  /**
   * Gérer le changement de catégorie pour filtrer les sous-catégories et formes
   */
  onCategorieChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const categorieId = +target.value;

    if (categorieId) {
      // Réinitialiser les champs dépendants
      this.productForm.patchValue({
        sousCategorieId: '',
        formeId: ''
      });

      // Charger les sous-catégories pour cette catégorie
      this.sousCategorieService.getByCategorie(categorieId).subscribe({
        next: (sousCategories) => {
          this.sousCategoriesDropdown = Array.isArray(sousCategories) ? sousCategories : [];
        },
        error: (error) => {
          console.error('❌ Erreur chargement sous-catégories:', error);
          this.sousCategoriesDropdown = [];
        }
      });

      // Charger les formes filtrées par catégorie
      this.formeService.getByCategorie(categorieId).subscribe({
        next: (formes) => {
          this.formesDropdown = Array.isArray(formes) ? formes : [];
        },
        error: (error) => {
          console.error('❌ Erreur chargement formes:', error);
          this.formesDropdown = [];
        }
      });

      // Charger les taux TVA filtrés par catégorie
      this.tauxTVAService.getDropdownByCategorie(categorieId).subscribe({
        next: (taux) => {
          this.tauxTVADropdown = Array.isArray(taux) ? taux : [];

          // Sélectionner automatiquement le premier taux TVA et désactiver le champ
          if (this.tauxTVADropdown.length > 0) {
            const premierTaux = this.tauxTVADropdown[0];
            this.productForm.patchValue({ tauxTVAId: premierTaux.id });
            this.productForm.get('tauxTVAId')?.disable(); // Rendre non modifiable
            console.log('✅ Taux TVA sélectionné automatiquement:', premierTaux);
          } else {
            this.productForm.get('tauxTVAId')?.enable(); // Réactiver si aucun taux trouvé
          }
        },
        error: (error) => {
          console.error('❌ Erreur chargement taux TVA:', error);
          this.tauxTVADropdown = [];
          this.productForm.get('tauxTVAId')?.enable(); // Réactiver en cas d'erreur
        }
      });
    } else {
      // Vider les dropdowns dépendants
      this.sousCategoriesDropdown = [];
      this.formesDropdown = [];
      this.tauxTVADropdown = [];
    }
  }

  /**
   * Charger les données du produit pour l'édition selon ProduitDto backend
   */
  loadProductData(): void {
    if (this.product) {
      this.productForm.patchValue({
        nom: this.product.nom,
        description: this.product.description,
        prixAchatHT: this.product.prixAchatHT,
        prixVenteHT: this.product.prixVenteHT,
        stock: this.product.stock,
        sousCategorieId: this.product.sousCategorieId,
        marqueId: this.product.marqueId,
        formeId: this.product.formeId,
        tauxTVAId: this.product.tauxTVAId,
        referenceFournisseur: this.product.referenceFournisseur || '',
        codeABarre: this.product.codeABarre || '',
        pourcentageRemise: this.product.pourcentageRemiseTotale || 0
      });

      // En mode édition, laisser le taux TVA modifiable
      this.productForm.get('tauxTVAId')?.enable();

      // Gérer les images multiples
      if (this.product.images && this.product.images.length > 0) {
        this.imagePreviewUrls = this.product.images.map(img => img.imageUrl);
      }
    }
  }

  /**
   * Gérer l'ajout d'images multiples (max 10)
   */
  onImagesChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const files = target.files;

    if (files) {
      // Vérifier le nombre total d'images
      if (this.selectedImageFiles.length + files.length > this.maxImages) {
        alert(`Vous ne pouvez ajouter que ${this.maxImages} images maximum. Actuellement: ${this.selectedImageFiles.length}`);
        return;
      }

      Array.from(files).forEach(file => {
        // Vérifier le type de fichier
        if (!file.type.startsWith('image/')) {
          alert(`Le fichier ${file.name} n'est pas une image valide`);
          return;
        }

        // Vérifier la taille (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          alert(`Le fichier ${file.name} ne doit pas dépasser 5MB`);
          return;
        }

        // Ajouter le fichier
        this.selectedImageFiles.push(file);

        // Créer un aperçu
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            this.imagePreviewUrls.push(e.target.result as string);
          }
        };
        reader.readAsDataURL(file);
      });

      // Réinitialiser l'input
      target.value = '';
    }
  }

  /**
   * Supprimer une image par index
   */
  removeImage(index: number): void {
    this.selectedImageFiles.splice(index, 1);
    this.imagePreviewUrls.splice(index, 1);
  }

  /**
   * Réorganiser les images (déplacer vers le haut)
   */
  moveImageUp(index: number): void {
    if (index > 0) {
      // Échanger les fichiers
      [this.selectedImageFiles[index], this.selectedImageFiles[index - 1]] =
      [this.selectedImageFiles[index - 1], this.selectedImageFiles[index]];

      // Échanger les aperçus
      [this.imagePreviewUrls[index], this.imagePreviewUrls[index - 1]] =
      [this.imagePreviewUrls[index - 1], this.imagePreviewUrls[index]];
    }
  }

  /**
   * Réorganiser les images (déplacer vers le bas)
   */
  moveImageDown(index: number): void {
    if (index < this.selectedImageFiles.length - 1) {
      // Échanger les fichiers
      [this.selectedImageFiles[index], this.selectedImageFiles[index + 1]] =
      [this.selectedImageFiles[index + 1], this.selectedImageFiles[index]];

      // Échanger les aperçus
      [this.imagePreviewUrls[index], this.imagePreviewUrls[index + 1]] =
      [this.imagePreviewUrls[index + 1], this.imagePreviewUrls[index]];
    }
  }

  /**
   * Récupérer le taux TVA sélectionné
   */
  getTauxTVASelectionne(): number {
    const tauxTVAId = this.productForm.get('tauxTVAId')?.value;
    if (tauxTVAId && this.tauxTVADropdown.length > 0) {
      const tauxTrouve = this.tauxTVADropdown.find(t => t.id == tauxTVAId);
      if (tauxTrouve && tauxTrouve.taux) {
        return tauxTrouve.taux / 100; // Convertir le pourcentage en décimal
      }
    }
    return 0.19; // 19% par défaut si aucun taux trouvé
  }

  /**
   * Calculer le prix TTC à partir du prix HT
   */
  getPrixTTC(): number {
    const prixHT = this.productForm.get('prixVenteHT')?.value || 0;
    const tauxTVA = this.getTauxTVASelectionne();
    return prixHT * (1 + tauxTVA);
  }

  /**
   * Obtenir le libellé du taux TVA sélectionné
   */
  getTauxTVALibelle(): string {
    const tauxTVAId = this.productForm.getRawValue().tauxTVAId;
    if (tauxTVAId && this.tauxTVADropdown.length > 0) {
      const tauxTrouve = this.tauxTVADropdown.find(t => t.id == tauxTVAId);
      if (tauxTrouve) {
        return `${tauxTrouve.nom} (${tauxTrouve.taux}%)`;
      }
    }
    return 'Aucun taux sélectionné';
  }

  /**
   * Soumettre le formulaire selon ProduitCreateDto/ProduitUpdateDto backend
   */
  onSubmit(): void {
    // Vérifier que le taux TVA est bien sélectionné
    const tauxTVAId = this.productForm.getRawValue().tauxTVAId;
    if (!tauxTVAId) {
      alert('Veuillez sélectionner une catégorie pour que le taux TVA soit automatiquement défini.');
      return;
    }

    if (this.productForm.valid) {
      this.isSubmitting = true;

      if (this.isEdit && this.product) {
        // Mode édition - ProduitUpdateDto
        const updateData: ProduitUpdate = {
          id: this.product.id,
          nom: this.productForm.value.nom,
          description: this.productForm.value.description,
          prixVenteHT: this.productForm.value.prixVenteHT,
          stock: this.productForm.value.stock,
          pourcentageRemise: this.productForm.value.pourcentageRemise || undefined,
          imageFiles: this.selectedImageFiles.length > 0 ? this.selectedImageFiles : undefined
        };
        this.save.emit(updateData);
      } else {
        // Mode création - ProduitCreateDto
        // Utiliser getRawValue() pour inclure les champs disabled (comme tauxTVAId)
        const formValues = this.productForm.getRawValue();
        const createData: ProduitCreate = {
          referenceOriginal: `REF-${Date.now()}`, // Généré automatiquement
          referenceFournisseur: formValues.referenceFournisseur,
          codeABarre: formValues.codeABarre || `${Date.now()}${Math.floor(Math.random() * 1000)}`,
          nom: formValues.nom,
          description: formValues.description,
          prixAchatHT: formValues.prixAchatHT,
          prixVenteHT: formValues.prixVenteHT,
          tauxTVAId: formValues.tauxTVAId,
          stock: formValues.stock,
          sousCategorieId: formValues.sousCategorieId,
          marqueId: formValues.marqueId,
          formeId: formValues.formeId,
          fournisseurId: 1, // À récupérer depuis le service d'authentification
          pourcentageRemise: formValues.pourcentageRemise || undefined,
          imageFiles: this.selectedImageFiles.length > 0 ? this.selectedImageFiles : undefined
        };
        this.save.emit(createData);
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  /**
   * Annuler
   */
  onCancel(): void {
    this.cancel.emit();
  }

  /**
   * Marquer tous les champs comme touchés
   */
  private markFormGroupTouched(): void {
    Object.keys(this.productForm.controls).forEach(key => {
      const control = this.productForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Obtenir les erreurs d'un champ
   */
  getFieldError(fieldName: string): string {
    const field = this.productForm.get(fieldName);
    if (field?.errors && field?.touched) {
      if (field.errors['required']) return `${fieldName} est requis`;
      if (field.errors['minlength']) return `Minimum ${field.errors['minlength'].requiredLength} caractères`;
      if (field.errors['maxlength']) return `Maximum ${field.errors['maxlength'].requiredLength} caractères`;
      if (field.errors['min']) return `Valeur minimum : ${field.errors['min'].min}`;
      if (field.errors['max']) return `Valeur maximum : ${field.errors['max'].max}`;
      if (field.errors['email']) return 'Format email invalide';
    }
    return '';
  }

  /**
   * Vérifier si un champ a des erreurs
   */
  hasFieldError(fieldName: string): boolean {
    const field = this.productForm.get(fieldName);
    return !!(field?.errors && field?.touched);
  }

  /**
   * Formater le prix
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(price);
  }

  /**
   * Calculer le prix final avec remise
   */
  getFinalPrice(): number {
    const prixVente = this.productForm.get('prixVenteHT')?.value || 0;
    const remise = this.productForm.get('pourcentageRemise')?.value || 0;
    return prixVente * (1 - remise / 100);
  }

  /**
   * Obtenir le pourcentage de remise
   */
  getDiscountPercentage(): number {
    return this.productForm.get('pourcentageRemise')?.value || 0;
  }
}
