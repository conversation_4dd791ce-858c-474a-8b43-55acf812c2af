﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/tva")]
    [ApiController]
    public class TauxTVAController : ControllerBase
    {
        private readonly ITauxTVAService _tauxTVAService;

        public TauxTVAController(ITauxTVAService tauxTVAService)
        {
            _tauxTVAService = tauxTVAService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<TauxTVA>>> GetAll([FromQuery] bool inclureInactifs = false)
        {
            return Ok(await _tauxTVAService.GetAllAsync(inclureInactifs));
        }

        [HttpGet("actuel")]
        public async Task<ActionResult<TauxTVA>> GetTauxActuel()
        {
            var taux = await _tauxTVAService.GetTauxActuelAsync();
            return taux != null ? Ok(taux) : NotFound("Aucun taux actif trouvé");
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<TauxTVA>> GetById(int id)
        {
            var taux = await _tauxTVAService.GetByIdAsync(id);
            return taux != null ? Ok(taux) : NotFound();
        }

        [HttpGet("dropdown")]
        public async Task<ActionResult<Dictionary<int, string>>> GetForDropdown()
        {
            return Ok(await _tauxTVAService.GetTauxForDropdownAsync());
        }

        [HttpGet("by-categorie/{categorieId}")]
        public async Task<ActionResult<IEnumerable<TauxTVA>>> GetByCategorie(int categorieId)
        {
            return Ok(await _tauxTVAService.GetByCategorieAsync(categorieId));
        }

        [HttpGet("dropdown/{categorieId}")]
        public async Task<ActionResult<Dictionary<int, string>>> GetDropdownByCategorie(int categorieId)
        {
            return Ok(await _tauxTVAService.GetTauxForDropdownByCategorieAsync(categorieId));
        }

        [HttpPost("calculer-ttc")]
        public async Task<ActionResult<decimal>> CalculerTTC([FromQuery] int tauxId, [FromQuery] decimal prixHT)
        {
            try
            {
                return Ok(await _tauxTVAService.CalculerTTCAsync(tauxId, prixHT));
            }
            catch (KeyNotFoundException)
            {
                return NotFound("Taux non trouvé");
            }
        }

        [HttpPost("calculer-ht")]
        public async Task<ActionResult<decimal>> CalculerHT([FromQuery] int tauxId, [FromQuery] decimal prixTTC)
        {
            try
            {
                return Ok(await _tauxTVAService.CalculerHTAsync(tauxId, prixTTC));
            }
            catch (KeyNotFoundException)
            {
                return NotFound("Taux non trouvé");
            }
        }

        [HttpPost]
        [ProducesResponseType(typeof(TauxTVADto), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult> Create([FromBody] TauxTVADto.Create dto)
        {
            if (dto == null)
            {
                return BadRequest("Les données de la TVA ne peuvent pas être nulles");
            }
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var taux = new TauxTVA
                {
                    Libelle = dto.Libelle!,
                    Taux = dto.Taux,
                    Description = dto.Description,
                    EstActif = dto.EstActif,
                    DateEffet = dto.DateEffet,
                    DateFin = dto.DateFin
                };

                var created = await _tauxTVAService.CreateAsync(taux);

                return CreatedAtAction(nameof(GetById), new { id = created.Id },
                    new TauxTVADto
                    {
                        Id = created.Id,
                        Libelle = created.Libelle,
                        Taux = created.Taux,
                        Description = created.Description,
                        EstActif = created.EstActif,
                        DateEffet = created.DateEffet,
                        DateFin = created.DateFin
                    });
            }
            catch (ArgumentNullException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                // Log l'erreur ici
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPut("{id:int}")]
        public async Task<IActionResult> Update(int id, [FromBody] TauxTVA taux)
        {
            if (id != taux.Id)
                return BadRequest("ID mismatch");

            try
            {
                await _tauxTVAService.UpdateAsync(taux);
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _tauxTVAService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
