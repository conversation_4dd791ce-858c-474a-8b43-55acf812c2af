﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Interfaces;
using WebApiPfe.Models.Entity;
namespace WebApiPfe.Services.Implementations
{
    public class NotificationService : INotificationService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(AppDbContext context, IMapper mapper, ILogger<NotificationService> logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<NotificationDto> GetByIdAsync(int id)
        {
            var notification = await _context.Notifications
                .Include(n => n.Utilisateur)
                .FirstOrDefaultAsync(n => n.Id == id);

            if (notification == null)
                throw new KeyNotFoundException("Notification introuvable");

            return _mapper.Map<NotificationDto>(notification);
        }

        public async Task<List<NotificationDto>> GetByUserAsync(int userId)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UtilisateurId == userId)
                .OrderByDescending(n => n.DateEnvoi)
                .ToListAsync();

            return _mapper.Map<List<NotificationDto>>(notifications);
        }

        public async Task<List<NotificationDto>> GetUnreadByUserAsync(int userId)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UtilisateurId == userId && !n.EstLue)
                .OrderByDescending(n => n.DateEnvoi)
                .ToListAsync();

            return _mapper.Map<List<NotificationDto>>(notifications);
        }

        public async Task<NotificationDto> CreateAsync(CreateNotificationDto dto)
        {
            var userExists = await _context.Utilisateurs.AnyAsync(u => u.Id == dto.UtilisateurId);
            if (!userExists)
                throw new KeyNotFoundException("Utilisateur introuvable");

            var notification = new Notification
            {
                Contenu = dto.Contenu,
                DateEnvoi = DateTime.UtcNow,
                EstLue = false,
                UtilisateurId = dto.UtilisateurId
            };

            _context.Notifications.Add(notification);
            await _context.SaveChangesAsync();

            return await GetByIdAsync(notification.Id);
        }

        public async Task<WebApiPfe.DTOs.NotificationDto> CreateNotificationAsync(WebApiPfe.DTOs.CreateNotificationDto dto)
        {
            var userExists = await _context.Utilisateurs.AnyAsync(u => u.Id == dto.UtilisateurId);
            if (!userExists)
                throw new KeyNotFoundException("Utilisateur introuvable");

            var notification = new Notification
            {
                Titre = dto.Titre,
                Message = dto.Message,
                Type = dto.Type,
                DateEnvoi = DateTime.UtcNow,
                EstLue = false,
                UtilisateurId = dto.UtilisateurId
            };

            _context.Notifications.Add(notification);
            await _context.SaveChangesAsync();

            return new WebApiPfe.DTOs.NotificationDto
            {
                Id = notification.Id,
                Titre = notification.Titre,
                Message = notification.Message,
                Type = notification.Type,
                DateEnvoi = notification.DateEnvoi,
                EstLue = notification.EstLue,
                UtilisateurId = notification.UtilisateurId
            };
        }

        public async Task MarkAsReadAsync(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null)
                throw new KeyNotFoundException("Notification introuvable");

            notification.EstLue = true;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null)
                throw new KeyNotFoundException("Notification introuvable");

            _context.Notifications.Remove(notification);
            await _context.SaveChangesAsync();
        }
    }
}
