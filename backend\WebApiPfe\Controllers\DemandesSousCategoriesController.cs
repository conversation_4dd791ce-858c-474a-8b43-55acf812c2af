using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DemandesSousCategoriesController : ControllerBase
    {
        private readonly IDemandeSousCategorieService _demandeSousCategorieService;

        public DemandesSousCategoriesController(IDemandeSousCategorieService demandeSousCategorieService)
        {
            _demandeSousCategorieService = demandeSousCategorieService;
        }

        /// <summary>
        /// Obtenir toutes les demandes de sous-catégories (Admin seulement)
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<DemandeSousCategorieDto>>> GetAllDemandes()
        {
            try
            {
                var demandes = await _demandeSousCategorieService.GetAllDemandesAsync();
                return Ok(demandes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des demandes", error = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir les demandes de sous-catégories par statut (Admin seulement)
        /// </summary>
        [HttpGet("statut/{statut}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<DemandeSousCategorieDto>>> GetDemandesByStatut(StatutDemande statut)
        {
            try
            {
                var demandes = await _demandeSousCategorieService.GetDemandesByStatutAsync(statut);
                return Ok(demandes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des demandes", error = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir les demandes de sous-catégories du fournisseur connecté
        /// </summary>
        [HttpGet("mes-demandes")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<IEnumerable<DemandeSousCategorieDto>>> GetMesDemandes()
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!int.TryParse(userIdClaim, out int fournisseurId))
                {
                    return BadRequest(new { message = "ID utilisateur invalide" });
                }

                var demandes = await _demandeSousCategorieService.GetDemandesByFournisseurAsync(fournisseurId);
                return Ok(demandes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des demandes", error = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir une demande de sous-catégorie par ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<DemandeSousCategorieDto>> GetDemandeById(int id)
        {
            try
            {
                var demande = await _demandeSousCategorieService.GetDemandeByIdAsync(id);
                if (demande == null)
                {
                    return NotFound(new { message = "Demande non trouvée" });
                }

                // Vérifier les autorisations
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                if (userRole == "Fournisseur" && int.TryParse(userIdClaim, out int fournisseurId))
                {
                    if (demande.FournisseurId != fournisseurId)
                    {
                        return Forbid();
                    }
                }
                else if (userRole != "Admin")
                {
                    return Forbid();
                }

                return Ok(demande);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération de la demande", error = ex.Message });
            }
        }

        /// <summary>
        /// Créer une nouvelle demande de sous-catégorie (Fournisseur seulement)
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<DemandeSousCategorieDto>> CreateDemande([FromBody] CreateDemandeSousCategorieDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!int.TryParse(userIdClaim, out int fournisseurId))
                {
                    return BadRequest(new { message = "ID utilisateur invalide" });
                }

                var demande = await _demandeSousCategorieService.CreateDemandeAsync(createDto, fournisseurId);
                return CreatedAtAction(nameof(GetDemandeById), new { id = demande.Id }, demande);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la création de la demande", error = ex.Message });
            }
        }

        /// <summary>
        /// Traiter une demande de sous-catégorie (Admin seulement)
        /// </summary>
        [HttpPut("{id}/traiter")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<DemandeSousCategorieDto>> TraiterDemande(int id, [FromBody] TraiterDemandeSousCategorieDto traiterDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!int.TryParse(userIdClaim, out int adminId))
                {
                    return BadRequest(new { message = "ID utilisateur invalide" });
                }

                var demande = await _demandeSousCategorieService.TraiterDemandeAsync(id, traiterDto, adminId);
                return Ok(demande);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors du traitement de la demande", error = ex.Message });
            }
        }

        /// <summary>
        /// Supprimer une demande de sous-catégorie
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteDemande(int id)
        {
            try
            {
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                // Vérifier les autorisations
                if (userRole == "Fournisseur" && int.TryParse(userIdClaim, out int fournisseurId))
                {
                    var demande = await _demandeSousCategorieService.GetDemandeByIdAsync(id);
                    if (demande == null)
                    {
                        return NotFound(new { message = "Demande non trouvée" });
                    }
                    if (demande.FournisseurId != fournisseurId)
                    {
                        return Forbid();
                    }
                    if (demande.Statut != StatutDemande.EnAttente)
                    {
                        return BadRequest(new { message = "Impossible de supprimer une demande déjà traitée" });
                    }
                }
                else if (userRole != "Admin")
                {
                    return Forbid();
                }

                var success = await _demandeSousCategorieService.DeleteDemandeAsync(id);
                if (!success)
                {
                    return NotFound(new { message = "Demande non trouvée" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la suppression de la demande", error = ex.Message });
            }
        }
    }
}
