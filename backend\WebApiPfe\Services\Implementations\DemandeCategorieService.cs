using AutoMapper;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class DemandeCategorieService : IDemandeCategorieService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly INotificationService _notificationService;

        public DemandeCategorieService(AppDbContext context, IMapper mapper, INotificationService notificationService)
        {
            _context = context;
            _mapper = mapper;
            _notificationService = notificationService;
        }

        public async Task<IEnumerable<DemandeCategorieDto>> GetAllDemandesAsync()
        {
            var demandes = await _context.DemandesCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.CategorieCreee)
                .OrderByDescending(d => d.DateDemande)
                .ToListAsync();

            return demandes.Select(d => new DemandeCategorieDto
            {
                Id = d.Id,
                Nom = d.Nom,
                Description = d.Description,
                FournisseurId = d.FournisseurId,
                NomFournisseur = d.Fournisseur?.UserName ?? "Inconnu",
                Statut = d.Statut,
                DateDemande = d.DateDemande,
                DateTraitement = d.DateTraitement,
                AdminTraitantId = d.AdminTraitantId,
                NomAdminTraitant = d.AdminTraitant?.UserName,
                CommentaireAdmin = d.CommentaireAdmin,
                CategorieCreeeId = d.CategorieCreeeId,
                NomCategorieCreee = d.CategorieCreee?.Nom
            });
        }

        public async Task<IEnumerable<DemandeCategorieDto>> GetDemandesByFournisseurAsync(int fournisseurId)
        {
            var demandes = await _context.DemandesCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.CategorieCreee)
                .Where(d => d.FournisseurId == fournisseurId)
                .OrderByDescending(d => d.DateDemande)
                .ToListAsync();

            return demandes.Select(d => new DemandeCategorieDto
            {
                Id = d.Id,
                Nom = d.Nom,
                Description = d.Description,
                FournisseurId = d.FournisseurId,
                NomFournisseur = d.Fournisseur?.UserName ?? "Inconnu",
                Statut = d.Statut,
                DateDemande = d.DateDemande,
                DateTraitement = d.DateTraitement,
                AdminTraitantId = d.AdminTraitantId,
                NomAdminTraitant = d.AdminTraitant?.UserName,
                CommentaireAdmin = d.CommentaireAdmin,
                CategorieCreeeId = d.CategorieCreeeId,
                NomCategorieCreee = d.CategorieCreee?.Nom
            });
        }

        public async Task<IEnumerable<DemandeCategorieDto>> GetDemandesByStatutAsync(StatutDemande statut)
        {
            var demandes = await _context.DemandesCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.CategorieCreee)
                .Where(d => d.Statut == statut)
                .OrderByDescending(d => d.DateDemande)
                .ToListAsync();

            return demandes.Select(d => new DemandeCategorieDto
            {
                Id = d.Id,
                Nom = d.Nom,
                Description = d.Description,
                FournisseurId = d.FournisseurId,
                NomFournisseur = d.Fournisseur?.UserName ?? "Inconnu",
                Statut = d.Statut,
                DateDemande = d.DateDemande,
                DateTraitement = d.DateTraitement,
                AdminTraitantId = d.AdminTraitantId,
                NomAdminTraitant = d.AdminTraitant?.UserName,
                CommentaireAdmin = d.CommentaireAdmin,
                CategorieCreeeId = d.CategorieCreeeId,
                NomCategorieCreee = d.CategorieCreee?.Nom
            });
        }

        public async Task<DemandeCategorieDto?> GetDemandeByIdAsync(int id)
        {
            var demande = await _context.DemandesCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.CategorieCreee)
                .FirstOrDefaultAsync(d => d.Id == id);

            if (demande == null) return null;

            return new DemandeCategorieDto
            {
                Id = demande.Id,
                Nom = demande.Nom,
                Description = demande.Description,
                FournisseurId = demande.FournisseurId,
                NomFournisseur = demande.Fournisseur?.UserName ?? "Inconnu",
                Statut = demande.Statut,
                DateDemande = demande.DateDemande,
                DateTraitement = demande.DateTraitement,
                AdminTraitantId = demande.AdminTraitantId,
                NomAdminTraitant = demande.AdminTraitant?.UserName,
                CommentaireAdmin = demande.CommentaireAdmin,
                CategorieCreeeId = demande.CategorieCreeeId,
                NomCategorieCreee = demande.CategorieCreee?.Nom
            };
        }

        public async Task<DemandeCategorieDto> CreateDemandeAsync(WebApiPfe.DTOs.Admin.CreateDemandeCategorieDto createDto, int fournisseurId)
        {
            var demande = new DemandeCategorie
            {
                Nom = createDto.Nom,
                Description = createDto.Description,
                FournisseurId = fournisseurId,
                Statut = StatutDemande.EnAttente,
                DateDemande = DateTime.UtcNow
            };

            _context.DemandesCategories.Add(demande);

            // Créer la catégorie avec statut en attente
            var categorie = new Categorie
            {
                Nom = createDto.Nom,
                Description = createDto.Description,
                EstValidee = false // En attente d'approbation
            };

            _context.Categories.Add(categorie);
            await _context.SaveChangesAsync();

            // Envoyer notification aux admins
            var admins = await _context.Users
                .Where(u => u.RoleDiscriminator == RoleUtilisateur.Admin)
                .ToListAsync();

            Console.WriteLine($"Nombre d'admins trouvés: {admins.Count}");

            foreach (var admin in admins)
            {
                Console.WriteLine($"Envoi notification à admin ID: {admin.Id}");
                await _notificationService.CreateNotificationAsync(new CreateNotificationDto
                {
                    UtilisateurId = admin.Id,
                    Titre = "Nouvelle demande de catégorie",
                    Message = $"Le fournisseur a demandé la création de la catégorie '{createDto.Nom}'",
                    Type = TypeNotification.DemandeCategorie
                });
            }

            return await GetDemandeByIdAsync(demande.Id) ?? throw new InvalidOperationException("Erreur lors de la création de la demande");
        }

        public async Task<DemandeCategorieDto> TraiterDemandeAsync(int id, TraiterDemandeCategorieDto traiterDto, int adminId)
        {
            var demande = await _context.DemandesCategories
                .Include(d => d.Fournisseur)
                .FirstOrDefaultAsync(d => d.Id == id);

            if (demande == null)
                throw new ArgumentException("Demande non trouvée");

            if (demande.Statut != StatutDemande.EnAttente)
                throw new InvalidOperationException("Cette demande a déjà été traitée");

            demande.Statut = traiterDto.Statut;
            demande.DateTraitement = DateTime.UtcNow;
            demande.AdminTraitantId = adminId;
            demande.CommentaireAdmin = traiterDto.CommentaireAdmin;

            // Si approuvée, valider la catégorie existante
            if (traiterDto.Statut == StatutDemande.Approuvee)
            {
                var categorieExistante = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Nom == demande.Nom && !c.EstValidee);

                if (categorieExistante != null)
                {
                    categorieExistante.EstValidee = true;
                    demande.CategorieCreeeId = categorieExistante.Id;
                }
            }
            else if (traiterDto.Statut == StatutDemande.Rejetee)
            {
                // Si rejetée, supprimer la catégorie en attente
                var categorieExistante = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Nom == demande.Nom && !c.EstValidee);

                if (categorieExistante != null)
                {
                    _context.Categories.Remove(categorieExistante);
                }
            }

            await _context.SaveChangesAsync();

            // Envoyer notification au fournisseur
            var messageNotification = traiterDto.Statut == StatutDemande.Approuvee
                ? $"Votre demande de catégorie '{demande.Nom}' a été approuvée"
                : $"Votre demande de catégorie '{demande.Nom}' a été rejetée";

            if (!string.IsNullOrEmpty(traiterDto.CommentaireAdmin))
            {
                messageNotification += $". Commentaire: {traiterDto.CommentaireAdmin}";
            }

            await _notificationService.CreateNotificationAsync(new CreateNotificationDto
            {
                UtilisateurId = demande.FournisseurId,
                Titre = "Réponse à votre demande de catégorie",
                Message = messageNotification,
                Type = TypeNotification.ReponseDemandeCategorie

            });

            return await GetDemandeByIdAsync(id) ?? throw new InvalidOperationException("Erreur lors du traitement de la demande");
        }

        public async Task<bool> DeleteDemandeAsync(int id)
        {
            var demande = await _context.DemandesCategories.FindAsync(id);
            if (demande == null) return false;

            _context.DemandesCategories.Remove(demande);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
