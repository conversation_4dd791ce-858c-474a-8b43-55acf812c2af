@import '../../../../styles.scss';

.promotions-fournisseur-container {
  padding: 1.5rem;
  
  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    
    h2 {
      color: $primary-color;
      font-weight: 600;
      margin-bottom: 0.5rem;
      
      i {
        margin-right: 0.5rem;
      }
    }
    
    .btn-primary {
      background-color: $primary-color;
      border-color: $primary-color;
      
      &:hover {
        background-color: darken($primary-color, 10%);
        border-color: darken($primary-color, 10%);
      }
    }
  }
  
  .stats-section {
    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border: 1px solid #e0e0e0;
      height: 100%;
      display: flex;
      align-items: center;
      
      .stat-icon {
        font-size: 2rem;
        color: $primary-color;
        margin-right: 1rem;
        
        &.text-success {
          color: #28a745 !important;
        }
        
        &.text-primary {
          color: $primary-color !important;
        }
        
        &.text-warning {
          color: #ffc107 !important;
        }
      }
      
      .stat-content {
        h3 {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 0.25rem;
          color: $text-color;
        }
        
        p {
          margin: 0;
          color: #6c757d;
          font-size: 0.9rem;
        }
      }
    }
    
    .type-distribution {
      .card {
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        
        .card-header {
          background-color: #f8f9fa;
          border-bottom: 1px solid #e0e0e0;
          
          h5 {
            color: $text-color;
            font-weight: 600;
            margin: 0;
            
            i {
              margin-right: 0.5rem;
              color: $primary-color;
            }
          }
        }
        
        .type-stat {
          display: flex;
          align-items: center;
          padding: 1rem;
          border: 1px solid #f0f0f0;
          border-radius: 6px;
          margin-bottom: 1rem;
          
          .type-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            color: $primary-color;
          }
          
          .type-info {
            h6 {
              margin-bottom: 0.25rem;
              color: $text-color;
            }
            
            .badge {
              font-size: 0.8rem;
            }
          }
        }
      }
    }
  }
  
  .filters-section {
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    
    .card-body {
      padding: 1.5rem;
    }
    
    .form-label {
      font-weight: 500;
      color: $text-color;
      margin-bottom: 0.5rem;
    }
    
    .form-select,
    .form-control {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
  }
  
  .promotions-list {
    .card {
      border: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 8px;
      
      .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        padding: 1rem 1.5rem;
        
        h5 {
          color: $text-color;
          font-weight: 600;
        }
        
        .btn-group {
          .btn {
            border-color: #e0e0e0;
            
            &.active {
              background-color: $primary-color;
              border-color: $primary-color;
              color: white;
            }
            
            &:hover:not(.active) {
              background-color: #f8f9fa;
            }
          }
        }
      }
    }
    
    .promotion-item {
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #f8f9fa;
      }
      
      &:last-child {
        border-bottom: none !important;
      }
      
      .promotion-info {
        h6 {
          color: $text-color;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }
        
        .badge {
          font-size: 0.75rem;
          padding: 0.4rem 0.6rem;
          
          i {
            margin-right: 0.25rem;
          }
        }
        
        .promotion-details {
          margin-top: 0.5rem;
          
          small {
            margin-bottom: 0.25rem;
            
            i {
              margin-right: 0.25rem;
            }
          }
        }
      }
      
      .promotion-value {
        .value-display {
          .value-number {
            font-size: 2rem;
            font-weight: 700;
            color: $primary-color;
          }
        }
        
        .usage-info {
          .progress {
            height: 6px;
            background-color: #f0f0f0;
            
            .progress-bar {
              transition: width 0.3s ease;
            }
          }
        }
      }
      
      .promotion-stats {
        small {
          margin-bottom: 0.25rem;
        }
      }
      
      .btn-group-vertical {
        .btn {
          border-radius: 4px;
          margin-bottom: 0.25rem;
          font-size: 0.875rem;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &.btn-outline-primary {
            border-color: $primary-color;
            color: $primary-color;
            
            &:hover {
              background-color: $primary-color;
              border-color: $primary-color;
            }
          }
          
          &.btn-outline-warning {
            border-color: #ffc107;
            color: #ffc107;
            
            &:hover {
              background-color: #ffc107;
              border-color: #ffc107;
              color: #000;
            }
          }
          
          &.btn-outline-success {
            border-color: #28a745;
            color: #28a745;
            
            &:hover {
              background-color: #28a745;
              border-color: #28a745;
            }
          }
          
          &.btn-outline-danger {
            border-color: #dc3545;
            color: #dc3545;
            
            &:hover {
              background-color: #dc3545;
              border-color: #dc3545;
            }
          }
        }
      }
    }
  }
  
  .pagination-section {
    .pagination {
      .page-link {
        color: $primary-color;
        border-color: #e0e0e0;
        
        &:hover {
          background-color: #f8f9fa;
          border-color: $primary-color;
        }
      }
      
      .page-item.active .page-link {
        background-color: $primary-color;
        border-color: $primary-color;
      }
      
      .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #e0e0e0;
      }
    }
  }
  
  .alert {
    border: none;
    border-radius: 6px;
    
    &.alert-danger {
      background-color: #f8d7da;
      color: #721c24;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

// Modal styles
.modal {
  .modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  }
  
  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    
    .modal-title {
      color: $text-color;
      font-weight: 600;
      
      i {
        margin-right: 0.5rem;
        color: $primary-color;
      }
    }
  }
  
  .modal-body {
    padding: 1.5rem;
    
    .form-label {
      font-weight: 500;
      color: $text-color;
    }
    
    .form-control,
    .form-select {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
    
    .form-text {
      color: #6c757d;
      font-size: 0.875rem;
    }
    
    .text-danger {
      font-size: 0.875rem;
    }
    
    .input-group-text {
      background-color: #f8f9fa;
      border-color: #e0e0e0;
    }
    
    .form-check-input {
      &:checked {
        background-color: $primary-color;
        border-color: $primary-color;
      }
      
      &:focus {
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
  }
  
  .modal-footer {
    border-top: 1px solid #e0e0e0;
    padding: 1rem 1.5rem;
    
    .btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.btn-primary {
        background-color: $primary-color;
        border-color: $primary-color;
        
        &:hover:not(:disabled) {
          background-color: darken($primary-color, 10%);
          border-color: darken($primary-color, 10%);
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .promotions-fournisseur-container {
    padding: 1rem;
    
    .header-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .stats-section {
      .stat-card {
        margin-bottom: 1rem;
      }
    }
    
    .filters-section .card-body {
      padding: 1rem;
    }
    
    .promotion-item {
      .row {
        flex-direction: column;
      }
      
      .col-md-3 {
        text-align: left !important;
        margin-top: 1rem;
      }
      
      .btn-group-vertical {
        flex-direction: row;
        
        .btn {
          margin-bottom: 0;
          margin-right: 0.25rem;
          
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
  
  .modal-dialog {
    margin: 1rem;
  }
}
