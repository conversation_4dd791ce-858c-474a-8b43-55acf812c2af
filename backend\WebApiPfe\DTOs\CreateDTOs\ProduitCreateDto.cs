﻿using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class ProduitCreateDto
    {
        [Required(ErrorMessage = "La référence originale est obligatoire")]
        [StringLength(50, ErrorMessage = "Max 50 caractères")]
        public required string ReferenceOriginal { get; set; }

        [StringLength(50, ErrorMessage = "Max 50 caractères")]
        public string? ReferenceFournisseur { get; set; }

        [Required(ErrorMessage = "Le code à barre est obligatoire")]
        [StringLength(30, ErrorMessage = "Max 30 caractères")]
        public required string CodeABarre { get; set; }

        [Required(ErrorMessage = "Le nom est obligatoire")]
        [StringLength(100, ErrorMessage = "Max 100 caractères")]
        public required string Nom { get; set; }

        public string? Description { get; set; }

        [Required(ErrorMessage = "Le prix d'achat HT est obligatoire")]
        [Range(0.01, double.MaxValue)]
        public decimal PrixAchatHT { get; set; }

        [Required(ErrorMessage = "Le prix de vente HT est obligatoire")]
        [Range(0.01, double.MaxValue)]
        public decimal PrixVenteHT { get; set; }

        

        [Required(ErrorMessage = "Le taux de TVA est obligatoire")]
        public int TauxTVAId { get; set; }

        [Required(ErrorMessage = "Le stock initial est obligatoire")]
        [Range(0, int.MaxValue)]
        public int Stock { get; set; }

        [Required(ErrorMessage = "La sous-catégorie est obligatoire")]
        public int SousCategorieId { get; set; }

        [Required(ErrorMessage = "La marque est obligatoire")]
        public int MarqueId { get; set; }

        [Required(ErrorMessage = "La forme est obligatoire")]
        public int FormeId { get; set; }

        [Required(ErrorMessage = "Le fournisseur est obligatoire")]
        public int FournisseurId { get; set; }

        [SwaggerSchema(Description = "Fichiers images à uploader")]
        public List<IFormFile> ImageFiles { get; set; } = new List<IFormFile>();
        [Range(0, 100, ErrorMessage = "La remise doit être entre 0 et 100")]
        public int? PourcentageRemise { get; set; }
    }
}
