using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class DemandeSousCategorieDto
    {
        public int Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int CategorieId { get; set; }
        public string CategorieNom { get; set; } = string.Empty;
        public int FournisseurId { get; set; }
        public string FournisseurNom { get; set; } = string.Empty;
        public string FournisseurEmail { get; set; } = string.Empty;
        public StatutDemande Statut { get; set; }
        public string StatutLibelle { get; set; } = string.Empty;
        public DateTime DateDemande { get; set; }
        public DateTime? DateTraitement { get; set; }
        public string? AdminTraitantNom { get; set; }
        public string? CommentaireAdmin { get; set; }
        public int? SousCategorieCreeeId { get; set; }
        public string? SousCategorieCreeeNom { get; set; }
    }
}
