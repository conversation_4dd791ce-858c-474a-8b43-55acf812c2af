<div class="admin-products-container">
  <!-- Header -->
  <div class="section-header">
    <h1 class="section-title">📦 Gestion des produits</h1>
    <p class="section-subtitle">Validation et gestion des produits de la plateforme</p>
  </div>

  <!-- Filters and Search -->
  <div class="filters-section">
    <div class="search-box">
      <input 
        type="text" 
        placeholder="Rechercher par nom, référence ou fournisseur..."
        [(ngModel)]="searchTerm"
        (ngModelChange)="searchTerm.set($event)"
        class="search-input">
      <i class="icon-search"></i>
    </div>

    <div class="filters">
      <select 
        [(ngModel)]="selectedCategory"
        (ngModelChange)="selectedCategory.set($event)"
        class="filter-select">
        <option value="">Toutes les catégories</option>
        <!-- Options dynamiques à ajouter -->
      </select>

      <select 
        [(ngModel)]="selectedStatus"
        (ngModelChange)="selectedStatus.set($event)"
        class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="valide">Validés</option>
        <option value="invalide">En attente</option>
        <option value="enavant">En avant</option>
      </select>

      <button class="btn btn-primary" (click)="refresh()">
        <i class="icon-refresh"></i>
        Actualiser
      </button>
    </div>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des produits...</p>
  </div>

  <!-- Error -->
  <div *ngIf="error()" class="error-message">
    <i class="icon-alert"></i>
    {{ error() }}
    <button class="btn btn-sm btn-primary" (click)="refresh()">
      Réessayer
    </button>
  </div>

  <!-- Products Table -->
  <div *ngIf="!isLoading() && !error()" class="products-table-container">
    <div class="table-header">
      <h3>Produits ({{ filteredProduits().length }})</h3>
    </div>

    <div class="table-wrapper">
      <table class="products-table">
        <thead>
          <tr>
            <th>Produit</th>
            <th>Référence</th>
            <th>Prix</th>
            <th>Stock</th>
            <th>Fournisseur</th>
            <th>Catégorie</th>
            <th>Statut</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let produit of filteredProduits()" class="product-row">
            <td class="product-info">
              <div class="product-image">
                <img 
                  *ngIf="produit.images.length > 0; else noImage" 
                  [src]="produit.images[0]" 
                  [alt]="produit.nom"
                  class="product-img">
                <ng-template #noImage>
                  <div class="no-image">📦</div>
                </ng-template>
              </div>
              <div class="product-details">
                <div class="product-name">{{ produit.nom }}</div>
                <div class="product-category">{{ produit.categorieNom }} > {{ produit.sousCategorieNom }}</div>
              </div>
            </td>
            
            <td class="product-reference">{{ produit.reference }}</td>
            
            <td class="product-price">{{ formatPrice(produit.prix) }}</td>
            
            <td class="product-stock">
              <span [ngClass]="produit.stock > 0 ? 'stock-available' : 'stock-empty'">
                {{ produit.stock }}
              </span>
            </td>
            
            <td class="product-supplier">{{ produit.fournisseurNom }}</td>
            
            <td class="product-category-cell">{{ produit.categorieNom }}</td>
            
            <td class="product-status">
              <span class="status-badge" [ngClass]="getStatusClass(produit)">
                {{ getStatusText(produit) }}
              </span>
            </td>
            
            <td class="product-actions">
              <div class="action-buttons">
                <button 
                  class="btn btn-sm btn-success"
                  [class.btn-secondary]="produit.estValide"
                  (click)="toggleValidation(produit)"
                  title="Valider/Invalider">
                  <i class="icon-check" *ngIf="!produit.estValide"></i>
                  <i class="icon-x" *ngIf="produit.estValide"></i>
                </button>
                
                <button 
                  class="btn btn-sm btn-warning"
                  [class.btn-secondary]="produit.estEnAvant"
                  (click)="toggleEnAvant(produit)"
                  title="Mettre en avant">
                  <i class="icon-star"></i>
                </button>
                
                <button 
                  class="btn btn-sm btn-danger"
                  (click)="deleteProduit(produit)"
                  title="Supprimer">
                  <i class="icon-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredProduits().length === 0" class="empty-state">
      <div class="empty-icon">📦</div>
      <h3>Aucun produit trouvé</h3>
      <p>Aucun produit ne correspond à vos critères de recherche.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="totalPages() > 1" class="pagination-container">
    <div class="pagination">
      <button 
        class="btn btn-sm"
        [disabled]="currentPage() === 1"
        (click)="currentPage.set(currentPage() - 1)">
        Précédent
      </button>
      
      <span class="page-info">
        Page {{ currentPage() }} sur {{ totalPages() }}
      </span>
      
      <button 
        class="btn btn-sm"
        [disabled]="currentPage() === totalPages()"
        (click)="currentPage.set(currentPage() + 1)">
        Suivant
      </button>
    </div>
  </div>
</div>
