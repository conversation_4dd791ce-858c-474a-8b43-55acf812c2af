﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class TauxTVAService : ITauxTVAService
    {
        private readonly AppDbContext _context;

        public TauxTVAService(AppDbContext context)
        {
            _context = context;
        }

        public async Task<decimal> CalculerHTAsync(int tauxId, decimal prixTTC)
        {
            var taux = await GetByIdAsync(tauxId);
            return taux.CalculerHT(prixTTC);
        }

        public async Task<decimal> CalculerTTCAsync(int tauxId, decimal prixHT)
        {
            var taux = await GetByIdAsync(tauxId);
            return taux.CalculerTTC(prixHT);
        }

        public async Task<TauxTVA> CreateAsync(TauxTVA taux)
        {
            if (taux == null)
                throw new ArgumentNullException(nameof(taux));

            if (string.IsNullOrWhiteSpace(taux.Libelle))
                throw new ArgumentException("Le libellé est obligatoire");

            if (taux.Taux <= 0 || taux.Taux >= 100)
                throw new ArgumentException("Le taux doit être compris entre 0.01 et 99.99");

            var conflits = await _context.TauxTVA
               .Where(t => t.Taux == taux.Taux && t.EstActif)
               .Where(t => !t.DateFin.HasValue || t.DateFin >= taux.DateEffet)
               .ToListAsync();

            if (conflits.Any())
            {
                throw new ArgumentException($"Conflit avec le taux existant ID: {conflits.First().Id}");
            }

            await _context.TauxTVA.AddAsync(taux);
            await _context.SaveChangesAsync();
            return taux;
        }
        public async Task DeleteAsync(int id)
        {
            var taux = await _context.TauxTVA
                .Include(t => t.Produits)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (taux == null)
                throw new KeyNotFoundException("Taux non trouvé");

            if (taux.Produits?.Any() == true)
                throw new InvalidOperationException("Impossible de supprimer : des produits sont associés");

            _context.TauxTVA.Remove(taux);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(int id) =>
            await _context.TauxTVA.AnyAsync(t => t.Id == id);

        public async Task<IEnumerable<TauxTVA>> GetAllAsync(bool inclureInactifs = false)
        {
            var query = _context.TauxTVA.AsQueryable();

            if (!inclureInactifs)
                query = query.Where(t => t.EstActif);

            return await query
                .OrderByDescending(t => t.DateEffet)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<TauxTVA> GetByIdAsync(int id) =>
            await _context.TauxTVA.FirstOrDefaultAsync(t => t.Id == id);

        public async Task<Dictionary<int, string>> GetTauxForDropdownAsync() =>
            await _context.TauxTVA
                .Where(t => t.EstActif)
                .OrderByDescending(t => t.DateEffet)
                .ToDictionaryAsync(t => t.Id, t => $"{t.Libelle} ({t.Taux}%)");

        public async Task<TauxTVA> GetTauxActuelAsync() =>
            await _context.TauxTVA
                .Where(t => t.EstActif && t.DateEffet <= DateTime.UtcNow)
                .OrderByDescending(t => t.DateEffet)
                .FirstOrDefaultAsync();

        public async Task<bool> IsTauxUniqueAsync(decimal taux, DateTime dateEffet, int? ignoreId = null)
        {
            var query = _context.TauxTVA
                .Where(t => t.Taux == taux && t.DateEffet == dateEffet);

            if (ignoreId.HasValue)
                query = query.Where(t => t.Id != ignoreId.Value);

            return !await query.AnyAsync();
        }

        public async Task UpdateAsync(TauxTVA taux)
        {
            if (!await IsTauxUniqueAsync(taux.Taux, taux.DateEffet, taux.Id))
                throw new ArgumentException("Un taux identique existe déjà pour cette période");

            if (taux.Taux <= 0 || taux.Taux >= 100)
                throw new ArgumentException("Le taux doit être compris entre 0 et 100");

            _context.TauxTVA.Update(taux);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<TauxTVA>> GetByCategorieAsync(int categorieId) =>
            await _context.TauxTVA
                .Where(t => t.EstActif && (t.CategorieId == categorieId || t.CategorieId == null))
                .OrderByDescending(t => t.DateEffet)
                .AsNoTracking()
                .ToListAsync();

        public async Task<Dictionary<int, string>> GetTauxForDropdownByCategorieAsync(int categorieId) =>
            await _context.TauxTVA
                .Where(t => t.EstActif && (t.CategorieId == categorieId || t.CategorieId == null))
                .OrderByDescending(t => t.DateEffet)
                .ToDictionaryAsync(t => t.Id, t => $"{t.Libelle} ({t.Taux}%)");
    }
}
