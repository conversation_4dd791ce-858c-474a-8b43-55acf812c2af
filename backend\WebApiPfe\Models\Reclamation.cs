using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Models
{
    public class Reclamation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int CommandeId { get; set; }

        [Required]
        public int ClientId { get; set; }

        [Required]
        [StringLength(100)]
        public string Objet { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public TypeReclamation Type { get; set; }

        [Required]
        public StatutReclamation Statut { get; set; } = StatutReclamation.EnAttente;

        [Required]
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;

        public DateTime? DateTraitement { get; set; }

        public DateTime? DateResolution { get; set; }

        [StringLength(1000)]
        public string? ReponseAdmin { get; set; }

        [StringLength(1000)]
        public string? ReponseFournisseur { get; set; }

        public int? TraitePar { get; set; } // ID de l'admin/fournisseur qui traite

        [StringLength(500)]
        public string? PiecesJointes { get; set; } // URLs des fichiers joints

        // Propriétés de navigation
        [ForeignKey("CommandeId")]
        public virtual Commande Commande { get; set; } = null!;

        [ForeignKey("ClientId")]
        public virtual Client Client { get; set; } = null!;

        [ForeignKey("TraitePar")]
        public virtual Utilisateur? TraiteurReclamation { get; set; }

        // Historique des actions
        public virtual ICollection<HistoriqueReclamation> Historique { get; set; } = new List<HistoriqueReclamation>();
    }

    public enum TypeReclamation
    {
        ProduitDefectueux = 1,
        LivraisonRetard = 2,
        ProduitNonConforme = 3,
        ServiceClient = 4,
        Remboursement = 5,
        Autre = 6
    }

    public enum StatutReclamation
    {
        EnAttente = 1,
        EnCours = 2,
        Resolue = 3,
        Fermee = 4,
        Rejetee = 5
    }

    public class HistoriqueReclamation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ReclamationId { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Commentaire { get; set; }

        [Required]
        public DateTime DateAction { get; set; } = DateTime.UtcNow;

        // Propriétés de navigation
        [ForeignKey("ReclamationId")]
        public virtual Reclamation Reclamation { get; set; } = null!;

        [ForeignKey("UtilisateurId")]
        public virtual Utilisateur Utilisateur { get; set; } = null!;
    }
}
