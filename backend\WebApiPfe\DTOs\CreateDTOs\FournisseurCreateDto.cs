﻿using System.ComponentModel.DataAnnotations;
using WebApiPfe.Models.Entity;
using WebApiPfe.Validators;
namespace WebApiPfe.DTOs.CreateDTOs { 
    public class FournisseurCreateDto : UtilisateurCreateDto
{
    [Required]
    [RegularExpression(@"^\d{8}$", ErrorMessage = "Matricule fiscale invalide (8 chiffres requis)")] 
    public required string MatriculeFiscale { get; set; }

    [Required]
    [StringLength(200)]
    public required string RaisonSociale { get; set; }

    public string? Description { get; set; }

    [Required(ErrorMessage = "Le RIB est requis.")]
    [CustomValidation(typeof(RibValidator), nameof(RibValidator.ValidateRIB))]
    public required string RIB { get; set; }

    [Required]
    [StringLength(3, MinimumLength = 3, ErrorMessage = "Le code banque doit contenir 3 chiffres.")]
    [RegularExpression(@"^[0-9]+$", ErrorMessage = "Seuls les chiffres sont autorisés.")] 
    public required string CodeBanque { get; set; }

    [Range(0.5, 1, ErrorMessage = "La commission doit être entre 50% et 100%")]
    public decimal Commission { get; set; } = 0.75m;
    public int DelaiPreparationJours { get; set; } = 2;

    public decimal FraisLivraisonBase { get; set; } = 9.99m;

    [Required]
    public required IFormFile LogoFile { get; set; }

    // Champs d'adresse
    [Required]
    public required string Rue { get; set; }

    [Required]
    public required string Ville { get; set; }

    [Required]
    [RegularExpression(@"^\d{4}$", ErrorMessage = "Le code postal doit contenir exactement 4 chiffres")]
    public required string CodePostal { get; set; }

    public string Pays { get; set; } = "Tunisie";
    }
}