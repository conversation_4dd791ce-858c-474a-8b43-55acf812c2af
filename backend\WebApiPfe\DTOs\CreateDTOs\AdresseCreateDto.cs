﻿using System.ComponentModel.DataAnnotations;
namespace WebApiPfe.DTOs.CreateDTOs
{
    public class AdresseCreateDto
    {
        [Required]
        public string Rue { get; set; }
        [Required]
        public string Ville { get; set; }
        [Required]
        [RegularExpression(@"^\d{4}$", ErrorMessage = "Le code postal doit contenir exactement 4 chiffres")]
        public string CodePostal { get; set; }
        [Required]
        public string Pays { get; set; } = "Tunisie";
        public bool EstPrincipale { get; set; }
        [Required]
        public int EntityId { get; set; }
    }
}
