import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { DashboardService, Fournisseur } from '../../services/dashboard.service';
import { FournisseurService } from '../../services/fournisseur.service';
import { AdresseDto } from '../../models/adresse.model';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})
export class ProfileComponent implements OnInit {
  // Signaux pour la gestion d'état
  fournisseurInfo = signal<Fournisseur | null>(null);
  isLoading = signal(true);
  isEditing = signal(false);
  isSaving = signal(false);
  error = signal<string | null>(null);
  successMessage = signal<string | null>(null);

  // Gestion des adresses
  showOtherAddresses = false;
  editingAddresses = new Set<number>();

  // Formulaire
  profileForm: FormGroup;

  constructor(
    private authService: AuthService,
    private dashboardService: DashboardService,
    private fb: FormBuilder,
    private fournisseurService: FournisseurService
  ) {
    this.profileForm = this.createForm();
  }

  ngOnInit(): void {
    console.log('🔍 Contenu du localStorage:');
    console.log('- token:', !!localStorage.getItem('token'));
    console.log('- user:', !!localStorage.getItem('user'));
    console.log('- fournisseur_profile:', !!localStorage.getItem('fournisseur_profile'));
    console.log('- supplierId:', localStorage.getItem('supplierId'));

    this.loadProfileData();
  }

  /**
   * Getter pour le FormArray des adresses
   */
  get adresses(): FormArray {
    return this.profileForm.get('adresses') as FormArray;
  }

  /**
   * Créer le formulaire de profil
   */
  private createForm(): FormGroup {
    return this.fb.group({
      // Informations personnelles
      nom: ['', [Validators.required, Validators.minLength(2)]],
      prenom: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\-\s()]+$/)]],
      dateNaissance: [''],

      // Informations entreprise
      raisonSociale: ['', [Validators.required, Validators.minLength(2)]],
      matriculeFiscale: ['', [Validators.required]],
      description: [''],
      
      // Informations bancaires (lecture seule)
      ribMasque: [{value: '', disabled: true}],
      codeBanque: [{value: '', disabled: true}],
      
      // Paramètres commerciaux
      commission: [{value: 0, disabled: true}],
      delaiPreparationJours: ['', [Validators.required, Validators.min(1)]],
      fraisLivraisonBase: ['', [Validators.required, Validators.min(0)]],

      // Adresses
      adresses: this.fb.array([])
    });
  }

  /**
   * Charger les données du profil depuis localStorage et backend
   */
  private loadProfileData(): void {
    this.isLoading.set(true);
    this.error.set(null);

    console.log('🔄 Chargement des données du profil...');

    // Priorité 1: Récupérer les données depuis l'API
    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.id) {
      console.log('🌐 Récupération des données depuis l\'API pour l\'utilisateur ID:', currentUser.id);

      this.fournisseurService.getById(currentUser.id).subscribe({
        next: (response) => {
          console.log('✅ Données du fournisseur reçues de l\'API:', response);
          this.handleApiResponse(response);
        },
        error: (error) => {
          console.error('❌ Erreur lors de la récupération depuis l\'API:', error);
          // Fallback vers localStorage
          this.loadFromLocalStorage();
        }
      });
    } else {
      console.warn('⚠️ Utilisateur non connecté, fallback vers localStorage');
      this.loadFromLocalStorage();
    }
  }

  /**
   * Traiter la réponse de l'API
   */
  private handleApiResponse(response: any): void {
    let fournisseur: Fournisseur;

    if (response && response.data) {
      // Réponse avec wrapper
      const apiData = response.data;
      fournisseur = this.mapApiDataToFournisseur(apiData);
    } else if (response) {
      // Réponse directe sans wrapper
      fournisseur = this.mapApiDataToFournisseur(response);
    } else {
      console.error('❌ Réponse API vide');
      this.loadFromLocalStorage();
      return;
    }

    console.log('✅ Profil créé depuis l\'API:', fournisseur);
    this.fournisseurInfo.set(fournisseur);
    this.populateForm(fournisseur);

    // Sauvegarder dans localStorage pour les prochaines fois
    localStorage.setItem('fournisseur_profile', JSON.stringify(fournisseur));
    this.isLoading.set(false);
  }

  /**
   * Mapper les données API vers l'interface Fournisseur
   */
  private mapApiDataToFournisseur(apiData: any): Fournisseur {
    return {
      id: apiData.id,
      email: apiData.email,
      nom: apiData.nom,
      prenom: apiData.prenom,
      phoneNumber: apiData.phoneNumber,
      role: apiData.role,
      dateNaissance: apiData.dateNaissance,
      dateInscription: apiData.dateInscription,
      derniereConnexion: apiData.derniereConnexion,
      estActif: apiData.estActif,
      matriculeFiscale: apiData.matriculeFiscale,
      raisonSociale: apiData.raisonSociale,
      description: apiData.description || '',
      ribMasque: apiData.ribMasque || apiData.rib || '',
      codeBanque: apiData.codeBanque,
      commission: apiData.commission,
      delaiPreparationJours: apiData.delaiPreparationJours,
      fraisLivraisonBase: apiData.fraisLivraisonBase,
      logoFile: apiData.logoFile || '',
      adresses: apiData.adresses || []
    };
  }

  /**
   * Charger depuis localStorage en fallback
   */
  private loadFromLocalStorage(): void {
    // Essayer d'abord les données complètes du profil fournisseur
    const storedProfile = localStorage.getItem('fournisseur_profile');
    const storedUser = localStorage.getItem('user');
    const currentUser = this.authService.getCurrentUser();

    if (storedProfile) {
      try {
        const profileData = JSON.parse(storedProfile);
        console.log('🎯 Données complètes du profil depuis localStorage:', profileData);

        // Utiliser directement les données complètes
        const fournisseur: Fournisseur = {
          id: profileData.id,
          email: profileData.email,
          nom: profileData.nom,
          prenom: profileData.prenom,
          phoneNumber: profileData.phoneNumber,
          role: profileData.role,
          dateNaissance: profileData.dateNaissance,
          dateInscription: profileData.dateInscription,
          derniereConnexion: profileData.derniereConnexion,
          estActif: profileData.estActif,
          matriculeFiscale: profileData.matriculeFiscale,
          raisonSociale: profileData.raisonSociale,
          description: profileData.description,
          ribMasque: profileData.ribMasque,
          codeBanque: profileData.codeBanque,
          commission: profileData.commission,
          delaiPreparationJours: profileData.delaiPreparationJours,
          fraisLivraisonBase: profileData.fraisLivraisonBase,
          logoFile: profileData.logoFile,
          adresses: profileData.adresses
        };

        console.log('✅ Profil créé depuis données complètes:', fournisseur);
        this.fournisseurInfo.set(fournisseur);
        this.populateForm(fournisseur);
        this.isLoading.set(false);
        return;
      } catch (error) {
        console.warn('⚠️ Erreur parsing fournisseur_profile:', error);
      }
    }

    // Fallback: essayer les données utilisateur de base
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        console.log('📦 Fallback: Données utilisateur depuis localStorage:', userData);

        // Créer un objet Fournisseur à partir des données localStorage
        const fournisseurFromStorage: Fournisseur = {
          id: userData.id || currentUser?.id || 0,
          email: userData.email || currentUser?.email || '',
          nom: userData.nom || currentUser?.nom || '',
          prenom: userData.prenom || currentUser?.prenom || '',
          phoneNumber: userData.phoneNumber || userData.telephone || '',
          role: userData.role || 'Fournisseur',
          dateNaissance: userData.dateNaissance || '',
          dateInscription: userData.dateInscription || new Date().toISOString(),
          derniereConnexion: userData.derniereConnexion || null,
          estActif: userData.estActif !== undefined ? userData.estActif : true,
          matriculeFiscale: userData.matriculeFiscale || '',
          raisonSociale: userData.raisonSociale || '',
          description: userData.description || '',
          ribMasque: userData.ribMasque || userData.rib || '',
          codeBanque: userData.codeBanque || '',
          commission: userData.commission || 0,
          delaiPreparationJours: userData.delaiPreparationJours || 3,
          fraisLivraisonBase: userData.fraisLivraisonBase || 5.00,
          logoFile: userData.logoFile || '',
          adresses: userData.adresses || []
        };

        console.log('✅ Profil créé depuis localStorage:', fournisseurFromStorage);
        this.fournisseurInfo.set(fournisseurFromStorage);
        this.populateForm(fournisseurFromStorage);
        this.isLoading.set(false);
        return;
      } catch (error) {
        console.warn('⚠️ Erreur parsing localStorage user:', error);
      }
    }

    // Fallback: essayer le service dashboard (qui utilise maintenant localStorage)
    console.log('🔄 Fallback: tentative via DashboardService...');
    this.dashboardService.getFournisseurInfo().subscribe({
      next: (fournisseur) => {
        if (fournisseur) {
          console.log('✅ Données du profil chargées via DashboardService:', fournisseur);
          this.fournisseurInfo.set(fournisseur);
          this.populateForm(fournisseur);
        } else {
          // Dernier fallback: créer un profil minimal depuis currentUser
          this.createMinimalProfile();
        }
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement du profil via DashboardService:', error);
        // Dernier fallback: créer un profil minimal
        this.createMinimalProfile();
        this.isLoading.set(false);
      }
    });
  }

  /**
   * Créer un profil minimal depuis les données disponibles
   */
  private createMinimalProfile(): void {
    const currentUser = this.authService.getCurrentUser();

    if (currentUser) {
      const minimalProfile: Fournisseur = {
        id: currentUser.id,
        email: currentUser.email,
        nom: currentUser.nom,
        prenom: currentUser.prenom,
        phoneNumber: (currentUser as any).phoneNumber || (currentUser as any).telephone || '',
        role: currentUser.role || 'Fournisseur',
        dateNaissance: '',
        dateInscription: new Date().toISOString(),
        derniereConnexion: null,
        estActif: true,
        matriculeFiscale: '',
        raisonSociale: '',
        description: '',
        ribMasque: '',
        codeBanque: '',
        commission: 0,
        delaiPreparationJours: 3,
        fraisLivraisonBase: 5.00,
        logoFile: '',
        adresses: []
      };

      console.log('✅ Profil minimal créé:', minimalProfile);
      this.fournisseurInfo.set(minimalProfile);
      this.populateForm(minimalProfile);
    } else {
      this.error.set('Aucune donnée utilisateur disponible. Veuillez vous reconnecter.');
    }
  }

  /**
   * Remplir le formulaire avec les données du fournisseur
   */
  private populateForm(fournisseur: Fournisseur): void {
    console.log('📝 Remplissage du formulaire avec:', fournisseur);

    this.profileForm.patchValue({
      nom: fournisseur.nom || '',
      prenom: fournisseur.prenom || '',
      email: fournisseur.email || '',
      phoneNumber: fournisseur.phoneNumber || '',
      dateNaissance: fournisseur.dateNaissance ? fournisseur.dateNaissance.split('T')[0] : '',
      raisonSociale: fournisseur.raisonSociale || '',
      matriculeFiscale: fournisseur.matriculeFiscale || '',
      description: fournisseur.description || '',
      ribMasque: fournisseur.ribMasque || 'Non renseigné',
      codeBanque: fournisseur.codeBanque || 'Non renseigné',
      commission: fournisseur.commission || 0,
      delaiPreparationJours: fournisseur.delaiPreparationJours || 3,
      fraisLivraisonBase: fournisseur.fraisLivraisonBase || 5.00
    });

    // Initialiser les adresses
    this.initAdresses(fournisseur.adresses || []);

    console.log('✅ Formulaire rempli avec les valeurs:', this.profileForm.value);
  }

  /**
   * Initialiser les adresses dans le FormArray
   */
  private initAdresses(adresses: AdresseDto[]): void {
    const adressesArray = this.adresses;
    adressesArray.clear();

    adresses.forEach(adresse => {
      adressesArray.push(this.createAdresseFormGroup(adresse));
    });
  }

  /**
   * Créer un FormGroup pour une adresse
   */
  private createAdresseFormGroup(adresse?: AdresseDto): FormGroup {
    return this.fb.group({
      id: [adresse?.id || 0],
      rue: [adresse?.rue || '', Validators.required],
      ville: [adresse?.ville || '', Validators.required],
      codePostal: [adresse?.codePostal || '', [Validators.required, Validators.pattern(/^[0-9]{4}$/)]],
      pays: [adresse?.pays || 'Tunisie', Validators.required],
      estPrincipale: [adresse?.estPrincipale || false]
    });
  }

  /**
   * Ajouter une nouvelle adresse
   */
  addAdresse(): void {
    const newIndex = this.adresses.length;
    this.adresses.push(this.createAdresseFormGroup());
    this.editingAddresses.add(newIndex);
  }

  /**
   * Supprimer une adresse
   */
  removeAdresse(index: number): void {
    this.adresses.removeAt(index);
    this.editingAddresses.delete(index);

    // Réajuster les indices des adresses en édition
    const newEditingAddresses = new Set<number>();
    this.editingAddresses.forEach(editIndex => {
      if (editIndex > index) {
        newEditingAddresses.add(editIndex - 1);
      } else if (editIndex < index) {
        newEditingAddresses.add(editIndex);
      }
    });
    this.editingAddresses = newEditingAddresses;
  }

  /**
   * Basculer le mode édition d'une adresse
   */
  toggleEditAddress(index: number): void {
    if (this.editingAddresses.has(index)) {
      this.editingAddresses.delete(index);
    } else {
      this.editingAddresses.add(index);
    }
  }

  /**
   * Vérifier si une adresse est en mode édition
   */
  isEditingAddress(index: number): boolean {
    return this.editingAddresses.has(index);
  }

  /**
   * Définir une adresse comme principale
   */
  setAsMain(index: number): void {
    // Désactiver toutes les autres adresses principales
    for (let i = 0; i < this.adresses.length; i++) {
      this.adresses.at(i).get('estPrincipale')?.setValue(i === index);
    }
  }

  /**
   * Basculer l'affichage des autres adresses
   */
  toggleOtherAddresses(): void {
    this.showOtherAddresses = !this.showOtherAddresses;
  }

  /**
   * Activer le mode édition
   */
  enableEdit(): void {
    this.isEditing.set(true);
    this.successMessage.set(null);
    this.error.set(null);
  }

  /**
   * Annuler l'édition
   */
  cancelEdit(): void {
    this.isEditing.set(false);
    const fournisseur = this.fournisseurInfo();
    if (fournisseur) {
      this.populateForm(fournisseur);
    }
    this.error.set(null);
  }

  /**
   * Sauvegarder les modifications
   */
  saveProfile(): void {
    if (this.profileForm.valid) {
      this.isSaving.set(true);
      this.error.set(null);

      const formData = this.profileForm.value;
      console.log('💾 Sauvegarde du profil:', formData);

      // TODO: Implémenter l'appel API pour mettre à jour le profil
      // Pour l'instant, simulation d'une sauvegarde
      setTimeout(() => {
        this.isSaving.set(false);
        this.isEditing.set(false);
        this.successMessage.set('Profil mis à jour avec succès !');
        
        // Masquer le message de succès après 3 secondes
        setTimeout(() => {
          this.successMessage.set(null);
        }, 3000);
      }, 1000);
    } else {
      this.error.set('Veuillez corriger les erreurs dans le formulaire');
      this.markFormGroupTouched();
    }
  }

  /**
   * Marquer tous les champs comme touchés pour afficher les erreurs
   */
  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach(key => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Vérifier si un champ a une erreur
   */
  hasFieldError(fieldName: string): boolean {
    const field = this.profileForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  /**
   * Obtenir le message d'erreur pour un champ
   */
  getFieldError(fieldName: string): string {
    const field = this.profileForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) return 'Ce champ est obligatoire';
      if (field.errors['email']) return 'Email invalide';
      if (field.errors['minlength']) return `Minimum ${field.errors['minlength'].requiredLength} caractères`;
      if (field.errors['pattern']) return 'Format invalide';
      if (field.errors['min']) return `Valeur minimum: ${field.errors['min'].min}`;
    }
    return '';
  }

  /**
   * Formater la date d'inscription
   */
  formatDate(dateString: string): string {
    if (!dateString) return 'Non renseigné';
    return new Date(dateString).toLocaleDateString('fr-FR');
  }

  /**
   * Formater le statut
   */
  getStatusText(estActif: boolean): string {
    return estActif ? 'Actif' : 'Inactif';
  }

  /**
   * Obtenir la classe CSS pour le statut
   */
  getStatusClass(estActif: boolean): string {
    return estActif ? 'status-active' : 'status-inactive';
  }

  /**
   * Formater la commission
   */
  formatCommission(commission: number): string {
    return `${commission}%`;
  }

  /**
   * Formater les frais de livraison
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  }

  /**
   * Rafraîchir les données du profil depuis l'API
   */
  refreshProfile(): void {
    console.log('🔄 Rafraîchissement du profil depuis l\'API...');
    // Forcer le rechargement depuis l'API
    this.isLoading.set(true);
    this.error.set(null);

    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.id) {
      this.fournisseurService.getById(currentUser.id).subscribe({
        next: (response) => {
          console.log('✅ Profil rafraîchi depuis l\'API:', response);
          this.handleApiResponse(response);
        },
        error: (error) => {
          console.error('❌ Erreur lors du rafraîchissement:', error);
          this.error.set('Erreur lors du rafraîchissement du profil');
          this.isLoading.set(false);
        }
      });
    } else {
      this.error.set('Utilisateur non connecté');
      this.isLoading.set(false);
    }
  }

  /**
   * Debug: afficher toutes les données disponibles
   */
  debugProfile(): void {
    console.log('🐛 DEBUG PROFIL COMPLET:');
    console.log('📦 localStorage:');
    console.log('  - token:', !!localStorage.getItem('token'));
    console.log('  - user:', localStorage.getItem('user'));
    console.log('  - fournisseur_profile:', localStorage.getItem('fournisseur_profile'));
    console.log('  - supplierId:', localStorage.getItem('supplierId'));
    console.log('👤 AuthService:');
    console.log('  - currentUser:', this.authService.getCurrentUser());
    console.log('  - isAuthenticated:', this.authService.isAuthenticated());
    console.log('🏪 Composant:');
    console.log('  - fournisseurInfo:', this.fournisseurInfo());
    console.log('  - form values:', this.profileForm.value);
    console.log('  - isLoading:', this.isLoading());
    console.log('  - error:', this.error());
    console.log('📊 Statistiques localStorage:');
    console.log('  - Nombre de clés:', Object.keys(localStorage).length);
    console.log('  - Toutes les clés:', Object.keys(localStorage));
  }

  /**
   * Forcer la création du profil depuis localStorage uniquement
   */
  loadFromLocalStorageOnly(): void {
    console.log('🔄 Chargement forcé depuis localStorage uniquement...');
    this.isLoading.set(true);
    this.error.set(null);

    // Essayer d'abord les données complètes
    const storedProfile = localStorage.getItem('fournisseur_profile');
    if (storedProfile) {
      try {
        const profileData = JSON.parse(storedProfile);
        console.log('🎯 Données complètes trouvées:', profileData);

        const fournisseur: Fournisseur = {
          id: profileData.id || 0,
          email: profileData.email || '',
          nom: profileData.nom || '',
          prenom: profileData.prenom || '',
          phoneNumber: profileData.phoneNumber || '',
          role: profileData.role || 'Fournisseur',
          dateNaissance: profileData.dateNaissance || '',
          dateInscription: profileData.dateInscription || new Date().toISOString(),
          derniereConnexion: profileData.derniereConnexion || null,
          estActif: profileData.estActif !== undefined ? profileData.estActif : true,
          matriculeFiscale: profileData.matriculeFiscale || '',
          raisonSociale: profileData.raisonSociale || '',
          description: profileData.description || '',
          ribMasque: profileData.ribMasque || 'Non renseigné',
          codeBanque: profileData.codeBanque || 'Non renseigné',
          commission: profileData.commission || 0,
          delaiPreparationJours: profileData.delaiPreparationJours || 3,
          fraisLivraisonBase: profileData.fraisLivraisonBase || 5.00,
          logoFile: profileData.logoFile || '',
          adresses: profileData.adresses || []
        };

        console.log('✅ Profil créé depuis données complètes:', fournisseur);
        this.fournisseurInfo.set(fournisseur);
        this.populateForm(fournisseur);
        this.isLoading.set(false);
        return;
      } catch (error) {
        console.error('❌ Erreur parsing fournisseur_profile:', error);
      }
    }

    // Fallback: données utilisateur de base
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        console.log('📦 Données localStorage:', userData);

        const fournisseur: Fournisseur = {
          id: userData.id || 0,
          email: userData.email || '',
          nom: userData.nom || '',
          prenom: userData.prenom || '',
          phoneNumber: userData.phoneNumber || userData.telephone || '',
          role: userData.role || 'Fournisseur',
          dateNaissance: userData.dateNaissance || '',
          dateInscription: userData.dateInscription || new Date().toISOString(),
          derniereConnexion: userData.derniereConnexion || null,
          estActif: userData.estActif !== undefined ? userData.estActif : true,
          matriculeFiscale: userData.matriculeFiscale || '',
          raisonSociale: userData.raisonSociale || '',
          description: userData.description || '',
          ribMasque: userData.ribMasque || userData.rib || 'Non renseigné',
          codeBanque: userData.codeBanque || 'Non renseigné',
          commission: userData.commission || 0,
          delaiPreparationJours: userData.delaiPreparationJours || 3,
          fraisLivraisonBase: userData.fraisLivraisonBase || 5.00,
          logoFile: userData.logoFile || '',
          adresses: userData.adresses || []
        };

        console.log('✅ Profil créé depuis localStorage:', fournisseur);
        this.fournisseurInfo.set(fournisseur);
        this.populateForm(fournisseur);
        this.isLoading.set(false);
      } catch (error) {
        console.error('❌ Erreur parsing localStorage:', error);
        this.error.set('Erreur lors de la lecture des données localStorage');
        this.isLoading.set(false);
      }
    } else {
      this.error.set('Aucune donnée utilisateur trouvée dans localStorage');
      this.isLoading.set(false);
    }
  }
}
