using System.ComponentModel.DataAnnotations;
using WebApiPfe.Models;

namespace WebApiPfe.DTOs.ReclamationDTOs
{
    public class CreateReclamationDto
    {
        [Required]
        public int CommandeId { get; set; }

        [Required]
        [StringLength(100, ErrorMessage = "L'objet ne peut pas dépasser 100 caractères")]
        public string Objet { get; set; } = string.Empty;

        [Required]
        [StringLength(1000, ErrorMessage = "La description ne peut pas dépasser 1000 caractères")]
        public string Description { get; set; } = string.Empty;

        [Required]
        public TypeReclamation Type { get; set; }

        public List<IFormFile>? PiecesJointes { get; set; }
    }

    public class UpdateReclamationDto
    {
        [StringLength(1000)]
        public string? ReponseAdmin { get; set; }

        [StringLength(1000)]
        public string? ReponseFournisseur { get; set; }

        public StatutReclamation? Statut { get; set; }

        [StringLength(500)]
        public string? Commentaire { get; set; }
    }

    public class ReclamationReadDto
    {
        public int Id { get; set; }
        public int CommandeId { get; set; }
        public string NumeroCommande { get; set; } = string.Empty;
        public int ClientId { get; set; }
        public string NomClient { get; set; } = string.Empty;
        public string EmailClient { get; set; } = string.Empty;
        public string Objet { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public TypeReclamation Type { get; set; }
        public string TypeLibelle { get; set; } = string.Empty;
        public StatutReclamation Statut { get; set; }
        public string StatutLibelle { get; set; } = string.Empty;
        public DateTime DateCreation { get; set; }
        public DateTime? DateTraitement { get; set; }
        public DateTime? DateResolution { get; set; }
        public string? ReponseAdmin { get; set; }
        public string? ReponseFournisseur { get; set; }
        public string? NomTraiteur { get; set; }
        public List<string> PiecesJointes { get; set; } = new List<string>();
        public List<HistoriqueReclamationDto> Historique { get; set; } = new List<HistoriqueReclamationDto>();
    }

    public class HistoriqueReclamationDto
    {
        public int Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public string? Commentaire { get; set; }
        public DateTime DateAction { get; set; }
        public string NomUtilisateur { get; set; } = string.Empty;
        public string RoleUtilisateur { get; set; } = string.Empty;
    }

    public class ReclamationStatsDto
    {
        public int TotalReclamations { get; set; }
        public int ReclamationsEnAttente { get; set; }
        public int ReclamationsEnCours { get; set; }
        public int ReclamationsResolues { get; set; }
        public int ReclamationsFermees { get; set; }
        public double TauxResolution { get; set; }
        public double DelaiMoyenResolution { get; set; }
        public Dictionary<TypeReclamation, int> ReclamationsParType { get; set; } = new Dictionary<TypeReclamation, int>();
        public List<ReclamationReadDto> ReclamationsRecentes { get; set; } = new List<ReclamationReadDto>();
    }

    public class ReclamationFilterDto
    {
        public StatutReclamation? Statut { get; set; }
        public TypeReclamation? Type { get; set; }
        public DateTime? DateDebut { get; set; }
        public DateTime? DateFin { get; set; }
        public int? ClientId { get; set; }
        public int? FournisseurId { get; set; }
        public string? Recherche { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string SortBy { get; set; } = "DateCreation";
        public bool SortDesc { get; set; } = true;
    }
}
