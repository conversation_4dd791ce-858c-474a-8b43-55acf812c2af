using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [EnableCors("AllowSpecificOrigin")]
    [Authorize]
    public class DemandesController : ControllerBase
    {
        private readonly IDemandeService _demandeService;

        public DemandesController(IDemandeService demandeService)
        {
            _demandeService = demandeService;
        }

        #region Demandes de catégories

        [HttpPost("categories")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<IActionResult> CreerDemandeCategorie([FromBody] CreateDemandeCategorieDto dto)
        {
            try
            {
                var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var demande = await _demandeService.CreerDemandeCategorieAsync(dto, fournisseurId);
                return Ok(demande);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpGet("categories")]
        public async Task<IActionResult> GetDemandesCategories([FromQuery] int? fournisseurId = null)
        {
            try
            {
                // Si c'est un fournisseur, ne montrer que ses demandes
                if (User.IsInRole("Fournisseur"))
                {
                    fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                }

                var demandes = await _demandeService.GetDemandesCategoriesAsync(fournisseurId);
                return Ok(demandes);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpGet("categories/{id}")]
        public async Task<IActionResult> GetDemandeCategorie(int id)
        {
            try
            {
                var demande = await _demandeService.GetDemandeCategorieByIdAsync(id);
                if (demande == null)
                    return NotFound();

                // Vérifier les permissions
                if (User.IsInRole("Fournisseur"))
                {
                    var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                    if (demande.FournisseurId != fournisseurId)
                        return Forbid();
                }

                return Ok(demande);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPut("categories/{id}/traiter")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> TraiterDemandeCategorie(int id, [FromBody] TraiterDemandeDto dto)
        {
            try
            {
                var adminId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var demande = await _demandeService.TraiterDemandeCategorieAsync(id, dto, adminId);
                return Ok(demande);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        #endregion

        #region Demandes de sous-catégories

        [HttpPost("sous-categories")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<IActionResult> CreerDemandeSousCategorie([FromBody] CreateDemandeSousCategorieDto dto)
        {
            try
            {
                var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var demande = await _demandeService.CreerDemandeSousCategorieAsync(dto, fournisseurId);
                return Ok(demande);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpGet("sous-categories")]
        public async Task<IActionResult> GetDemandesSousCategories([FromQuery] int? fournisseurId = null)
        {
            try
            {
                // Si c'est un fournisseur, ne montrer que ses demandes
                if (User.IsInRole("Fournisseur"))
                {
                    fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                }

                var demandes = await _demandeService.GetDemandesSousCategoriesAsync(fournisseurId);
                return Ok(demandes);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpGet("sous-categories/{id}")]
        public async Task<IActionResult> GetDemandeSousCategorie(int id)
        {
            try
            {
                var demande = await _demandeService.GetDemandeSousCategorieByIdAsync(id);
                if (demande == null)
                    return NotFound();

                // Vérifier les permissions
                if (User.IsInRole("Fournisseur"))
                {
                    var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                    if (demande.FournisseurId != fournisseurId)
                        return Forbid();
                }

                return Ok(demande);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPut("sous-categories/{id}/traiter")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> TraiterDemandeSousCategorie(int id, [FromBody] TraiterDemandeDto dto)
        {
            try
            {
                var adminId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var demande = await _demandeService.TraiterDemandeSousCategorieAsync(id, dto, adminId);
                return Ok(demande);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        #endregion

        #region Statistiques

        [HttpGet("statistiques/en-attente")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetNombreDemandesEnAttente()
        {
            try
            {
                var nombre = await _demandeService.GetNombreDemandesEnAttenteAsync();
                return Ok(new { nombreDemandesEnAttente = nombre });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        #endregion
    }
}
