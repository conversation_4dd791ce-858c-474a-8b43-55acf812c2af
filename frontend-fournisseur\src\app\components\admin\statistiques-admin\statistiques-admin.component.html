<div class="statistiques-admin-container">
  <div class="header-section">
    <h2><i class="bi bi-graph-up"></i> Statistiques Générales</h2>
    <p class="text-muted">Tableau de bord des performances de la plateforme</p>
    
    <div class="header-actions">
      <div class="periode-selector">
        <select class="form-select" [(ngModel)]="filter.periode" (change)="onPeriodeChange()">
          <option *ngFor="let periode of periodesPredefinies" [value]="periode.value">
            {{ periode.label }}
          </option>
        </select>
      </div>
      
      <div class="export-buttons">
        <div class="btn-group">
          <button class="btn btn-outline-primary btn-sm" (click)="exportStatistiques('pdf')">
            <i class="bi bi-file-pdf"></i> PDF
          </button>
          <button class="btn btn-outline-success btn-sm" (click)="exportStatistiques('excel')">
            <i class="bi bi-file-excel"></i> Excel
          </button>
          <button class="btn btn-outline-secondary btn-sm" (click)="exportStatistiques('csv')">
            <i class="bi bi-file-csv"></i> CSV
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger">
    <i class="bi bi-exclamation-triangle"></i> {{ error }}
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
  </div>

  <!-- Contenu principal -->
  <div *ngIf="!loading && stats" class="stats-content">
    <!-- KPIs principaux -->
    <div class="kpis-section mb-4">
      <div class="row g-3">
        <div class="col-md-3">
          <div class="kpi-card">
            <div class="kpi-icon">
              <i class="bi bi-currency-euro"></i>
            </div>
            <div class="kpi-content">
              <h3>{{ formatMontant(stats.totalVentes) }}</h3>
              <p>Chiffre d'affaires</p>
              <div class="kpi-evolution" [class]="'text-' + getEvolutionColor(stats.evolutionVentes)">
                <i class="bi" [class]="'bi-' + getEvolutionIcon(stats.evolutionVentes)"></i>
                {{ formatEvolution(stats.evolutionVentes) }}
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="kpi-card">
            <div class="kpi-icon">
              <i class="bi bi-cart"></i>
            </div>
            <div class="kpi-content">
              <h3>{{ formatNombre(stats.totalCommandes) }}</h3>
              <p>Commandes</p>
              <div class="kpi-evolution" [class]="'text-' + getEvolutionColor(stats.evolutionCommandes)">
                <i class="bi" [class]="'bi-' + getEvolutionIcon(stats.evolutionCommandes)"></i>
                {{ formatEvolution(stats.evolutionCommandes) }}
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="kpi-card">
            <div class="kpi-icon">
              <i class="bi bi-people"></i>
            </div>
            <div class="kpi-content">
              <h3>{{ formatNombre(stats.totalClients) }}</h3>
              <p>Clients</p>
              <div class="kpi-evolution" [class]="'text-' + getEvolutionColor(stats.evolutionClients)">
                <i class="bi" [class]="'bi-' + getEvolutionIcon(stats.evolutionClients)"></i>
                {{ formatEvolution(stats.evolutionClients) }}
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="kpi-card">
            <div class="kpi-icon">
              <i class="bi bi-box"></i>
            </div>
            <div class="kpi-content">
              <h3>{{ formatNombre(stats.totalProduits) }}</h3>
              <p>Produits</p>
              <div class="kpi-evolution text-info">
                <i class="bi bi-info-circle"></i>
                Total actif
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Métriques secondaires -->
    <div class="secondary-metrics mb-4">
      <div class="row g-3">
        <div class="col-md-4">
          <div class="metric-card">
            <h6><i class="bi bi-basket"></i> Panier moyen</h6>
            <div class="metric-value">{{ formatMontant(stats.panierMoyen) }}</div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="metric-card">
            <h6><i class="bi bi-receipt"></i> Commande moyenne</h6>
            <div class="metric-value">{{ formatMontant(stats.commandeMoyenne) }}</div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="metric-card">
            <h6><i class="bi bi-percent"></i> Taux de conversion</h6>
            <div class="metric-value">{{ getTauxConversion() }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Onglets -->
    <div class="tabs-section">
      <ul class="nav nav-tabs">
        <li class="nav-item">
          <button class="nav-link" [class.active]="activeTab === 'overview'" (click)="onTabChange('overview')">
            <i class="bi bi-speedometer2"></i> Vue d'ensemble
          </button>
        </li>
        <li class="nav-item">
          <button class="nav-link" [class.active]="activeTab === 'ventes'" (click)="onTabChange('ventes')">
            <i class="bi bi-graph-up"></i> Évolution des ventes
          </button>
        </li>
        <li class="nav-item">
          <button class="nav-link" [class.active]="activeTab === 'produits'" (click)="onTabChange('produits')">
            <i class="bi bi-trophy"></i> Top produits
          </button>
        </li>
        <li class="nav-item">
          <button class="nav-link" [class.active]="activeTab === 'commandes'" (click)="onTabChange('commandes')">
            <i class="bi bi-cart-check"></i> Commandes
          </button>
        </li>
      </ul>
    </div>

    <!-- Contenu des onglets -->
    <div class="tab-content">
      <!-- Onglet Vue d'ensemble -->
      <div *ngIf="activeTab === 'overview'" class="tab-pane">
        <div class="row g-4">
          <!-- Graphique évolution ventes -->
          <div class="col-md-8">
            <div class="card">
              <div class="card-header">
                <h5><i class="bi bi-graph-up"></i> Évolution du chiffre d'affaires</h5>
              </div>
              <div class="card-body">
                <div *ngIf="evolutionVentes.length === 0" class="text-center text-muted py-4">
                  <i class="bi bi-graph-up fs-1"></i>
                  <p class="mt-2">Aucune donnée disponible</p>
                </div>
                <div *ngIf="evolutionVentes.length > 0" class="chart-container">
                  <div *ngFor="let vente of evolutionVentes" class="chart-bar">
                    <div class="bar-container">
                      <div class="bar" 
                           [style.height.%]="getVentesPercentage(vente.montant)"
                           [title]="formatMontant(vente.montant)">
                      </div>
                    </div>
                    <div class="bar-label">{{ formatPeriodeLabel(vente.periode) }}</div>
                    <div class="bar-value">{{ formatMontant(vente.montant) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Top produits résumé -->
          <div class="col-md-4">
            <div class="card">
              <div class="card-header">
                <h5><i class="bi bi-trophy"></i> Top 5 produits</h5>
              </div>
              <div class="card-body">
                <div *ngIf="topProduits.length === 0" class="text-center text-muted py-3">
                  <i class="bi bi-trophy"></i>
                  <p class="mt-2">Aucun produit</p>
                </div>
                <div *ngFor="let produit of topProduits.slice(0, 5); let i = index" class="top-product-item">
                  <div class="rank-badge">{{ i + 1 }}</div>
                  <div class="product-info">
                    <h6>{{ produit.nom }}</h6>
                    <small class="text-muted">{{ produit.quantiteVendue }} vendus</small>
                  </div>
                  <div class="product-value">
                    {{ formatMontant(produit.chiffreAffaires) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Onglet Évolution des ventes -->
      <div *ngIf="activeTab === 'ventes'" class="tab-pane">
        <div class="card">
          <div class="card-header">
            <h5><i class="bi bi-graph-up"></i> Détail de l'évolution des ventes</h5>
          </div>
          <div class="card-body">
            <div *ngIf="evolutionVentes.length === 0" class="text-center text-muted py-5">
              <i class="bi bi-graph-up fs-1"></i>
              <p class="mt-2">Aucune donnée de vente disponible</p>
            </div>
            <div *ngIf="evolutionVentes.length > 0" class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Période</th>
                    <th>Chiffre d'affaires</th>
                    <th>Nombre de commandes</th>
                    <th>Nombre de clients</th>
                    <th>Panier moyen</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let vente of evolutionVentes">
                    <td>{{ formatPeriodeLabel(vente.periode) }}</td>
                    <td><strong>{{ formatMontant(vente.montant) }}</strong></td>
                    <td>{{ formatNombre(vente.nombreCommandes) }}</td>
                    <td>{{ formatNombre(vente.nombreClients) }}</td>
                    <td>{{ formatMontant(vente.nombreCommandes > 0 ? vente.montant / vente.nombreCommandes : 0) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Onglet Top produits -->
      <div *ngIf="activeTab === 'produits'" class="tab-pane">
        <div class="card">
          <div class="card-header">
            <h5><i class="bi bi-trophy"></i> Classement des produits</h5>
          </div>
          <div class="card-body">
            <div *ngIf="topProduits.length === 0" class="text-center text-muted py-5">
              <i class="bi bi-trophy fs-1"></i>
              <p class="mt-2">Aucun produit trouvé</p>
            </div>
            <div *ngIf="topProduits.length > 0" class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Rang</th>
                    <th>Produit</th>
                    <th>Fournisseur</th>
                    <th>Quantité vendue</th>
                    <th>Chiffre d'affaires</th>
                    <th>Nb commandes</th>
                    <th>Note moyenne</th>
                    <th>Stock</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let produit of topProduits; let i = index">
                    <td>
                      <span class="rank-badge" [class]="i < 3 ? 'rank-top' : ''">{{ i + 1 }}</span>
                    </td>
                    <td>
                      <div>
                        <strong>{{ produit.nom }}</strong>
                        <br><small class="text-muted">Réf: {{ produit.reference }}</small>
                      </div>
                    </td>
                    <td>{{ produit.fournisseurNom || 'N/A' }}</td>
                    <td><strong>{{ formatNombre(produit.quantiteVendue) }}</strong></td>
                    <td><strong>{{ formatMontant(produit.chiffreAffaires) }}</strong></td>
                    <td>{{ formatNombre(produit.nombreCommandes) }}</td>
                    <td>
                      <span class="badge bg-warning">{{ produit.noteMoyenne | number:'1.1-1' }}/5</span>
                    </td>
                    <td>
                      <span class="badge" [class]="produit.stock > 10 ? 'bg-success' : produit.stock > 0 ? 'bg-warning' : 'bg-danger'">
                        {{ produit.stock }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Onglet Commandes -->
      <div *ngIf="activeTab === 'commandes'" class="tab-pane">
        <div class="card">
          <div class="card-header">
            <h5><i class="bi bi-cart-check"></i> Évolution des commandes</h5>
          </div>
          <div class="card-body">
            <div *ngIf="evolutionCommandes.length === 0" class="text-center text-muted py-5">
              <i class="bi bi-cart-check fs-1"></i>
              <p class="mt-2">Aucune donnée de commande disponible</p>
            </div>
            <div *ngIf="evolutionCommandes.length > 0">
              <!-- Graphique en barres -->
              <div class="chart-container mb-4">
                <div *ngFor="let commande of evolutionCommandes" class="chart-bar">
                  <div class="bar-container">
                    <div class="bar bg-info" 
                         [style.height.%]="getCommandesPercentage(commande.nombreCommandes)"
                         [title]="commande.nombreCommandes + ' commandes'">
                    </div>
                  </div>
                  <div class="bar-label">{{ commande.date | date:'dd/MM' }}</div>
                  <div class="bar-value">{{ commande.nombreCommandes }}</div>
                </div>
              </div>
              
              <!-- Tableau détaillé -->
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Nombre de commandes</th>
                      <th>Montant total</th>
                      <th>Montant moyen</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let commande of evolutionCommandes">
                      <td>{{ commande.date | date:'dd/MM/yyyy' }}</td>
                      <td><strong>{{ formatNombre(commande.nombreCommandes) }}</strong></td>
                      <td><strong>{{ formatMontant(commande.montantTotal) }}</strong></td>
                      <td>{{ formatMontant(commande.nombreCommandes > 0 ? commande.montantTotal / commande.nombreCommandes : 0) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
