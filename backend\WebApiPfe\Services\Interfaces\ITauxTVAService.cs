﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Services.Interfaces
{
    public interface ITauxTVAService
    {
        Task<TauxTVA> GetByIdAsync(int id);
        Task<IEnumerable<TauxTVA>> GetAllAsync(bool inclureInactifs = false);
        Task<TauxTVA> CreateAsync(TauxTVA taux);
        Task UpdateAsync(TauxTVA taux);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<Dictionary<int, string>> GetTauxForDropdownAsync();
        Task<bool> IsTauxUniqueAsync(decimal taux, DateTime dateEffet, int? ignoreId = null);
        Task<decimal> CalculerTTCAsync(int tauxId, decimal prixHT);
        Task<decimal> CalculerHTAsync(int tauxId, decimal prixTTC);
        Task<TauxTVA> GetTauxActuelAsync();
        Task<IEnumerable<TauxTVA>> GetByCategorieAsync(int categorieId);
        Task<Dictionary<int, string>> GetTauxForDropdownByCategorieAsync(int categorieId);
    }
}
