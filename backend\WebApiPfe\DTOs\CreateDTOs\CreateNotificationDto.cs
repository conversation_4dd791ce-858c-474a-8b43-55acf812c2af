﻿using System.ComponentModel.DataAnnotations;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class CreateNotificationDto
    {
        [Required]
        [StringLength(200, MinimumLength = 3)]
        public string Titre { get; set; } = string.Empty;

        [Required]
        [StringLength(500, MinimumLength = 5)]
        public string Message { get; set; } = string.Empty;

        [Required]
        public TypeNotification Type { get; set; }

        [Required]
        public int UtilisateurId { get; set; }
    }
}
