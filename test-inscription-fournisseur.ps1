# Test d'inscription fournisseur
$uri = "http://localhost:5014/api/Auth/register/fournisseur"

# Creer un fichier temporaire pour le logo
$logoPath = "test-logo.txt"
"Test Logo Content" | Out-File -FilePath $logoPath -Encoding UTF8

# Preparer les donnees du formulaire
$form = @{
    nom = "Dupont"
    prenom = "Jean"
    email = "<EMAIL>"
    password = "Test123!"
    raisonSociale = "Optique Dupont SARL"
    matriculeFiscale = "123456789"
    commission = "0.75"
    rue = "123 Avenue de la Republique"
    ville = "Tunis"
    codePostal = "1000"
    pays = "Tunisie"
    logoFile = Get-Item $logoPath
}

try {
    Write-Host "Test d'inscription fournisseur..."
    Write-Host "URL: $uri"

    $response = Invoke-RestMethod -Uri $uri -Method POST -Form $form

    Write-Host "Inscription reussie!"
    Write-Host "Reponse:"
    $response | ConvertTo-Json -Depth 3

} catch {
    Write-Host "Erreur lors de l'inscription:"
    Write-Host $_.Exception.Message

    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Details de l'erreur:"
        Write-Host $responseBody
    }
} finally {
    # Nettoyer le fichier temporaire
    if (Test-Path $logoPath) {
        Remove-Item $logoPath
    }
}
