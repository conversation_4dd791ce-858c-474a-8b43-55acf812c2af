<div class="profile-container">
  <!-- Header -->
  <div class="profile-header">
    <div class="header-content">
      <div class="header-text">
        <h1 class="profile-title">
          <span class="title-icon">👤</span>
          Mon Profil
        </h1>
        <p class="profile-subtitle">G<PERSON>rez vos informations personnelles et professionnelles</p>
      </div>
      <div class="header-actions">
        <div *ngIf="!isLoading() && fournisseurInfo()">
          <button
            *ngIf="!isEditing()"
            class="btn btn-primary"
            (click)="enableEdit()"
            type="button">
            <span>✏️</span> Modifier
          </button>
          <div *ngIf="isEditing()" class="edit-actions">
            <button
              class="btn btn-secondary"
              (click)="cancelEdit()"
              type="button"
              [disabled]="isSaving()">
              Annuler
            </button>
            <button
              class="btn btn-primary"
              (click)="saveProfile()"
              type="button"
              [disabled]="isSaving() || profileForm.invalid">
              <span *ngIf="isSaving()" class="spinner"></span>
              {{ isSaving() ? 'Sauvegarde...' : 'Sauvegarder' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Messages -->
  <div *ngIf="error()" class="alert alert-error">
    <span class="alert-icon">❌</span>
    {{ error() }}
  </div>

  <div *ngIf="successMessage()" class="alert alert-success">
    <span class="alert-icon">✅</span>
    {{ successMessage() }}
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-content">
      <div class="spinner large"></div>
      <p>Chargement de votre profil...</p>
    </div>
  </div>

  <!-- Profile Content -->
  <div *ngIf="!isLoading() && fournisseurInfo()" class="profile-content">
    <form [formGroup]="profileForm" class="profile-form">
      
      <!-- Informations personnelles -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">👨‍💼</span>
          Informations personnelles
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="prenom">Prénom *</label>
            <input
              type="text"
              id="prenom"
              formControlName="prenom"
              class="form-control"
              [class.is-invalid]="hasFieldError('prenom')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('prenom')">
              {{ getFieldError('prenom') }}
            </div>
          </div>

          <div class="form-group">
            <label for="nom">Nom *</label>
            <input
              type="text"
              id="nom"
              formControlName="nom"
              class="form-control"
              [class.is-invalid]="hasFieldError('nom')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('nom')">
              {{ getFieldError('nom') }}
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="email">Email *</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="form-control"
              [class.is-invalid]="hasFieldError('email')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('email')">
              {{ getFieldError('email') }}
            </div>
          </div>

          <div class="form-group">
            <label for="phoneNumber">Téléphone *</label>
            <input
              type="tel"
              id="phoneNumber"
              formControlName="phoneNumber"
              class="form-control"
              [class.is-invalid]="hasFieldError('phoneNumber')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('phoneNumber')">
              {{ getFieldError('phoneNumber') }}
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="dateNaissance">Date de naissance</label>
          <input
            type="date"
            id="dateNaissance"
            formControlName="dateNaissance"
            class="form-control"
            [readonly]="!isEditing()"
          />
        </div>
      </div>

      <!-- Informations entreprise -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">🏢</span>
          Informations entreprise
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="raisonSociale">Raison sociale *</label>
            <input
              type="text"
              id="raisonSociale"
              formControlName="raisonSociale"
              class="form-control"
              [class.is-invalid]="hasFieldError('raisonSociale')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('raisonSociale')">
              {{ getFieldError('raisonSociale') }}
            </div>
          </div>

          <div class="form-group">
            <label for="matriculeFiscale">Matricule fiscal *</label>
            <input
              type="text"
              id="matriculeFiscale"
              formControlName="matriculeFiscale"
              class="form-control"
              [class.is-invalid]="hasFieldError('matriculeFiscale')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('matriculeFiscale')">
              {{ getFieldError('matriculeFiscale') }}
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="description">Description</label>
          <textarea
            id="description"
            formControlName="description"
            class="form-control"
            rows="4"
            [readonly]="!isEditing()"
            placeholder="Décrivez votre entreprise et vos activités..."
          ></textarea>
        </div>
      </div>

      <!-- Informations bancaires -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">🏦</span>
          Informations bancaires
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="ribMasque">RIB</label>
            <input
              type="text"
              id="ribMasque"
              formControlName="ribMasque"
              class="form-control"
              readonly
            />
            <small class="form-hint">Contactez l'administration pour modifier vos informations bancaires</small>
          </div>

          <div class="form-group">
            <label for="codeBanque">Code banque</label>
            <input
              type="text"
              id="codeBanque"
              formControlName="codeBanque"
              class="form-control"
              readonly
            />
          </div>
        </div>
      </div>

      <!-- Paramètres commerciaux -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">💼</span>
          Paramètres commerciaux
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="commission">Commission</label>
            <div class="input-group">
              <input
                type="number"
                id="commission"
                formControlName="commission"
                class="form-control"
                readonly
              />
              <span class="input-suffix">%</span>
            </div>
            <small class="form-hint">Taux de commission défini par l'administration</small>
          </div>

          <div class="form-group">
            <label for="delaiPreparationJours">Délai de préparation *</label>
            <div class="input-group">
              <input
                type="number"
                id="delaiPreparationJours"
                formControlName="delaiPreparationJours"
                class="form-control"
                [class.is-invalid]="hasFieldError('delaiPreparationJours')"
                [readonly]="!isEditing()"
                min="1"
              />
              <span class="input-suffix">jours</span>
            </div>
            <div class="invalid-feedback" *ngIf="hasFieldError('delaiPreparationJours')">
              {{ getFieldError('delaiPreparationJours') }}
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="fraisLivraisonBase">Frais de livraison de base *</label>
          <div class="input-group">
            <input
              type="number"
              id="fraisLivraisonBase"
              formControlName="fraisLivraisonBase"
              class="form-control"
              [class.is-invalid]="hasFieldError('fraisLivraisonBase')"
              [readonly]="!isEditing()"
              min="0"
              step="0.01"
            />
            <span class="input-suffix">DT</span>
          </div>
          <div class="invalid-feedback" *ngIf="hasFieldError('fraisLivraisonBase')">
            {{ getFieldError('fraisLivraisonBase') }}
          </div>
        </div>
      </div>

      <!-- Section Adresses -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">📍</span>
          Adresses
          <button
            type="button"
            class="btn btn-sm btn-outline-primary ms-auto"
            (click)="addAdresse()"
            *ngIf="isEditing()"
          >
            <span>➕</span> Ajouter une adresse
          </button>
        </h3>

        <div formArrayName="adresses">
          <div *ngFor="let adresseCtrl of adresses.controls; let i = index"
               [formGroupName]="i"
               class="address-item">

            <!-- Mode affichage -->
            <div *ngIf="!isEditingAddress(i)" class="address-display">
              <div class="address-content">
                <span *ngIf="adresseCtrl.get('rue')?.value" class="address-text">
                  {{ adresseCtrl.get('rue')?.value }},
                  {{ adresseCtrl.get('ville')?.value }},
                  {{ adresseCtrl.get('codePostal')?.value }},
                  {{ adresseCtrl.get('pays')?.value }}
                </span>
                <span *ngIf="!adresseCtrl.get('rue')?.value" class="no-address">
                  Adresse vide
                </span>
                <span *ngIf="adresseCtrl.get('estPrincipale')?.value" class="badge badge-primary ms-2">
                  Principale
                </span>
              </div>
              <div class="address-actions" *ngIf="isEditing()">
                <button
                  type="button"
                  class="btn btn-sm btn-outline-secondary"
                  (click)="toggleEditAddress(i)"
                >
                  ✏️ Modifier
                </button>
                <button
                  type="button"
                  class="btn btn-sm btn-outline-danger"
                  (click)="removeAdresse(i)"
                >
                  🗑️ Supprimer
                </button>
              </div>
            </div>

            <!-- Mode édition -->
            <div *ngIf="isEditingAddress(i)" class="address-edit">
              <div class="row">
                <div class="col-md-12 mb-3">
                  <label class="form-label">Rue *</label>
                  <input
                    type="text"
                    formControlName="rue"
                    class="form-control"
                    placeholder="Numéro et nom de rue"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">Ville *</label>
                  <input
                    type="text"
                    formControlName="ville"
                    class="form-control"
                    placeholder="Ville"
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">Code Postal *</label>
                  <input
                    type="text"
                    formControlName="codePostal"
                    class="form-control"
                    placeholder="1234"
                    maxlength="4"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">Pays *</label>
                  <input
                    type="text"
                    formControlName="pays"
                    class="form-control"
                    value="Tunisie"
                    readonly
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <div class="form-check">
                    <input
                      type="checkbox"
                      formControlName="estPrincipale"
                      (change)="setAsMain(i)"
                      class="form-check-input"
                      id="principal-{{i}}"
                    />
                    <label for="principal-{{i}}" class="form-check-label">
                      Adresse principale
                    </label>
                  </div>
                </div>
              </div>

              <div class="address-actions">
                <button
                  type="button"
                  class="btn btn-sm btn-success"
                  (click)="toggleEditAddress(i)"
                >
                  ✅ Valider
                </button>
              </div>
            </div>
          </div>

          <div *ngIf="adresses.length === 0" class="no-addresses">
            <p class="text-muted">Aucune adresse enregistrée</p>
            <button
              type="button"
              class="btn btn-outline-primary"
              (click)="addAdresse()"
              *ngIf="isEditing()"
            >
              <span>➕</span> Ajouter votre première adresse
            </button>
          </div>
        </div>
      </div>

      <!-- Informations système -->
      <div class="form-section" *ngIf="fournisseurInfo()">
        <h3 class="section-title">
          <span class="section-icon">⚙️</span>
          Informations système
        </h3>
        
        <div class="info-grid">
          <div class="info-item">
            <label>Statut du compte</label>
            <span class="badge" [ngClass]="getStatusClass(fournisseurInfo()!.estActif)">
              {{ getStatusText(fournisseurInfo()!.estActif) }}
            </span>
          </div>

          <div class="info-item">
            <label>Date d'inscription</label>
            <span>{{ formatDate(fournisseurInfo()!.dateInscription) }}</span>
          </div>

          <div class="info-item">
            <label>Dernière connexion</label>
            <span>{{ fournisseurInfo()!.derniereConnexion ? formatDate(fournisseurInfo()!.derniereConnexion!) : 'Jamais' }}</span>
          </div>          
        </div>
      </div>
    </form>
  </div>
</div>
