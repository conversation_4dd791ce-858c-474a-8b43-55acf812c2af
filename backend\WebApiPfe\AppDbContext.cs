﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models;
using WebApiPfe.Models.Enum;

namespace WebApiPfe
{
    public class AppDbContext : IdentityDbContext<Utilisateur, IdentityRole<int>, int> 
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options) { }

        public async Task<TEntity> FindOrThrowAsync<TEntity>(params object[] keyValues)
            where TEntity : class
        {
            var entity = await FindAsync<TEntity>(keyValues);
            return entity ?? throw new KeyNotFoundException(
                $"{typeof(TEntity).Name} avec clé {string.Join(",", keyValues)} introuvable");
        }

        // Déclaration des DbSets
        public DbSet<Produit> Produits { get; set; }
        public DbSet<Categorie> Categories { get; set; }
        public DbSet<SousCategorie> SousCategories { get; set; }
        public DbSet<Marque> Marques { get; set; }
        public DbSet<Forme> Formes { get; set; }
        public DbSet<TauxTVA> TauxTVA { get; set; }
        public DbSet<Fournisseur> Fournisseurs { get; set; }
        public DbSet<StockFournisseur> StocksFournisseurs { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<Panier> Paniers { get; set; }
        public DbSet<ItemPanier> ItemsPanier { get; set; }
        public DbSet<Commande> Commandes { get; set; }
        public DbSet<DetailsCommande> DetailsCommandes { get; set; }
        public DbSet<CommandeFournisseur> CommandesFournisseurs { get; set; }
        public DbSet<LigneCommandeFournisseur> LignesCommandeFournisseur { get; set; }
        public DbSet<Paiement> Paiements { get; set; }
        public DbSet<Promotion> Promotions { get; set; }
        public DbSet<PromotionUtilisee> PromotionsUtilisees { get; set; }
        public DbSet<PromotionGestion> PromotionGestions { get; set; }
        public DbSet<PromotionUtiliseeGestion> PromotionUtiliseesGestion { get; set; }
        public DbSet<Avis> Avis { get; set; }
        public DbSet<Favori> Favoris { get; set; }
        public DbSet<Adresse> Adresses { get; set; }
        public DbSet<Livraison> Livraisons { get; set; }
        public DbSet<StatutLivraison> StatutsLivraison { get; set; }
        public DbSet<Notification> Notifications { get; set; }
    public DbSet<Reclamation> Reclamations { get; set; }
    public DbSet<HistoriqueReclamation> HistoriqueReclamations { get; set; }
        public DbSet<Utilisateur> Utilisateurs { get; set; }
        public DbSet<Admin> Admins { get; set; }
        public DbSet<ImagesProduit> ImagesProduit { get; set; }
        public DbSet<Remboursement> Remboursements { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            // Configuration des tables TPH pour Identity
            modelBuilder.Entity<Utilisateur>().ToTable("AspNetUsers");
            modelBuilder.Entity<Admin>().HasBaseType<Utilisateur>();
            modelBuilder.Entity<Client>().HasBaseType<Utilisateur>();
            modelBuilder.Entity<Fournisseur>().HasBaseType<Utilisateur>();

            // Configuration spécifique
            //Admin
            modelBuilder.Entity<Admin>(entity => {
                entity.Property(a => a.AdminToken).HasMaxLength(20).IsRequired();
                entity.HasIndex(a => a.AdminToken).IsUnique();
            });
            //Adresse
            modelBuilder.Entity<Adresse>(entity =>
            {
                entity.HasKey(a => a.Id);
                entity.Property(a => a.Rue).IsRequired().HasMaxLength(200);
                entity.Property(a => a.Ville).IsRequired().HasMaxLength(100);
                entity.Property(a => a.CodePostal).IsRequired().HasMaxLength(20);
                entity.Property(a => a.Pays).IsRequired().HasMaxLength(100);
                entity.Property(a => a.EstPrincipale).HasDefaultValue(false);
                entity.HasOne(a => a.Utilisateur)
                      .WithMany(u => u.Adresses)
                      .HasForeignKey(a => a.UtilisateurId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasIndex(a => new { a.UtilisateurId, a.EstPrincipale });
            });
            //Avis
            modelBuilder.Entity<Avis>(entity =>
            {
                entity.HasKey(a => a.Id);
                entity.Property(a => a.Note).IsRequired();
                entity.Property(a => a.Commentaire).HasMaxLength(1000);
                entity.Property(a => a.DatePublication).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(a => a.Statut).HasDefaultValue(StatutAvis.EnAttente);
                entity.Property(a => a.CommentaireModeration).HasMaxLength(500);

                entity.HasOne(a => a.Produit)
                      .WithMany(p => p.Avis)
                      .HasForeignKey(a => a.ProduitId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(a => a.Client)
                      .WithMany(p => p.Avis)
                      .HasForeignKey(a => a.ClientId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(a => a.Moderateur)
                      .WithMany()
                      .HasForeignKey(a => a.ModerePar)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(a => new { a.ClientId, a.ProduitId }).IsUnique();
            });
            //Categorie
            modelBuilder.Entity<Categorie>(entity =>
            {
                entity.HasKey(c => c.Id);

                entity.Property(c => c.Nom)
                      .IsRequired()
                      .HasMaxLength(100);

                entity.HasIndex(c => c.Nom).IsUnique();
            });
            //Client
            modelBuilder.Entity<Client>(entity =>
            {
                entity.HasOne(c => c.Panier)
                      .WithOne(p => p.Client)
                      .HasForeignKey<Panier>(p => p.ClientId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasMany(c => c.Commandes)
                    .WithOne(cmd => cmd.Client)
                    .HasForeignKey(cmd => cmd.ClientId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(c => c.Avis)
                    .WithOne(a => a.Client)
                    .HasForeignKey(a => a.ClientId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(c => c.Favoris)
                    .WithOne(f => f.Client)
                    .HasForeignKey(f => f.ClientId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            //Commande
            modelBuilder.Entity<Commande>(entity =>
            {
                entity.Property(c => c.DateCreation).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(c => c.MontantTotal).HasColumnType("decimal(18,2)");
                entity.Property(c => c.FraisLivraison).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(c => c.Statut).HasConversion<string>();

                entity.HasOne(c => c.Client)
                      .WithMany(cl => cl.Commandes)
                      .HasForeignKey(c => c.ClientId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(c => c.Paiement)
                      .WithOne(p => p.Commande)
                      .HasForeignKey<Paiement>(p => p.CommandeId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.HasIndex(c => new { c.ClientId, c.Statut });
            });
            //CommandeFournisseur
            modelBuilder.Entity<CommandeFournisseur>(entity =>
            {
                entity.Property(c => c.Reference).HasMaxLength(20);
                entity.Property(c => c.FraisLivraison).HasColumnType("decimal(18,2)");
                entity.Property(c => c.DateCreation).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(c => c.Statut).HasConversion<string>();

                entity.HasOne(c => c.Fournisseur)
                      .WithMany(cmd => cmd.CommandesFournisseurs)
                      .HasForeignKey(c => c.FournisseurId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(c => c.CommandeClient)
                      .WithMany(cmd => cmd.CommandesFournisseurs)
                      .HasForeignKey(c => c.CommandeClientId);

                entity.HasIndex(c => c.Reference).IsUnique();
                entity.HasMany(c => c.LignesCommande)
                        .WithOne(l => l.Commande)
                        .HasForeignKey(l => l.CommandeId);

            });
            //DetailsCommande
            modelBuilder.Entity<DetailsCommande>(entity =>
            {
                entity.Property(d => d.PrixUnitaireHT).HasColumnType("decimal(18,2)");
                entity.Property(d => d.TauxTVAValue).HasColumnType("decimal(5,2)");

                entity.HasOne(d => d.Commande)
                      .WithMany(c => c.DetailsCommandes)
                      .HasForeignKey(d => d.CommandeId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Produit)
                      .WithMany(p => p.DetailsCommandes)
                      .HasForeignKey(d => d.ProduitId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(d => new { d.CommandeId, d.ProduitId }).IsUnique();
            });
            //Favori
            modelBuilder.Entity<Favori>(entity =>
            {
                entity.HasOne(f => f.Client)
                      .WithMany(c => c.Favoris)
                      .HasForeignKey(f => f.ClientId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(f => f.Produit)
                      .WithMany(c => c.Favoris)
                      .HasForeignKey(f => f.ProduitId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(f => new { f.ClientId, f.ProduitId }).IsUnique();
            });
            //Forme
            modelBuilder.Entity<Forme>(entity =>
            {
                entity.Property(f => f.Nom).HasMaxLength(100);
                entity.Property(f => f.ImageUrl).HasMaxLength(500);

                entity.HasOne(f => f.Categorie)
                      .WithMany(c => c.Formes)
                      .HasForeignKey(f => f.CategorieId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.HasMany(f => f.Produits)
                        .WithOne(p => p.Forme)
                        .HasForeignKey(p => p.FormeId)
                        .OnDelete(DeleteBehavior.Restrict);
                entity.HasIndex(f => new { f.Nom, f.CategorieId }).IsUnique();
            });
            //Fournisseur
            modelBuilder.Entity<Fournisseur>(entity =>
            {
                entity.Property(f => f.MatriculeFiscale)
                     .HasMaxLength(8)
                     .IsFixedLength();
                entity.Property(f => f.RIB)
                    .HasMaxLength(20)
                    .IsFixedLength();
                entity.Property(f => f.CodeBanque)
                    .HasMaxLength(3)
                    .IsFixedLength();
                entity.Property(f => f.Commission)
                    .HasColumnType("decimal(3,2)")
                    .HasDefaultValue(0.15m);
                entity.Property(f => f.FraisLivraisonBase)
                    .HasColumnType("decimal(10,2)")
                    .HasDefaultValue(9.99m);
                entity.Property(f => f.LogoFile)
                    .HasMaxLength(500)
                    .IsRequired();
                entity.Property(f => f.EstActif)
                    .HasDefaultValue(true);
                entity.Property(f => f.DelaiPreparationJours)
                    .HasDefaultValue(2);

                entity.HasIndex(f => f.MatriculeFiscale).IsUnique();
                entity.HasIndex(f => f.RIB).IsUnique();

                entity.HasMany(f => f.Produits)
                    .WithOne(p => p.Fournisseur)
                    .HasForeignKey(p => p.FournisseurId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(f => f.CommandesFournisseurs)
                    .WithOne(c => c.Fournisseur)
                    .HasForeignKey(c => c.FournisseurId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            //ImagesProduit
            modelBuilder.Entity<ImagesProduit>(entity =>
            {
                entity.Property(i => i.ImageUrl).HasMaxLength(500);

                entity.HasOne(i => i.Produit)
                      .WithMany(p => p.Images)
                      .HasForeignKey(i => i.ProduitId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(i => new { i.ProduitId, i.Ordre }).IsUnique();
            });
            //ItemPanier
            modelBuilder.Entity<ItemPanier>(entity =>
            {
                entity.Property(i => i.PrixUnitaire).HasColumnType("decimal(18,2)");
                entity.Property(i => i.PrixApresPromotion).HasColumnType("decimal(18,2)");
                entity.Property(i => i.DateAjout).HasDefaultValueSql("GETUTCDATE()");
                entity.HasOne(i => i.Panier)
                      .WithMany(p => p.Items)
                      .HasForeignKey(i => i.PanierId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(i => i.Produit)
                      .WithMany()
                      .HasForeignKey(i => i.ProduitId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.HasIndex(i => new { i.PanierId, i.ProduitId }).IsUnique();
            });
            //LigneCommandeFournisseur
            modelBuilder.Entity<LigneCommandeFournisseur>(entity =>
            {
                entity.Property(l => l.PrixUnitaire).HasColumnType("decimal(18,2)");

                entity.HasOne(l => l.Commande)
                      .WithMany(c => c.LignesCommande)
                      .HasForeignKey(l => l.CommandeId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(l => l.Produit)
                      .WithMany(p => p.LignesCommandeFournisseur)
                      .HasForeignKey(l => l.ProduitId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
            //Livraison
            modelBuilder.Entity<Livraison>(entity =>
            {
                entity.Property(l => l.Transporteur).HasMaxLength(100);
                entity.Property(l => l.NumeroSuivi).HasMaxLength(50);
                entity.Property(l => l.PoidsTotal).HasColumnType("decimal(10,3)");

                entity.HasOne(l => l.Commande)
                      .WithOne(c => c.Livraison)             
                      .HasForeignKey<Livraison>(l => l.CommandeId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(l => l.Fournisseur)
                      .WithMany()
                      .HasForeignKey(l => l.FournisseurId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(l => l.AdresseLivraison)
                      .WithMany()
                      .HasForeignKey(l => l.AdresseLivraisonId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(l => l.StatutLivraison)
                      .WithMany()
                      .HasForeignKey(l => l.StatutLivraisonId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(l => l.NumeroSuivi).IsUnique();
            });
            //Marque
            modelBuilder.Entity<Marque>(entity =>
            {
                entity.Property(m => m.Name)
                      .HasMaxLength(100)
                      .IsRequired();

                entity.Property(m => m.Logo)
                      .HasMaxLength(500)
                      .IsRequired();

                entity.HasIndex(m => m.Name).IsUnique();

                entity.HasMany(m => m.Produits)
                      .WithOne(p => p.Marque)
                      .HasForeignKey(p => p.MarqueId)
                      .OnDelete(DeleteBehavior.Cascade);
            });
            //Notification
            modelBuilder.Entity<Notification>(entity =>
            {
                entity.Property(n => n.Contenu).HasMaxLength(1000).IsRequired();
                entity.Property(n => n.DateEnvoi).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(n => n.EstLue).HasDefaultValue(false);

                entity.HasOne(n => n.Utilisateur)
                      .WithMany(u => u.Notifications)
                      .HasForeignKey(n => n.UtilisateurId)
                      .OnDelete(DeleteBehavior.Cascade);
            });
            // Paiement
            modelBuilder.Entity<Paiement>(entity =>
            {
                entity.HasIndex(p => p.TransactionId).IsUnique();

                entity.Property(p => p.Montant).HasColumnType("decimal(18,2)");
                entity.Property(p => p.DateCreation).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(p => p.DateMiseAJour).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(p => p.Statut).HasConversion<string>();
                entity.Property(p => p.Methode).HasConversion<string>();

                entity.HasOne(p => p.Commande)
                      .WithOne(c => c.Paiement)
                      .HasForeignKey<Paiement>(p => p.CommandeId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Remboursement)
                      .WithOne(r => r.Paiement)
                      .HasForeignKey<Remboursement>(r => r.PaiementId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
            //Panier
            modelBuilder.Entity<Panier>(entity =>
            {
                entity.Property(p => p.CodePromoApplique).HasMaxLength(50);
                entity.Property(p => p.Total).HasColumnType("decimal(18,2)");
                entity.Property(p => p.DateCreation).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(p => p.EstActif).HasDefaultValue(true);

                entity.HasOne(p => p.Client)
                      .WithOne(c => c.Panier)
                      .HasForeignKey<Panier>(p => p.ClientId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(p => p.ClientId).IsUnique().HasFilter("[EstActif] = 1");
            });
            //Produit
            modelBuilder.Entity<Produit>(entity =>
            {
                entity.Property(p => p.ReferenceOriginal).HasMaxLength(50);
                entity.Property(p => p.ReferenceFournisseur).HasMaxLength(50);
                entity.Property(p => p.CodeABarre).HasMaxLength(13).IsFixedLength();
                entity.Property(p => p.Nom).HasMaxLength(100);
                entity.Property(p => p.PrixAchatHT).HasColumnType("decimal(18,2)");
                entity.Property(p => p.PrixVenteHT).HasColumnType("decimal(18,2)");

                entity.HasIndex(p => p.ReferenceOriginal).IsUnique();
                entity.HasIndex(p => p.CodeABarre).IsUnique();

                entity.HasOne(p => p.TauxTVA)
                      .WithMany(t => t.Produits)
                      .HasForeignKey(p => p.TauxTVAId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.SousCategorie)
                      .WithMany(s => s.Produits)
                      .HasForeignKey(p => p.SousCategorieId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Marque)
                      .WithMany(m => m.Produits)
                      .HasForeignKey(p => p.MarqueId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Forme)
                      .WithMany(f => f.Produits)
                      .HasForeignKey(p => p.FormeId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
            //Promotion
            modelBuilder.Entity<Promotion>(entity =>
            {
                entity.Property(p => p.PourcentageRemise).HasColumnType("decimal(5,2)");
                entity.Property(p => p.CodePromo).HasMaxLength(20);
                entity.Property(p => p.Type).HasConversion<string>();

                entity.HasIndex(p => p.CodePromo).IsUnique().HasFilter("[CodePromo] IS NOT NULL");

                entity.HasOne(p => p.Categorie)
                      .WithMany()
                      .HasForeignKey(p => p.CategorieId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.SousCategorie)
                      .WithMany()
                      .HasForeignKey(p => p.SousCategorieId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Marque)
                      .WithMany()
                      .HasForeignKey(p => p.MarqueId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Fournisseur)
                      .WithMany()
                      .HasForeignKey(p => p.FournisseurId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Forme)
                      .WithMany()
                      .HasForeignKey(p => p.FormeId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
            //PromotionUtilisee
            modelBuilder.Entity<PromotionUtilisee>(entity =>
            {
                entity.Property(p => p.DateUtilisation).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(p => p.MontantEconomise).HasColumnType("decimal(18,2)");
                entity.Property(p => p.CodePromoUtilise).HasMaxLength(100);

                entity.HasOne(p => p.Promotion)
                      .WithMany()
                      .HasForeignKey(p => p.PromotionId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Commande)
                      .WithMany(c => c.PromotionsUtilisees)
                      .HasForeignKey(p => p.CommandeId)
                      .OnDelete(DeleteBehavior.Cascade);
            });
            //Remboursement
            modelBuilder.Entity<Remboursement>(entity =>
            {
                entity.Property(r => r.RemboursementId).HasMaxLength(50);
                entity.Property(r => r.Montant).HasColumnType("decimal(18,2)");
                entity.Property(r => r.DateDemande).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(r => r.Raison).HasMaxLength(500);
                entity.Property(r => r.Statut).HasConversion<string>();

                entity.HasOne(r => r.Paiement)
                      .WithOne(p => p.Remboursement)
                      .HasForeignKey<Remboursement>(r => r.PaiementId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(r => r.Initiateur)
                      .WithMany(u => u.RemboursementsInities)
                      .HasForeignKey(r => r.InitiateurId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(r => r.Commande)
                      .WithMany(c => c.Remboursements)
                      .HasForeignKey(r => r.CommandeId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.HasIndex(r => r.PaiementId).IsUnique();

            });
            //SousCategorie
            modelBuilder.Entity<SousCategorie>(entity =>
            {
                entity.Property(s => s.Nom).HasMaxLength(100);

                entity.HasOne(s => s.Categorie)
                      .WithMany(c => c.SousCategories)
                      .HasForeignKey(s => s.CategorieId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(s => new { s.Nom, s.CategorieId }).IsUnique();
            });
            //StatutLivraison
            modelBuilder.Entity<StatutLivraison>(entity =>
            {
                entity.Property(s => s.Libelle).HasMaxLength(50);
                entity.Property(s => s.Description).HasMaxLength(200);
                entity.Property(s => s.DateCreation).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(s => s.Code).HasConversion<string>();

                entity.HasIndex(s => s.Code).IsUnique();
                entity.HasMany(s => s.Livraisons)
                      .WithOne(l => l.StatutLivraison)
                      .HasForeignKey(l => l.StatutLivraisonId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
            //StockFournisseur
            modelBuilder.Entity<StockFournisseur>(entity =>
            {
                entity.HasKey(s => new { s.FournisseurId, s.ProduitId });

                entity.Property(s => s.PrixUnitaire).HasColumnType("decimal(18,2)");

                entity.HasOne(s => s.Fournisseur)
                      .WithMany(f => f.Stocks)
                      .HasForeignKey(s => s.FournisseurId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(s => s.Produit)
                      .WithMany(p => p.StocksFournisseurs)
                      .HasForeignKey(s => s.ProduitId)
                      .OnDelete(DeleteBehavior.Cascade);
            });
            //TauxTVA
            modelBuilder.Entity<TauxTVA>(entity =>
            {
                entity.Property(t => t.Libelle).HasMaxLength(50);
                entity.Property(t => t.Taux).HasColumnType("decimal(5,2)");
                entity.Property(t => t.DateEffet).HasDefaultValueSql("GETUTCDATE()");

                entity.HasIndex(t => t.Libelle).IsUnique();

                // Relation optionnelle avec Categorie
                entity.HasOne(t => t.Categorie)
                      .WithMany()
                      .HasForeignKey(t => t.CategorieId)
                      .OnDelete(DeleteBehavior.SetNull);
            });
            //Utilisateur
            modelBuilder.Entity<Utilisateur>(entity =>
            {
                entity.Property(u => u.Nom).HasMaxLength(50);
                entity.Property(u => u.Prenom).HasMaxLength(50);
                entity.Property(u => u.DateNaissance).HasColumnType("date");
                entity.Property(u => u.DateInscription).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(u => u.EstActif).HasDefaultValue(true);
                entity.HasDiscriminator(u => u.RoleDiscriminator)
                    .HasValue<Admin>(RoleUtilisateur.Admin)
                    .HasValue<Client>(RoleUtilisateur.Client)
                    .HasValue<Fournisseur>(RoleUtilisateur.Fournisseur);
                entity.HasMany(u => u.Adresses)
                      .WithOne(a => a.Utilisateur)
                      .HasForeignKey(a => a.UtilisateurId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasMany(u => u.Notifications)
                   .WithOne(n => n.Utilisateur)
                   .HasForeignKey(n => n.UtilisateurId)
                   .OnDelete(DeleteBehavior.Cascade);
            });

            // Configuration finale des index et relations complexes
            modelBuilder.Entity<StockFournisseur>(entity =>
            {
                entity.HasKey(s => new { s.FournisseurId, s.ProduitId });
                entity.Property(s => s.PrixUnitaire).HasColumnType("decimal(18,2)");
            });

            modelBuilder.Entity<Promotion>(entity =>
            {
                entity.HasIndex(p => p.CodePromo).IsUnique();
                entity.Property(p => p.PourcentageRemise).HasColumnType("decimal(5,2)");
            });

            // Configuration pour PromotionGestion
            modelBuilder.Entity<PromotionGestion>(entity =>
            {
                entity.HasIndex(p => p.Code).IsUnique();
                entity.Property(p => p.Valeur).HasColumnType("decimal(10,2)");
                entity.Property(p => p.MontantMinimum).HasColumnType("decimal(10,2)");
                entity.Property(p => p.MontantTotalEconomise).HasColumnType("decimal(15,2)");
                entity.Property(p => p.DateCreation).HasDefaultValueSql("GETUTCDATE()");

                entity.HasOne(p => p.Fournisseur)
                      .WithMany()
                      .HasForeignKey(p => p.FournisseurId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(p => p.Produit)
                      .WithMany()
                      .HasForeignKey(p => p.ProduitId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Configuration pour PromotionUtiliseeGestion
            modelBuilder.Entity<PromotionUtiliseeGestion>(entity =>
            {
                entity.Property(p => p.MontantReduction).HasColumnType("decimal(10,2)");
                entity.Property(p => p.DateUtilisation).HasDefaultValueSql("GETUTCDATE()");

                entity.HasOne(p => p.Promotion)
                      .WithMany(pr => pr.PromotionsUtilisees)
                      .HasForeignKey(p => p.PromotionId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(p => p.Commande)
                      .WithMany()
                      .HasForeignKey(p => p.CommandeId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Livraison>(entity =>
            {
                entity.HasIndex(l => l.NumeroSuivi).IsUnique();
                entity.Property(l => l.PoidsTotal).HasColumnType("decimal(10,3)");
            });

            // Configuration de la relation many-to-many entre Produit et Promotion

            modelBuilder.Entity<Produit>()
                .HasMany(p => p.PromotionsApplicables)
                .WithMany(p => p.ProduitsApplicables)
                .UsingEntity<Dictionary<string, object>>(
                    "ProduitPromotion",
                    j => j.HasOne<Promotion>().WithMany().HasForeignKey("PromotionId"),
                    j => j.HasOne<Produit>().WithMany().HasForeignKey("ProduitId"),
                    j => j.ToTable("ProduitPromotion")
                );

            // Désactiver le chargement automatique de la navigation inverse
            modelBuilder.Entity<Promotion>()
                .Navigation(p => p.ProduitsApplicables)
                .AutoInclude(false);
        }
    }
}