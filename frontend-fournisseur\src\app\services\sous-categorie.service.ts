import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
  SousCategorie,
  SousCategorieCreate,
  SousCategorieUpdate,
  SousCategorieDropdown
} from '../models';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SousCategorieService {
  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/SousCategories`;

  constructor(private http: HttpClient) {}

  /**
   * GET /api/SousCategories - Obtenir toutes les sous-catégories
   */
  getAll(fournisseurId?: number): Observable<SousCategorie[]> {
    console.log('📦 Récupération des sous-catégories');
    console.log('🔍 FournisseurId reçu:', fournisseurId);
    let url = `${this.API_URL}/enriched`;
    if (fournisseurId) {
      url += `?fournisseurId=${fournisseurId}`;
    }
    console.log('🌐 URL finale:', url);
    return this.http.get<SousCategorie[]>(url)
      .pipe(
        tap(response => console.log('✅ Sous-catégories récupérées:', response))
      );
  }

  /**
   * GET /api/SousCategories/{id} - Obtenir une sous-catégorie par ID
   */
  getById(id: number): Observable<SousCategorie> {
    console.log('🔍 Récupération de la sous-catégorie ID:', id);
    return this.http.get<SousCategorie>(`${this.API_URL}/${id}`)
      .pipe(
        tap(response => console.log('✅ Sous-catégorie récupérée:', response))
      );
  }

  /**
   * POST /api/SousCategories - Créer une nouvelle sous-catégorie
   */
  create(sousCategorie: SousCategorieCreate): Observable<SousCategorie> {
    console.log('➕ Création d\'une nouvelle sous-catégorie:', sousCategorie);
    return this.http.post<SousCategorie>(this.API_URL, sousCategorie)
      .pipe(
        tap(response => console.log('✅ Sous-catégorie créée:', response))
      );
  }

  /**
   * PUT /api/SousCategories/{id} - Mettre à jour une sous-catégorie
   */
  update(id: number, sousCategorie: SousCategorieUpdate): Observable<SousCategorie> {
    console.log('✏️ Mise à jour de la sous-catégorie ID:', id, sousCategorie);
    return this.http.put<SousCategorie>(`${this.API_URL}/${id}`, sousCategorie)
      .pipe(
        tap(response => console.log('✅ Sous-catégorie mise à jour:', response))
      );
  }

  /**
   * DELETE /api/SousCategories/{id} - Supprimer une sous-catégorie
   */
  delete(id: number): Observable<void> {
    console.log('🗑️ Suppression de la sous-catégorie ID:', id);
    return this.http.delete<void>(`${this.API_URL}/${id}`)
      .pipe(
        tap(() => console.log('✅ Sous-catégorie supprimée:', id))
      );
  }

  /**
   * GET /api/SousCategories/by-categorie/{categorieId} - Obtenir les sous-catégories d'une catégorie
   */
  getByCategorie(categorieId: number): Observable<SousCategorie[]> {
    console.log('📂 Récupération des sous-catégories pour la catégorie:', categorieId);
    return this.http.get<SousCategorie[]>(`${this.API_URL}/by-categorie/${categorieId}`)
      .pipe(
        tap(response => console.log('✅ Sous-catégories par catégorie récupérées:', response))
      );
  }

  /**
   * GET /api/SousCategories/{id}/produits-count - Obtenir le nombre de produits d'une sous-catégorie
   */
  getProduitsCount(id: number): Observable<number> {
    console.log('🔢 Récupération du nombre de produits pour la sous-catégorie:', id);
    return this.http.get<number>(`${this.API_URL}/${id}/produits-count`)
      .pipe(
        tap(response => console.log('✅ Nombre de produits récupéré:', response))
      );
  }

  /**
   * GET /api/SousCategories/dropdown/{categorieId} - Obtenir les sous-catégories pour dropdown
   */
  getDropdown(categorieId: number): Observable<SousCategorieDropdown[]> {
    console.log('📋 Récupération des sous-catégories pour dropdown, catégorie:', categorieId);
    return this.http.get<SousCategorieDropdown[]>(`${this.API_URL}/dropdown/${categorieId}`)
      .pipe(
        tap(response => console.log('✅ Dropdown sous-catégories récupéré:', response))
      );
  }
}
