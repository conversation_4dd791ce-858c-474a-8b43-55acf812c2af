using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.Services.Interfaces
{
    public interface IDemandeService
    {
        // Demandes de catégories
        Task<DemandeCategorieDto> CreerDemandeCategorieAsync(CreateDemandeCategorieDto dto, int fournisseurId);
        Task<IEnumerable<DemandeCategorieDto>> GetDemandesCategoriesAsync(int? fournisseurId = null);
        Task<DemandeCategorieDto?> GetDemandeCategorieByIdAsync(int id);
        Task<DemandeCategorieDto> TraiterDemandeCategorieAsync(int demandeId, TraiterDemandeDto dto, int adminId);

        // Demandes de sous-catégories
        Task<DemandeSousCategorieDto> CreerDemandeSousCategorieAsync(CreateDemandeSousCategorieDto dto, int fournisseurId);
        Task<IEnumerable<DemandeSousCategorieDto>> GetDemandesSousCategoriesAsync(int? fournisseurId = null);
        Task<DemandeSousCategorieDto?> GetDemandeSousCategorieByIdAsync(int id);
        Task<DemandeSousCategorieDto> TraiterDemandeSousCategorieAsync(int demandeId, TraiterDemandeDto dto, int adminId);

        // Statistiques
        Task<int> GetNombreDemandesEnAttenteAsync();
    }
}
