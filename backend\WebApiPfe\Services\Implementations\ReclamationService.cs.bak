using AutoMapper;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.ReclamationDTOs;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class ReclamationService : IReclamationService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<ReclamationService> _logger;

        public ReclamationService(
            AppDbContext context,
            IMapper mapper,
            IWebHostEnvironment environment,
            ILogger<ReclamationService> logger)
        {
            _context = context;
            _mapper = mapper;
            _environment = environment;
            _logger = logger;
        }

        public async Task<ReclamationReadDto> CreerReclamationAsync(CreateReclamationDto dto, int clientId)
        {
            try
            {
                // Vérifier si le client peut créer une réclamation pour cette commande
                if (!await PeutCreerReclamationAsync(clientId, dto.CommandeId))
                {
                    throw new InvalidOperationException("Vous ne pouvez pas créer de réclamation pour cette commande.");
                }

                var reclamation = new Reclamation
                {
                    CommandeId = dto.CommandeId,
                    ClientId = clientId,
                    Objet = dto.Objet,
                    Description = dto.Description,
                    Type = dto.Type,
                    Statut = StatutReclamation.EnAttente,
                    DateCreation = DateTime.UtcNow
                };

                _context.Reclamations.Add(reclamation);
                await _context.SaveChangesAsync();

                // Upload des pièces jointes si présentes
                if (dto.PiecesJointes?.Any() == true)
                {
                    var urlsFichiers = await UploadPiecesJointesAsync(dto.PiecesJointes, reclamation.Id);
                    reclamation.PiecesJointes = string.Join(";", urlsFichiers);
                    await _context.SaveChangesAsync();
                }

                // Ajouter à l'historique
                await AjouterHistoriqueAsync(reclamation.Id, clientId, "Création", "Réclamation créée par le client");

                // Envoyer notification
                await EnvoyerNotificationReclamationAsync(reclamation.Id, "Nouvelle réclamation");

                return await ObtenirReclamationAsync(reclamation.Id) ?? throw new InvalidOperationException("Erreur lors de la création de la réclamation");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la réclamation");
                throw;
            }
        }

        public async Task<ReclamationReadDto?> ObtenirReclamationAsync(int id)
        {
            var reclamation = await _context.Reclamations
                .Include(r => r.Commande)
                .Include(r => r.Client)
                .Include(r => r.TraiteurReclamation)
                .Include(r => r.Historique)
                    .ThenInclude(h => h.Utilisateur)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (reclamation == null) return null;

            return MapToReadDto(reclamation);
        }

        public async Task<List<ReclamationReadDto>> ObtenirReclamationsAsync(ReclamationFilterDto filter)
        {
            var query = _context.Reclamations
                .Include(r => r.Commande)
                .Include(r => r.Client)
                .Include(r => r.TraiteurReclamation)
                .AsQueryable();

            // Appliquer les filtres
            query = AppliquerFiltres(query, filter);

            // Pagination et tri
            var reclamations = await query
                .OrderByDescending(r => r.DateCreation)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return reclamations.Select(MapToReadDto).ToList();
        }

        public async Task<List<ReclamationReadDto>> ObtenirReclamationsClientAsync(int clientId, ReclamationFilterDto filter)
        {
            filter.ClientId = clientId;
            return await ObtenirReclamationsAsync(filter);
        }

        public async Task<List<ReclamationReadDto>> ObtenirReclamationsFournisseurAsync(int fournisseurId, ReclamationFilterDto filter)
        {
            var query = _context.Reclamations
                .Include(r => r.Commande)
                    .ThenInclude(c => c.CommandesFournisseurs)
                        .ThenInclude(cf => cf.Fournisseur)
                .Include(r => r.Client)
                .Include(r => r.TraiteurReclamation)
                .Where(r => r.Commande.CommandesFournisseurs.Any(cf => cf.FournisseurId == fournisseurId))
                .AsQueryable();

            // Appliquer les filtres
            query = AppliquerFiltres(query, filter);

            var reclamations = await query
                .OrderByDescending(r => r.DateCreation)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return reclamations.Select(MapToReadDto).ToList();
        }

        public async Task<List<ReclamationReadDto>> ObtenirReclamationsAdminAsync(ReclamationFilterDto filter)
        {
            return await ObtenirReclamationsAsync(filter);
        }

        public async Task<ReclamationReadDto> TraiterReclamationAsync(int id, UpdateReclamationDto dto, int traiteurId)
        {
            var reclamation = await _context.Reclamations.FindAsync(id);
            if (reclamation == null)
                throw new ArgumentException("Réclamation non trouvée");

            reclamation.Statut = dto.Statut ?? StatutReclamation.EnCours;
            reclamation.DateTraitement = DateTime.UtcNow;
            reclamation.TraitePar = traiteurId;

            if (!string.IsNullOrEmpty(dto.ReponseAdmin))
                reclamation.ReponseAdmin = dto.ReponseAdmin;

            if (!string.IsNullOrEmpty(dto.ReponseFournisseur))
                reclamation.ReponseFournisseur = dto.ReponseFournisseur;

            await _context.SaveChangesAsync();

            // Ajouter à l'historique
            await AjouterHistoriqueAsync(id, traiteurId, "Traitement", dto.Commentaire ?? "Réclamation prise en charge");

            // Envoyer notification
            await EnvoyerNotificationReclamationAsync(id, "Réclamation traitée");

            return await ObtenirReclamationAsync(id) ?? throw new InvalidOperationException("Erreur lors du traitement");
        }

        public async Task<ReclamationReadDto> MettreAJourReclamationAsync(int id, UpdateReclamationDto dto, int utilisateurId)
        {
            return await TraiterReclamationAsync(id, dto, utilisateurId);
        }

        public async Task<ReclamationReadDto> ResoudreReclamationAsync(int id, string resolution, int traiteurId)
        {
            var dto = new UpdateReclamationDto
            {
                Statut = StatutReclamation.Resolue,
                ReponseAdmin = resolution,
                Commentaire = "Réclamation résolue"
            };

            var reclamation = await TraiterReclamationAsync(id, dto, traiteurId);
            
            // Mettre à jour la date de résolution
            var entity = await _context.Reclamations.FindAsync(id);
            if (entity != null)
            {
                entity.DateResolution = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            return reclamation;
        }

        public async Task<ReclamationReadDto> FermerReclamationAsync(int id, string commentaire, int traiteurId)
        {
            var dto = new UpdateReclamationDto
            {
                Statut = StatutReclamation.Fermee,
                Commentaire = commentaire
            };

            return await TraiterReclamationAsync(id, dto, traiteurId);
        }

        public async Task<ReclamationReadDto> RejeterReclamationAsync(int id, string motif, int traiteurId)
        {
            var dto = new UpdateReclamationDto
            {
                Statut = StatutReclamation.Rejetee,
                ReponseAdmin = motif,
                Commentaire = "Réclamation rejetée"
            };

            return await TraiterReclamationAsync(id, dto, traiteurId);
        }

        public async Task<bool> SupprimerReclamationAsync(int id)
        {
            var reclamation = await _context.Reclamations.FindAsync(id);
            if (reclamation == null) return false;

            _context.Reclamations.Remove(reclamation);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<ReclamationStatsDto> ObtenirStatistiquesAsync()
        {
            var stats = new ReclamationStatsDto();
            
            var reclamations = await _context.Reclamations.ToListAsync();
            
            stats.TotalReclamations = reclamations.Count;
            stats.ReclamationsEnAttente = reclamations.Count(r => r.Statut == StatutReclamation.EnAttente);
            stats.ReclamationsEnCours = reclamations.Count(r => r.Statut == StatutReclamation.EnCours);
            stats.ReclamationsResolues = reclamations.Count(r => r.Statut == StatutReclamation.Resolue);
            stats.ReclamationsFermees = reclamations.Count(r => r.Statut == StatutReclamation.Fermee);

            if (stats.TotalReclamations > 0)
            {
                stats.TauxResolution = (double)(stats.ReclamationsResolues + stats.ReclamationsFermees) / stats.TotalReclamations * 100;
            }

            var reclamationsResolues = reclamations.Where(r => r.DateResolution.HasValue).ToList();
            if (reclamationsResolues.Any())
            {
                var delais = reclamationsResolues.Select(r => (r.DateResolution!.Value - r.DateCreation).TotalHours);
                stats.DelaiMoyenResolution = delais.Average();
            }

            // Statistiques par type
            foreach (TypeReclamation type in Enum.GetValues<TypeReclamation>())
            {
                stats.ReclamationsParType[type] = reclamations.Count(r => r.Type == type);
            }

            // Réclamations récentes
            var recentesFilter = new ReclamationFilterDto { Page = 1, PageSize = 5 };
            stats.ReclamationsRecentes = await ObtenirReclamationsAsync(recentesFilter);

            return stats;
        }

        public async Task<ReclamationStatsDto> ObtenirStatistiquesFournisseurAsync(int fournisseurId)
        {
            // Implémentation similaire mais filtrée par fournisseur
            var filter = new ReclamationFilterDto { FournisseurId = fournisseurId };
            var reclamations = await ObtenirReclamationsFournisseurAsync(fournisseurId, filter);
            
            // Calculer les statistiques basées sur les réclamations du fournisseur
            var stats = new ReclamationStatsDto
            {
                TotalReclamations = reclamations.Count,
                ReclamationsEnAttente = reclamations.Count(r => r.Statut == StatutReclamation.EnAttente),
                ReclamationsEnCours = reclamations.Count(r => r.Statut == StatutReclamation.EnCours),
                ReclamationsResolues = reclamations.Count(r => r.Statut == StatutReclamation.Resolue),
                ReclamationsFermees = reclamations.Count(r => r.Statut == StatutReclamation.Fermee)
            };

            return stats;
        }

        // Méthodes privées d'aide
        private ReclamationReadDto MapToReadDto(Reclamation reclamation)
        {
            return new ReclamationReadDto
            {
                Id = reclamation.Id,
                CommandeId = reclamation.CommandeId,
                NumeroCommande = $"CMD-{reclamation.Commande?.Id ?? 0}",
                ClientId = reclamation.ClientId,
                NomClient = $"{reclamation.Client?.Prenom} {reclamation.Client?.Nom}",
                EmailClient = reclamation.Client?.Email ?? "",
                Objet = reclamation.Objet,
                Description = reclamation.Description,
                Type = reclamation.Type,
                TypeLibelle = GetTypeLibelle(reclamation.Type),
                Statut = reclamation.Statut,
                StatutLibelle = GetStatutLibelle(reclamation.Statut),
                DateCreation = reclamation.DateCreation,
                DateTraitement = reclamation.DateTraitement,
                DateResolution = reclamation.DateResolution,
                ReponseAdmin = reclamation.ReponseAdmin,
                ReponseFournisseur = reclamation.ReponseFournisseur,
                NomTraiteur = reclamation.TraiteurReclamation != null ? 
                    $"{reclamation.TraiteurReclamation.Prenom} {reclamation.TraiteurReclamation.Nom}" : null,
                PiecesJointes = string.IsNullOrEmpty(reclamation.PiecesJointes) ? 
                    new List<string>() : reclamation.PiecesJointes.Split(';').ToList(),
                Historique = reclamation.Historique?.Select(h => new HistoriqueReclamationDto
                {
                    Id = h.Id,
                    Action = h.Action,
                    Commentaire = h.Commentaire,
                    DateAction = h.DateAction,
                    NomUtilisateur = $"{h.Utilisateur.Prenom} {h.Utilisateur.Nom}",
                    RoleUtilisateur = GetRoleLibelle(h.Utilisateur.RoleDiscriminator)
                }).OrderByDescending(h => h.DateAction).ToList() ?? new List<HistoriqueReclamationDto>()
            };
        }

        private static string GetTypeLibelle(TypeReclamation type)
        {
            return type switch
            {
                TypeReclamation.ProduitDefectueux => "Produit défectueux",
                TypeReclamation.LivraisonRetard => "Retard de livraison",
                TypeReclamation.ProduitNonConforme => "Produit non conforme",
                TypeReclamation.ServiceClient => "Service client",
                TypeReclamation.Remboursement => "Demande de remboursement",
                TypeReclamation.Autre => "Autre",
                _ => "Non défini"
            };
        }

        private static string GetStatutLibelle(StatutReclamation statut)
        {
            return statut switch
            {
                StatutReclamation.EnAttente => "En attente",
                StatutReclamation.EnCours => "En cours de traitement",
                StatutReclamation.Resolue => "Résolue",
                StatutReclamation.Fermee => "Fermée",
                StatutReclamation.Rejetee => "Rejetée",
                _ => "Non défini"
            };
        }

        private static string GetRoleLibelle(RoleUtilisateur role)
        {
            return role switch
            {
                RoleUtilisateur.Client => "Client",
                RoleUtilisateur.Fournisseur => "Fournisseur",
                RoleUtilisateur.Admin => "Administrateur",
                _ => "Utilisateur"
            };
        }

        private IQueryable<Reclamation> AppliquerFiltres(IQueryable<Reclamation> query, ReclamationFilterDto filter)
        {
            if (filter.Statut.HasValue)
                query = query.Where(r => r.Statut == filter.Statut.Value);

            if (filter.Type.HasValue)
                query = query.Where(r => r.Type == filter.Type.Value);

            if (filter.DateDebut.HasValue)
                query = query.Where(r => r.DateCreation >= filter.DateDebut.Value);

            if (filter.DateFin.HasValue)
                query = query.Where(r => r.DateCreation <= filter.DateFin.Value);

            if (filter.ClientId.HasValue)
                query = query.Where(r => r.ClientId == filter.ClientId.Value);

            if (!string.IsNullOrEmpty(filter.Recherche))
            {
                query = query.Where(r => r.Objet.Contains(filter.Recherche) || 
                                        r.Description.Contains(filter.Recherche));
            }

            return query;
        }

        private async Task AjouterHistoriqueAsync(int reclamationId, int utilisateurId, string action, string? commentaire = null)
        {
            var historique = new HistoriqueReclamation
            {
                ReclamationId = reclamationId,
                UtilisateurId = utilisateurId,
                Action = action,
                Commentaire = commentaire,
                DateAction = DateTime.UtcNow
            };

            _context.HistoriqueReclamations.Add(historique);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> PeutCreerReclamationAsync(int clientId, int commandeId)
        {
            // Vérifier que la commande appartient au client
            var commande = await _context.Commandes
                .FirstOrDefaultAsync(c => c.Id == commandeId && c.ClientId == clientId);

            if (commande == null) return false;

            // Vérifier que la commande est livrée (on peut faire une réclamation après livraison)
            if (commande.Statut != StatutCommande.Livree && commande.Statut != StatutCommande.Validee)
                return false;

            // Vérifier qu'il n'y a pas déjà une réclamation en cours pour cette commande
            var reclamationExistante = await _context.Reclamations
                .AnyAsync(r => r.CommandeId == commandeId && 
                              (r.Statut == StatutReclamation.EnAttente || r.Statut == StatutReclamation.EnCours));

            return !reclamationExistante;
        }

        public async Task<bool> PeutTraiterReclamationAsync(int utilisateurId, int reclamationId)
        {
            var utilisateur = await _context.Users.FindAsync(utilisateurId);
            if (utilisateur == null) return false;

            // Les admins peuvent traiter toutes les réclamations
            if (utilisateur.RoleDiscriminator == RoleUtilisateur.Admin)
                return true;

            // Les fournisseurs peuvent traiter les réclamations de leurs commandes
            if (utilisateur.RoleDiscriminator == RoleUtilisateur.Fournisseur)
            {
                var reclamation = await _context.Reclamations
                    .Include(r => r.Commande)
                        .ThenInclude(c => c.CommandesFournisseurs)
                    .FirstOrDefaultAsync(r => r.Id == reclamationId);

                return reclamation?.Commande.CommandesFournisseurs.Any(cf => cf.FournisseurId == utilisateurId) == true;
            }

            return false;
        }

        public async Task<List<string>> UploadPiecesJointesAsync(List<IFormFile> fichiers, int reclamationId)
        {
            var urlsFichiers = new List<string>();
            var uploadPath = Path.Combine(_environment.WebRootPath, "uploads", "reclamations", reclamationId.ToString());

            if (!Directory.Exists(uploadPath))
                Directory.CreateDirectory(uploadPath);

            foreach (var fichier in fichiers)
            {
                if (fichier.Length > 0)
                {
                    var fileName = $"{Guid.NewGuid()}_{fichier.FileName}";
                    var filePath = Path.Combine(uploadPath, fileName);

                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await fichier.CopyToAsync(stream);
                    }

                    urlsFichiers.Add($"/uploads/reclamations/{reclamationId}/{fileName}");
                }
            }

            return urlsFichiers;
        }

        public async Task EnvoyerNotificationReclamationAsync(int reclamationId, string action)
        {
            // TODO: Implémenter l'envoi de notifications (email, push, etc.)
            _logger.LogInformation($"Notification réclamation {reclamationId}: {action}");
            await Task.CompletedTask;
        }
    }
}
