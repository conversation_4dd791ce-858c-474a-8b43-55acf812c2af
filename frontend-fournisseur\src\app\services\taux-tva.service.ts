import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
  TauxTVA,
  TauxTVACreate,
  TauxTVAUpdate,
  TauxTVADropdown,
  CalculTTCRequest,
  CalculHTRequest,
  CalculResponse
} from '../models';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TauxTVAService {
  private readonly API_URL = `${environment.apiUrl}/tva`;

  constructor(private http: HttpClient) {
    console.log('🔧 TauxTVAService initialisé avec URL:', this.API_URL);
  }

  /**
   * GET /api/tva - Obtenir tous les taux TVA
   */
  getAll(): Observable<TauxTVA[]> {
    console.log('📦 Récupération des taux TVA');
    return this.http.get<TauxTVA[]>(this.API_URL)
      .pipe(
        tap(response => console.log('✅ Taux TVA récupérés:', response))
      );
  }

  /**
   * GET /api/tva/{id} - Obtenir un taux TVA par ID
   */
  getById(id: number): Observable<TauxTVA> {
    console.log('🔍 Récupération du taux TVA ID:', id);
    return this.http.get<TauxTVA>(`${this.API_URL}/${id}`)
      .pipe(
        tap(response => console.log('✅ Taux TVA récupéré:', response))
      );
  }

  /**
   * POST /api/tva - Créer un nouveau taux TVA
   */
  create(tauxTVA: TauxTVACreate): Observable<TauxTVA> {
    console.log('➕ Création d\'un nouveau taux TVA:', tauxTVA);
    return this.http.post<TauxTVA>(this.API_URL, tauxTVA)
      .pipe(
        tap(response => console.log('✅ Taux TVA créé:', response))
      );
  }

  /**
   * PUT /api/tva/{id} - Mettre à jour un taux TVA
   */
  update(id: number, tauxTVA: TauxTVAUpdate): Observable<TauxTVA> {
    console.log('✏️ Mise à jour du taux TVA ID:', id, tauxTVA);
    return this.http.put<TauxTVA>(`${this.API_URL}/${id}`, tauxTVA)
      .pipe(
        tap(response => console.log('✅ Taux TVA mis à jour:', response))
      );
  }

  /**
   * DELETE /api/tva/{id} - Supprimer un taux TVA
   */
  delete(id: number): Observable<void> {
    console.log('🗑️ Suppression du taux TVA ID:', id);
    return this.http.delete<void>(`${this.API_URL}/${id}`)
      .pipe(
        tap(() => console.log('✅ Taux TVA supprimé:', id))
      );
  }

  /**
   * GET /api/tva/actuel - Obtenir le taux TVA actuel
   */
  getActuel(): Observable<TauxTVA> {
    console.log('📅 Récupération du taux TVA actuel');
    return this.http.get<TauxTVA>(`${this.API_URL}/actuel`)
      .pipe(
        tap(response => console.log('✅ Taux TVA actuel récupéré:', response))
      );
  }

  /**
   * GET /api/tva/dropdown - Obtenir les taux TVA pour dropdown
   */
  getDropdown(): Observable<TauxTVADropdown[]> {
    console.log('📋 Récupération des taux TVA pour dropdown');
    return this.http.get<TauxTVADropdown[]>(`${this.API_URL}/dropdown`)
      .pipe(
        tap(response => console.log('✅ Dropdown taux TVA récupéré:', response))
      );
  }

  /**
   * GET /api/tva/by-categorie/{categorieId} - Obtenir les taux TVA d'une catégorie
   */
  getByCategorie(categorieId: number): Observable<TauxTVA[]> {
    console.log('📂 Récupération des taux TVA pour la catégorie:', categorieId);
    return this.http.get<TauxTVA[]>(`${this.API_URL}/by-categorie/${categorieId}`)
      .pipe(
        tap(response => console.log('✅ Taux TVA par catégorie récupérés:', response))
      );
  }

  /**
   * GET /api/tva/dropdown/{categorieId} - Obtenir les taux TVA pour dropdown par catégorie
   */
  getDropdownByCategorie(categorieId: number): Observable<TauxTVADropdown[]> {
    console.log('📋 Récupération des taux TVA dropdown pour la catégorie:', categorieId);
    return this.http.get<TauxTVADropdown[]>(`${this.API_URL}/dropdown/${categorieId}`)
      .pipe(
        tap(response => console.log('✅ Dropdown taux TVA par catégorie récupéré:', response))
      );
  }

  /**
   * POST /api/tva/calculer-ttc - Calculer le montant TTC
   */
  calculerTTC(request: CalculTTCRequest): Observable<CalculResponse> {
    console.log('🧮 Calcul TTC:', request);
    return this.http.post<CalculResponse>(`${this.API_URL}/calculer-ttc`, request)
      .pipe(
        tap(response => console.log('✅ Calcul TTC effectué:', response))
      );
  }

  /**
   * POST /api/tva/calculer-ht - Calculer le montant HT
   */
  calculerHT(request: CalculHTRequest): Observable<CalculResponse> {
    console.log('🧮 Calcul HT:', request);
    return this.http.post<CalculResponse>(`${this.API_URL}/calculer-ht`, request)
      .pipe(
        tap(response => console.log('✅ Calcul HT effectué:', response))
      );
  }
}
