import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CategorieService } from '../../services/categorie.service';
import { SousCategorieService } from '../../services/sous-categorie.service';
import { MarqueService } from '../../services/marque.service';
import { FormeService } from '../../services/forme.service';
import { TauxTVAService } from '../../services/taux-tva.service';
import { AuthService } from '../../services/auth.service';
import {
  Categorie,
  SousCategorie,
  Marque,
  Forme,
  TauxTVA,
  CategorieCreate,
  SousCategorieCreate,
  MarqueCreate,
  FormeCreate,
  TauxTVACreate
} from '../../models';

@Component({
  selector: 'app-referentiels',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="referentiels-container">
      <!-- Header moderne -->
      <div class="referentiels-header">
        <h1 class="referentiels-title">
          <span>🗂️</span>
          {{ isAdmin() ? 'Gestion des Référentiels' : 'Consultation des Référentiels' }}
        </h1>
        <p>{{ isAdmin() ? 'Configurez vos catégories, marques et paramètres' : 'Consultez les catégories, marques et paramètres disponibles' }}</p>
      </div>

      <!-- Onglets -->
      <div class="tabs">
        <button
          *ngFor="let tab of tabs"
          class="tab-button"
          [class.active]="activeTab() === tab.id"
          (click)="setActiveTab(tab.id)"
        >
          {{ tab.icon }} {{ tab.label }}
        </button>
      </div>

      <!-- Contenu des onglets -->
      <div class="tab-content">

        <!-- Catégories -->
        <div *ngIf="activeTab() === 'categories'" class="tab-panel">
          <div class="panel-header">
            <h3>📁 Catégories</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openCategorieForm()"
            >
              ➕ Nouvelle Catégorie
            </button>
            <div *ngIf="!isAdmin()" class="read-only-notice">
              <span class="badge badge-info">👁️ Mode consultation seule</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let categorie of categories()" class="item-card">
              <div class="item-header">
                <h4>{{ categorie.nom }}</h4>
                <span class="badge" [class]="categorie.estValidee ? 'badge-success' : 'badge-warning'">
                  {{ categorie.estValidee ? 'Validée' : 'En attente' }}
                </span>
              </div>
              <p *ngIf="categorie.description">{{ categorie.description }}</p>
              <div class="item-stats">
                <span>{{ categorie.sousCategoriesCount || 0 }} sous-catégories</span>
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editCategorie(categorie)">
                  ✏️ Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteCategorie(categorie)">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Sous-catégories -->
        <div *ngIf="activeTab() === 'sous-categories'" class="tab-panel">
          <div class="panel-header">
            <h3>📂 Sous-catégories</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openSousCategorieForm()"
            >
              ➕ Nouvelle Sous-catégorie
            </button>
            <div *ngIf="!isAdmin()" class="read-only-notice">
              <span class="badge badge-info">👁️ Mode consultation seule</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let sousCategorie of sousCategories()" class="item-card">
              <div class="item-header">
                <h4>{{ sousCategorie.nom }}</h4>
                <span class="badge" [class]="sousCategorie.estValidee ? 'badge-success' : 'badge-warning'">
                  {{ sousCategorie.estValidee ? 'Validée' : 'En attente' }}
                </span>
              </div>
              <p *ngIf="sousCategorie.description">{{ sousCategorie.description }}</p>
              <div class="item-stats">
                <span>Catégorie ID: {{ sousCategorie.categorieId }}</span>
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editSousCategorie(sousCategorie)">
                  ✏️ Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteSousCategorie(sousCategorie)">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Marques -->
        <div *ngIf="activeTab() === 'marques'" class="tab-panel">
          <div class="panel-header">
            <h3>🏷️ Marques</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openMarqueForm()"
            >
              ➕ Nouvelle Marque
            </button>
            <div *ngIf="!isAdmin()" class="read-only-notice">
              <span class="badge badge-info">👁️ Mode consultation seule</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let marque of marques()" class="item-card">
              <div class="item-header">
                <h4>{{ marque.name }}</h4>
              </div>
              <div class="marque-logo" *ngIf="marque.logo">
                <img [src]="marque.logo" [alt]="marque.name" class="logo-img">
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editMarque(marque)">
                  ✏️ Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteMarque(marque)">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Formes -->
        <div *ngIf="activeTab() === 'formes'" class="tab-panel">
          <div class="panel-header">
            <h3>🔷 Formes</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openFormeForm()"
            >
              ➕ Nouvelle Forme
            </button>
            <div *ngIf="!isAdmin()" class="read-only-notice">
              <span class="badge badge-info">👁️ Mode consultation seule</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let forme of formes()" class="item-card">
              <div class="item-header">
                <h4>{{ forme.nom }}</h4>
              </div>
              <div class="forme-image" *ngIf="forme.imageUrl">
                <img [src]="forme.imageUrl" [alt]="forme.nom" class="forme-img">
              </div>
              <div class="item-stats">
                <span>Catégorie ID: {{ forme.categorieId }}</span>
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editForme(forme)">
                  ✏️ Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteForme(forme)">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Taux TVA -->
        <div *ngIf="activeTab() === 'tva'" class="tab-panel">
          <div class="panel-header">
            <h3>💰 Taux TVA</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openTauxTVAForm()"
            >
              ➕ Nouveau Taux TVA
            </button>
            <div *ngIf="!isAdmin()" class="read-only-notice">
              <span class="badge badge-info">👁️ Mode consultation seule</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let taux of tauxTVA()" class="item-card">
              <div class="item-header">
                <h4>{{ taux.libelle }}</h4>
                <span class="badge" [class]="taux.estActif ? 'badge-success' : 'badge-danger'">
                  {{ taux.estActif ? 'Actif' : 'Inactif' }}
                </span>
              </div>
              <div class="tva-rate">
                <span class="rate">{{ taux.taux }}%</span>
              </div>
              <p *ngIf="taux.description">{{ taux.description }}</p>
              <div class="item-stats">
                <span>Effet: {{ formatDate(taux.dateEffet) }}</span>
                <span *ngIf="taux.dateFin">Fin: {{ formatDate(taux.dateFin) }}</span>
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editTauxTVA(taux)">
                  ✏️ Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteTauxTVA(taux)">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal pour les formulaires -->
    <div class="modal-overlay" *ngIf="showModal()" (click)="closeModal()">
      <div class="modal-content" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h3>{{ modalTitle() }}</h3>
          <button class="close-btn" (click)="closeModal()">✕</button>
        </div>

        <form #form="ngForm" (ngSubmit)="onSubmit(form)" class="modal-form">
          <!-- Formulaire dynamique selon le type -->
          <ng-container [ngSwitch]="modalType()">

            <!-- Formulaire Catégorie -->
            <div *ngSwitchCase="'categorie'">
              <div class="form-group">
                <label>Nom de la catégorie *</label>
                <input
                  type="text"
                  name="nom"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: Lunettes de Vue"
                />
              </div>
              <div class="form-group">
                <label>Description</label>
                <textarea
                  name="description"
                  ngModel
                  class="form-control"
                  placeholder="Description de la catégorie..."
                ></textarea>
              </div>
            </div>

            <!-- Formulaire Sous-catégorie -->
            <div *ngSwitchCase="'sous-categorie'">
              <div class="form-group">
                <label>Catégorie parente *</label>
                <select name="categorieId" ngModel required class="form-control">
                  <option value="">Sélectionner une catégorie</option>
                  <option *ngFor="let cat of categories()" [value]="cat.id">
                    {{ cat.nom }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>Nom de la sous-catégorie *</label>
                <input
                  type="text"
                  name="nom"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: Montures Classiques"
                />
              </div>
              <div class="form-group">
                <label>Description</label>
                <textarea
                  name="description"
                  ngModel
                  class="form-control"
                  placeholder="Description de la sous-catégorie..."
                ></textarea>
              </div>
              <div class="form-group">
                <label>
                  <input type="checkbox" name="estValidee" ngModel>
                  Validée
                </label>
              </div>
            </div>

            <!-- Formulaire Marque -->
            <div *ngSwitchCase="'marque'">
              <div class="form-group">
                <label>Nom de la marque *</label>
                <input
                  type="text"
                  name="name"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: Ray-Ban"
                />
              </div>
              <div class="form-group">
                <label>URL du logo</label>
                <input
                  type="text"
                  name="logo"
                  ngModel
                  class="form-control"
                  placeholder="Ex: rayban-logo.png"
                />
              </div>
            </div>

            <!-- Formulaire Forme -->
            <div *ngSwitchCase="'forme'">
              <div class="form-group">
                <label>Catégorie *</label>
                <select name="categorieId" ngModel required class="form-control">
                  <option value="">Sélectionner une catégorie</option>
                  <option *ngFor="let cat of categories()" [value]="cat.id">
                    {{ cat.nom }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>Nom de la forme *</label>
                <input
                  type="text"
                  name="nom"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: Rectangulaire"
                />
              </div>
              <div class="form-group">
                <label>URL de l'image</label>
                <input
                  type="text"
                  name="imageUrl"
                  ngModel
                  class="form-control"
                  placeholder="Ex: forme-rectangulaire.png"
                />
              </div>
            </div>

            <!-- Formulaire Taux TVA -->
            <div *ngSwitchCase="'tva'">
              <div class="form-group">
                <label>Libellé *</label>
                <input
                  type="text"
                  name="libelle"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: TVA Standard"
                />
              </div>
              <div class="form-group">
                <label>Taux (%) *</label>
                <input
                  type="number"
                  name="taux"
                  ngModel
                  required
                  min="0"
                  max="100"
                  step="0.1"
                  class="form-control"
                  placeholder="Ex: 20"
                />
              </div>
              <div class="form-group">
                <label>Description</label>
                <textarea
                  name="description"
                  ngModel
                  class="form-control"
                  placeholder="Description du taux TVA..."
                ></textarea>
              </div>
              <div class="form-group">
                <label>Date d'effet *</label>
                <input
                  type="date"
                  name="dateEffet"
                  ngModel
                  required
                  class="form-control"
                />
              </div>
              <div class="form-group">
                <label>Date de fin</label>
                <input
                  type="date"
                  name="dateFin"
                  ngModel
                  class="form-control"
                />
              </div>
              <div class="form-group">
                <label>
                  <input type="checkbox" name="estActif" ngModel checked>
                  Actif
                </label>
              </div>
            </div>
          </ng-container>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" (click)="closeModal()">
              Annuler
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="!form.valid || isLoading()">
              {{ isEditMode() ? 'Modifier' : 'Créer' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  `,
  styles: [`
    /* ===== RÉFÉRENTIELS MODERNE - DESIGN SYSTEM ===== */
    .referentiels-container {
      padding: var(--spacing-8);
      max-width: 1400px;
      margin: 0 auto;
      background: transparent;
    }

    /* === HEADER === */
    .referentiels-header {
      background: var(--gradient-primary);
      border-radius: var(--border-radius-2xl);
      padding: var(--spacing-8);
      margin-bottom: var(--spacing-8);
      color: var(--white);
      box-shadow: var(--shadow-blue-lg);
      position: relative;
      overflow: hidden;
    }

    .referentiels-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
      opacity: 0.3;
    }

    .referentiels-title {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-extrabold);
      margin: 0 0 var(--spacing-2) 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    /* === ONGLETS MODERNES === */
    .tabs {
      display: flex;
      background: var(--white);
      border-radius: var(--border-radius-xl);
      padding: var(--spacing-2);
      margin-bottom: var(--spacing-8);
      box-shadow: var(--shadow-base);
      overflow-x: auto;
    }

    .tab-button {
      padding: var(--spacing-3) var(--spacing-6);
      border: none;
      background: transparent;
      cursor: pointer;
      border-radius: var(--border-radius-lg);
      font-weight: var(--font-weight-medium);
      color: var(--gray-600);
      transition: all var(--transition-fast);
      white-space: nowrap;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .tab-button.active {
      background: var(--primary-600);
      color: var(--white);
      box-shadow: var(--shadow-md);
    }

    .tab-button:hover:not(.active) {
      background: var(--primary-50);
      color: var(--primary-600);
    }

    /* === PANEL HEADER === */
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-8);
      background: var(--white);
      padding: var(--spacing-6);
      border-radius: var(--border-radius-xl);
      box-shadow: var(--shadow-base);
    }

    .panel-header h3 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--gray-900);
      margin: 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    /* === GRILLE D'ÉLÉMENTS === */
    .items-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: var(--spacing-6);
    }

    .item-card {
      background: var(--white);
      padding: var(--spacing-6);
      border-radius: var(--border-radius-xl);
      box-shadow: var(--shadow-base);
      transition: all var(--transition-base);
      position: relative;
      overflow: hidden;
    }

    .item-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--gradient-primary);
    }

    .item-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-lg);
    }

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-4);
    }

    .item-header h4 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
      color: var(--gray-900);
      margin: 0;
    }

    /* === BADGES MODERNES === */
    .badge {
      padding: var(--spacing-1) var(--spacing-3);
      border-radius: var(--border-radius-full);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      text-transform: uppercase;
      letter-spacing: 0.025em;
    }

    .badge-success {
      background: var(--success-100);
      color: var(--success-800);
      border: 1px solid var(--success-200);
    }

    .badge-warning {
      background: var(--warning-100);
      color: var(--warning-800);
      border: 1px solid var(--warning-200);
    }

    .badge-danger {
      background: var(--error-100);
      color: var(--error-800);
      border: 1px solid var(--error-200);
    }

    .item-stats {
      margin: var(--spacing-4) 0;
      color: var(--gray-600);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
    }

    .item-actions {
      display: flex;
      gap: var(--spacing-2);
      margin-top: var(--spacing-4);
    }

    /* === TAUX TVA === */
    .tva-rate {
      text-align: center;
      margin: var(--spacing-4) 0;
      padding: var(--spacing-4);
      background: var(--primary-50);
      border-radius: var(--border-radius-lg);
    }

    .rate {
      font-size: var(--font-size-4xl);
      font-weight: var(--font-weight-extrabold);
      color: var(--primary-600);
      display: block;
    }

    /* === IMAGES === */
    .marque-logo, .forme-image {
      text-align: center;
      margin: var(--spacing-4) 0;
      padding: var(--spacing-3);
      background: var(--gray-50);
      border-radius: var(--border-radius-lg);
    }

    .logo-img, .forme-img {
      max-width: 120px;
      max-height: 80px;
      object-fit: contain;
      border-radius: var(--border-radius-md);
    }

    /* === MODAL MODERNE === */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(4px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: var(--z-modal);
      animation: fadeIn 0.2s ease-out;
    }

    .modal-content {
      background: var(--white);
      border-radius: var(--border-radius-2xl);
      box-shadow: var(--shadow-2xl);
      max-width: 600px;
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
      animation: slideUp 0.3s ease-out;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--gray-200);
      background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    }

    .modal-header h3 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--gray-900);
      margin: 0;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: var(--font-size-xl);
      cursor: pointer;
      color: var(--gray-400);
      padding: var(--spacing-2);
      border-radius: var(--border-radius-lg);
      transition: var(--transition-fast);
    }

    .close-btn:hover {
      background: var(--gray-100);
      color: var(--gray-600);
    }

    .modal-form {
      padding: var(--spacing-6);
    }

    /* === FORMULAIRES === */
    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-group label {
      display: block;
      margin-bottom: var(--spacing-2);
      font-weight: var(--font-weight-semibold);
      color: var(--gray-700);
      font-size: var(--font-size-sm);
    }

    .form-control {
      width: 100%;
      padding: var(--spacing-3) var(--spacing-4);
      border: 2px solid var(--gray-300);
      border-radius: var(--border-radius-lg);
      font-size: var(--font-size-base);
      transition: all var(--transition-fast);
      background: var(--white);
    }

    .form-control:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      transform: translateY(-1px);
    }

    .form-actions {
      display: flex;
      gap: var(--spacing-3);
      justify-content: flex-end;
      padding: var(--spacing-6);
      border-top: 1px solid var(--gray-200);
      background: var(--gray-50);
    }

    /* === BOUTONS === */
    .btn {
      padding: var(--spacing-3) var(--spacing-4);
      border: none;
      border-radius: var(--border-radius-lg);
      cursor: pointer;
      font-weight: var(--font-weight-medium);
      transition: all var(--transition-fast);
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .btn-primary {
      background: var(--gradient-primary);
      color: var(--white);
      box-shadow: var(--shadow-blue);
    }

    .btn-primary:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
      transform: translateY(-1px);
      box-shadow: var(--shadow-blue-lg);
    }

    .btn-secondary {
      background: var(--white);
      color: var(--gray-700);
      border: 2px solid var(--gray-300);
      box-shadow: var(--shadow-sm);
    }

    .btn-secondary:hover {
      background: var(--gray-50);
      border-color: var(--gray-400);
      transform: translateY(-1px);
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--error-600), var(--error-500));
      color: var(--white);
      box-shadow: var(--shadow-sm);
    }

    .btn-danger:hover {
      background: linear-gradient(135deg, var(--error-700), var(--error-600));
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .btn-sm {
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--font-size-xs);
    }

    /* === MODE CONSULTATION === */
    .read-only-notice {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .badge-info {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: var(--white);
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--border-radius-full);
      font-size: var(--font-size-xs);
      font-weight: 600;
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-1);
      box-shadow: var(--shadow-sm);
    }

    /* === RESPONSIVE === */
    @media (max-width: 768px) {
      .referentiels-container {
        padding: var(--spacing-4);
      }

      .items-grid {
        grid-template-columns: 1fr;
      }

      .panel-header {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: stretch;
      }

      .tabs {
        flex-wrap: wrap;
      }

      .modal-content {
        margin: var(--spacing-4);
        max-width: calc(100vw - 2rem);
      }

      .item-actions {
        flex-direction: column;
      }
    }
  `]
})
export class ReferentielsComponent implements OnInit {
  // Signals pour les données
  categories = signal<Categorie[]>([]);
  sousCategories = signal<SousCategorie[]>([]);
  marques = signal<Marque[]>([]);
  formes = signal<Forme[]>([]);
  tauxTVA = signal<TauxTVA[]>([]);

  // Signals pour l'interface
  activeTab = signal('categories');
  showModal = signal(false);
  modalType = signal('');
  modalTitle = signal('');
  isEditMode = signal(false);
  isLoading = signal(false);
  selectedItem = signal<any>(null);

  // Vérification des permissions
  isAdmin = signal(false);

  // Configuration des onglets
  tabs = [
    { id: 'categories', label: 'Catégories', icon: '📁' },
    { id: 'sous-categories', label: 'Sous-catégories', icon: '📂' },
    { id: 'marques', label: 'Marques', icon: '🏷️' },
    { id: 'formes', label: 'Formes', icon: '🔷' },
    { id: 'tva', label: 'Taux TVA', icon: '💰' }
  ];

  constructor(
    private categorieService: CategorieService,
    private sousCategorieService: SousCategorieService,
    private marqueService: MarqueService,
    private formeService: FormeService,
    private tauxTVAService: TauxTVAService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.checkUserRole();
    this.loadAllData();
  }

  /**
   * Vérifier le rôle de l'utilisateur
   */
  private checkUserRole(): void {
    const currentUser = this.authService.getCurrentUser();
    this.isAdmin.set(currentUser?.role === 'Admin');
    console.log('👤 Rôle utilisateur:', currentUser?.role, '- Admin:', this.isAdmin());
  }

  /**
   * Charger toutes les données
   */
  loadAllData(): void {
    this.loadCategories();
    this.loadSousCategories();
    this.loadMarques();
    this.loadFormes();
    this.loadTauxTVA();
  }

  /**
   * Charger les catégories
   */
  loadCategories(): void {
    this.categorieService.getAll().subscribe({
      next: (data) => {
        console.log('✅ Catégories chargées:', data);
        this.categories.set(Array.isArray(data) ? data : []);
      },
      error: (error) => {
        console.error('❌ Erreur catégories:', error);
        this.categories.set([]);
      }
    });
  }

  /**
   * Charger les sous-catégories
   */
  loadSousCategories(): void {
    this.sousCategorieService.getAll().subscribe({
      next: (data) => {
        console.log('✅ Sous-catégories chargées:', data);
        this.sousCategories.set(Array.isArray(data) ? data : []);
      },
      error: (error) => {
        console.error('❌ Erreur sous-catégories:', error);
        this.sousCategories.set([]);
      }
    });
  }

  /**
   * Charger les marques
   */
  loadMarques(): void {
    this.marqueService.getAll().subscribe({
      next: (data) => {
        console.log('✅ Marques chargées:', data);
        this.marques.set(Array.isArray(data) ? data : []);
      },
      error: (error) => {
        console.error('❌ Erreur marques:', error);
        this.marques.set([]);
      }
    });
  }

  /**
   * Charger les formes
   */
  loadFormes(): void {
    this.formeService.getAll().subscribe({
      next: (data) => {
        console.log('✅ Formes chargées:', data);
        this.formes.set(Array.isArray(data) ? data : []);
      },
      error: (error) => {
        console.error('❌ Erreur formes:', error);
        this.formes.set([]);
      }
    });
  }

  /**
   * Charger les taux TVA
   */
  loadTauxTVA(): void {
    this.tauxTVAService.getAll().subscribe({
      next: (data) => {
        console.log('✅ Taux TVA chargés:', data);
        this.tauxTVA.set(Array.isArray(data) ? data : []);
      },
      error: (error) => {
        console.error('❌ Erreur taux TVA:', error);
        this.tauxTVA.set([]);
      }
    });
  }

  /**
   * Changer d'onglet
   */
  setActiveTab(tabId: string): void {
    this.activeTab.set(tabId);
  }

  /**
   * Ouvrir le formulaire de catégorie
   */
  openCategorieForm(): void {
    this.modalType.set('categorie');
    this.modalTitle.set('Nouvelle Catégorie');
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }

  /**
   * Ouvrir le formulaire de sous-catégorie
   */
  openSousCategorieForm(): void {
    this.modalType.set('sous-categorie');
    this.modalTitle.set('Nouvelle Sous-catégorie');
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }

  /**
   * Ouvrir le formulaire de marque
   */
  openMarqueForm(): void {
    this.modalType.set('marque');
    this.modalTitle.set('Nouvelle Marque');
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }

  /**
   * Ouvrir le formulaire de forme
   */
  openFormeForm(): void {
    this.modalType.set('forme');
    this.modalTitle.set('Nouvelle Forme');
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }

  /**
   * Ouvrir le formulaire de taux TVA
   */
  openTauxTVAForm(): void {
    this.modalType.set('tva');
    this.modalTitle.set('Nouveau Taux TVA');
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }

  /**
   * Éditer une catégorie
   */
  editCategorie(categorie: Categorie): void {
    this.modalType.set('categorie');
    this.modalTitle.set('Modifier Catégorie');
    this.isEditMode.set(true);
    this.selectedItem.set(categorie);
    this.showModal.set(true);
  }

  /**
   * Éditer une sous-catégorie
   */
  editSousCategorie(sousCategorie: SousCategorie): void {
    this.modalType.set('sous-categorie');
    this.modalTitle.set('Modifier Sous-catégorie');
    this.isEditMode.set(true);
    this.selectedItem.set(sousCategorie);
    this.showModal.set(true);
  }

  /**
   * Éditer une marque
   */
  editMarque(marque: Marque): void {
    this.modalType.set('marque');
    this.modalTitle.set('Modifier Marque');
    this.isEditMode.set(true);
    this.selectedItem.set(marque);
    this.showModal.set(true);
  }

  /**
   * Éditer une forme
   */
  editForme(forme: Forme): void {
    this.modalType.set('forme');
    this.modalTitle.set('Modifier Forme');
    this.isEditMode.set(true);
    this.selectedItem.set(forme);
    this.showModal.set(true);
  }

  /**
   * Éditer un taux TVA
   */
  editTauxTVA(taux: TauxTVA): void {
    this.modalType.set('tva');
    this.modalTitle.set('Modifier Taux TVA');
    this.isEditMode.set(true);
    this.selectedItem.set(taux);
    this.showModal.set(true);
  }

  /**
   * Fermer le modal
   */
  closeModal(): void {
    this.showModal.set(false);
    this.modalType.set('');
    this.modalTitle.set('');
    this.isEditMode.set(false);
    this.selectedItem.set(null);
  }

  /**
   * Soumettre le formulaire
   */
  onSubmit(form: any): void {
    if (!form.valid) return;

    this.isLoading.set(true);
    const formData = form.value;
    const modalType = this.modalType();

    switch (modalType) {
      case 'categorie':
        this.handleCategorieSubmit(formData);
        break;
      case 'sous-categorie':
        this.handleSousCategorieSubmit(formData);
        break;
      case 'marque':
        this.handleMarqueSubmit(formData);
        break;
      case 'forme':
        this.handleFormeSubmit(formData);
        break;
      case 'tva':
        this.handleTauxTVASubmit(formData);
        break;
    }
  }

  /**
   * Gérer la soumission de catégorie
   */
  private handleCategorieSubmit(formData: any): void {
    const categorieData: CategorieCreate = {
      nom: formData.nom,
      description: formData.description
    };

    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.categorieService.update(selectedItem.id, categorieData).subscribe({
        next: () => {
          this.loadCategories();
          this.closeModal();
          this.isLoading.set(false);
          alert('Catégorie mise à jour avec succès');
        },
        error: (error) => {
          console.error('Erreur mise à jour catégorie:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la mise à jour');
        }
      });
    } else {
      this.categorieService.create(categorieData).subscribe({
        next: () => {
          this.loadCategories();
          this.closeModal();
          this.isLoading.set(false);
          alert('Catégorie créée avec succès');
        },
        error: (error) => {
          console.error('Erreur création catégorie:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la création');
        }
      });
    }
  }

  /**
   * Gérer la soumission de sous-catégorie
   */
  private handleSousCategorieSubmit(formData: any): void {
    const sousCategorieData: SousCategorieCreate = {
      nom: formData.nom,
      categorieId: +formData.categorieId,
      description: formData.description,
      estValidee: formData.estValidee || false
    };

    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.sousCategorieService.update(selectedItem.id, sousCategorieData).subscribe({
        next: () => {
          this.loadSousCategories();
          this.closeModal();
          this.isLoading.set(false);
          alert('Sous-catégorie mise à jour avec succès');
        },
        error: (error) => {
          console.error('Erreur mise à jour sous-catégorie:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la mise à jour');
        }
      });
    } else {
      this.sousCategorieService.create(sousCategorieData).subscribe({
        next: () => {
          this.loadSousCategories();
          this.closeModal();
          this.isLoading.set(false);
          alert('Sous-catégorie créée avec succès');
        },
        error: (error) => {
          console.error('Erreur création sous-catégorie:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la création');
        }
      });
    }
  }

  /**
   * Gérer la soumission de marque
   */
  private handleMarqueSubmit(formData: any): void {
    const marqueData: MarqueCreate = {
      name: formData.name,
      logo: formData.logo
    };

    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.marqueService.update(selectedItem.id, marqueData).subscribe({
        next: () => {
          this.loadMarques();
          this.closeModal();
          this.isLoading.set(false);
          alert('Marque mise à jour avec succès');
        },
        error: (error) => {
          console.error('Erreur mise à jour marque:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la mise à jour');
        }
      });
    } else {
      this.marqueService.create(marqueData).subscribe({
        next: () => {
          this.loadMarques();
          this.closeModal();
          this.isLoading.set(false);
          alert('Marque créée avec succès');
        },
        error: (error) => {
          console.error('Erreur création marque:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la création');
        }
      });
    }
  }

  /**
   * Gérer la soumission de forme
   */
  private handleFormeSubmit(formData: any): void {
    const formeData: FormeCreate = {
      nom: formData.nom,
      categorieId: +formData.categorieId,
      imageUrl: formData.imageUrl
    };

    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.formeService.update(selectedItem.id, formeData).subscribe({
        next: () => {
          this.loadFormes();
          this.closeModal();
          this.isLoading.set(false);
          alert('Forme mise à jour avec succès');
        },
        error: (error) => {
          console.error('Erreur mise à jour forme:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la mise à jour');
        }
      });
    } else {
      this.formeService.create(formeData).subscribe({
        next: () => {
          this.loadFormes();
          this.closeModal();
          this.isLoading.set(false);
          alert('Forme créée avec succès');
        },
        error: (error) => {
          console.error('Erreur création forme:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la création');
        }
      });
    }
  }

  /**
   * Gérer la soumission de taux TVA
   */
  private handleTauxTVASubmit(formData: any): void {
    const tauxTVAData: TauxTVACreate = {
      libelle: formData.libelle,
      taux: +formData.taux,
      description: formData.description,
      estActif: formData.estActif || false,
      dateEffet: formData.dateEffet,
      dateFin: formData.dateFin
    };

    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.tauxTVAService.update(selectedItem.id, tauxTVAData).subscribe({
        next: () => {
          this.loadTauxTVA();
          this.closeModal();
          this.isLoading.set(false);
          alert('Taux TVA mis à jour avec succès');
        },
        error: (error) => {
          console.error('Erreur mise à jour taux TVA:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la mise à jour');
        }
      });
    } else {
      this.tauxTVAService.create(tauxTVAData).subscribe({
        next: () => {
          this.loadTauxTVA();
          this.closeModal();
          this.isLoading.set(false);
          alert('Taux TVA créé avec succès');
        },
        error: (error) => {
          console.error('Erreur création taux TVA:', error);
          this.isLoading.set(false);
          alert('Erreur lors de la création');
        }
      });
    }
  }

  /**
   * Supprimer une catégorie
   */
  deleteCategorie(categorie: Categorie): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${categorie.nom}" ?`)) {
      this.categorieService.delete(categorie.id).subscribe({
        next: () => {
          this.loadCategories();
          alert('Catégorie supprimée avec succès');
        },
        error: (error) => {
          console.error('Erreur suppression catégorie:', error);
          alert('Erreur lors de la suppression');
        }
      });
    }
  }

  /**
   * Supprimer une sous-catégorie
   */
  deleteSousCategorie(sousCategorie: SousCategorie): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la sous-catégorie "${sousCategorie.nom}" ?`)) {
      this.sousCategorieService.delete(sousCategorie.id).subscribe({
        next: () => {
          this.loadSousCategories();
          alert('Sous-catégorie supprimée avec succès');
        },
        error: (error) => {
          console.error('Erreur suppression sous-catégorie:', error);
          alert('Erreur lors de la suppression');
        }
      });
    }
  }

  /**
   * Supprimer une marque
   */
  deleteMarque(marque: Marque): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la marque "${marque.name}" ?`)) {
      this.marqueService.delete(marque.id).subscribe({
        next: () => {
          this.loadMarques();
          alert('Marque supprimée avec succès');
        },
        error: (error) => {
          console.error('Erreur suppression marque:', error);
          alert('Erreur lors de la suppression');
        }
      });
    }
  }

  /**
   * Supprimer une forme
   */
  deleteForme(forme: Forme): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la forme "${forme.nom}" ?`)) {
      this.formeService.delete(forme.id).subscribe({
        next: () => {
          this.loadFormes();
          alert('Forme supprimée avec succès');
        },
        error: (error) => {
          console.error('Erreur suppression forme:', error);
          alert('Erreur lors de la suppression');
        }
      });
    }
  }

  /**
   * Supprimer un taux TVA
   */
  deleteTauxTVA(taux: TauxTVA): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le taux TVA "${taux.libelle}" ?`)) {
      this.tauxTVAService.delete(taux.id).subscribe({
        next: () => {
          this.loadTauxTVA();
          alert('Taux TVA supprimé avec succès');
        },
        error: (error) => {
          console.error('Erreur suppression taux TVA:', error);
          alert('Erreur lors de la suppression');
        }
      });
    }
  }

  /**
   * Formater une date
   */
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR');
  }
}