using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class DemandeService : IDemandeService
    {
        private readonly AppDbContext _context;
        private readonly INotificationService _notificationService;

        public DemandeService(AppDbContext context, INotificationService notificationService)
        {
            _context = context;
            _notificationService = notificationService;
        }

        #region Demandes de catégories

        public async Task<DemandeCategorieDto> CreerDemandeCategorieAsync(CreateDemandeCategorieDto dto, int fournisseurId)
        {
            var demande = new DemandeCategorie
            {
                Nom = dto.Nom,
                Description = dto.Description,
                FournisseurId = fournisseurId,
                Statut = StatutDemande.EnAttente,
                DateDemande = DateTime.UtcNow
            };

            _context.DemandesCategories.Add(demande);
            await _context.SaveChangesAsync();

            // Envoyer notification aux admins
            await EnvoyerNotificationAuxAdminsAsync(
                "Nouvelle demande de catégorie",
                $"Le fournisseur a demandé la création de la catégorie '{dto.Nom}'"
            );

            return await GetDemandeCategorieByIdAsync(demande.Id) ?? throw new InvalidOperationException("Erreur lors de la création de la demande");
        }

        public async Task<IEnumerable<DemandeCategorieDto>> GetDemandesCategoriesAsync(int? fournisseurId = null)
        {
            var query = _context.DemandesCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.CategorieCreee)
                .AsQueryable();

            if (fournisseurId.HasValue)
            {
                query = query.Where(d => d.FournisseurId == fournisseurId.Value);
            }

            var demandes = await query.OrderByDescending(d => d.DateDemande).ToListAsync();

            return demandes.Select(MapToDemandeCategorieDto);
        }

        public async Task<DemandeCategorieDto?> GetDemandeCategorieByIdAsync(int id)
        {
            var demande = await _context.DemandesCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.CategorieCreee)
                .FirstOrDefaultAsync(d => d.Id == id);

            return demande != null ? MapToDemandeCategorieDto(demande) : null;
        }

        public async Task<DemandeCategorieDto> TraiterDemandeCategorieAsync(int demandeId, TraiterDemandeDto dto, int adminId)
        {
            var demande = await _context.DemandesCategories
                .Include(d => d.Fournisseur)
                .FirstOrDefaultAsync(d => d.Id == demandeId);

            if (demande == null)
                throw new KeyNotFoundException("Demande introuvable");

            if (demande.Statut != StatutDemande.EnAttente)
                throw new InvalidOperationException("Cette demande a déjà été traitée");

            demande.Statut = dto.Statut;
            demande.DateTraitement = DateTime.UtcNow;
            demande.AdminTraitantId = adminId;
            demande.CommentaireAdmin = dto.CommentaireAdmin;

            // Si acceptée, créer la catégorie
            if (dto.Statut == StatutDemande.Approuvee)
            {
                var categorie = new Categorie
                {
                    Nom = demande.Nom,
                    Description = demande.Description ?? "",
                    EstValidee = true
                };

                _context.Categories.Add(categorie);
                await _context.SaveChangesAsync();

                demande.CategorieCreeeId = categorie.Id;
            }

            await _context.SaveChangesAsync();

            // Envoyer notification au fournisseur
            var statutTexte = dto.Statut == StatutDemande.Approuvee ? "acceptée" : "refusée";
            await _notificationService.CreateNotificationAsync(new CreateNotificationDto
            {
                UtilisateurId = demande.FournisseurId,
                Titre = "Réponse à votre demande de catégorie",
                Message = $"Votre demande de catégorie '{demande.Nom}' a été {statutTexte}",
                Type = TypeNotification.ReponseDemandeCategorie
            });

            return await GetDemandeCategorieByIdAsync(demandeId) ?? throw new InvalidOperationException("Erreur lors du traitement");
        }

        #endregion

        #region Demandes de sous-catégories

        public async Task<DemandeSousCategorieDto> CreerDemandeSousCategorieAsync(CreateDemandeSousCategorieDto dto, int fournisseurId)
        {
            var demande = new DemandeSousCategorie
            {
                Nom = dto.Nom,
                Description = dto.Description,
                CategorieId = dto.CategorieId,
                FournisseurId = fournisseurId,
                Statut = StatutDemande.EnAttente,
                DateDemande = DateTime.UtcNow
            };

            _context.DemandesSousCategories.Add(demande);
            await _context.SaveChangesAsync();

            // Envoyer notification aux admins
            await EnvoyerNotificationAuxAdminsAsync(
                "Nouvelle demande de sous-catégorie",
                $"Le fournisseur a demandé la création de la sous-catégorie '{dto.Nom}'"
            );

            return await GetDemandeSousCategorieByIdAsync(demande.Id) ?? throw new InvalidOperationException("Erreur lors de la création de la demande");
        }

        public async Task<IEnumerable<DemandeSousCategorieDto>> GetDemandesSousCategoriesAsync(int? fournisseurId = null)
        {
            var query = _context.DemandesSousCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.Categorie)
                .Include(d => d.AdminTraitant)
                .Include(d => d.SousCategorieCreee)
                .AsQueryable();

            if (fournisseurId.HasValue)
            {
                query = query.Where(d => d.FournisseurId == fournisseurId.Value);
            }

            var demandes = await query.OrderByDescending(d => d.DateDemande).ToListAsync();

            return demandes.Select(MapToDemandeSousCategorieDto);
        }

        public async Task<DemandeSousCategorieDto?> GetDemandeSousCategorieByIdAsync(int id)
        {
            var demande = await _context.DemandesSousCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.Categorie)
                .Include(d => d.AdminTraitant)
                .Include(d => d.SousCategorieCreee)
                .FirstOrDefaultAsync(d => d.Id == id);

            return demande != null ? MapToDemandeSousCategorieDto(demande) : null;
        }

        public async Task<DemandeSousCategorieDto> TraiterDemandeSousCategorieAsync(int demandeId, TraiterDemandeDto dto, int adminId)
        {
            var demande = await _context.DemandesSousCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.Categorie)
                .FirstOrDefaultAsync(d => d.Id == demandeId);

            if (demande == null)
                throw new KeyNotFoundException("Demande introuvable");

            if (demande.Statut != StatutDemande.EnAttente)
                throw new InvalidOperationException("Cette demande a déjà été traitée");

            demande.Statut = dto.Statut;
            demande.DateTraitement = DateTime.UtcNow;
            demande.AdminTraitantId = adminId;
            demande.CommentaireAdmin = dto.CommentaireAdmin;

            // Si acceptée, créer la sous-catégorie
            if (dto.Statut == StatutDemande.Approuvee)
            {
                var sousCategorie = new SousCategorie
                {
                    Nom = demande.Nom,
                    Description = demande.Description ?? "",
                    CategorieId = demande.CategorieId,
                    EstValidee = true
                };

                _context.SousCategories.Add(sousCategorie);
                await _context.SaveChangesAsync();

                demande.SousCategorieCreeeId = sousCategorie.Id;
            }

            await _context.SaveChangesAsync();

            // Envoyer notification au fournisseur
            var statutTexte = dto.Statut == StatutDemande.Approuvee ? "acceptée" : "refusée";
            await _notificationService.CreateNotificationAsync(new CreateNotificationDto
            {
                UtilisateurId = demande.FournisseurId,
                Titre = "Réponse à votre demande de sous-catégorie",
                Message = $"Votre demande de sous-catégorie '{demande.Nom}' a été {statutTexte}",
                Type = TypeNotification.ReponseDemandeSousCategorie
            });

            return await GetDemandeSousCategorieByIdAsync(demandeId) ?? throw new InvalidOperationException("Erreur lors du traitement");
        }

        #endregion

        #region Statistiques

        public async Task<int> GetNombreDemandesEnAttenteAsync()
        {
            var categoriesEnAttente = await _context.DemandesCategories
                .CountAsync(d => d.Statut == StatutDemande.EnAttente);

            var sousCategoriesEnAttente = await _context.DemandesSousCategories
                .CountAsync(d => d.Statut == StatutDemande.EnAttente);

            return categoriesEnAttente + sousCategoriesEnAttente;
        }

        #endregion

        #region Méthodes privées

        private async Task EnvoyerNotificationAuxAdminsAsync(string titre, string contenu)
        {
            var admins = await _context.Users.OfType<Admin>().ToListAsync();

            foreach (var admin in admins)
            {
                await _notificationService.CreateNotificationAsync(new CreateNotificationDto
                {
                    UtilisateurId = admin.Id,
                    Titre = titre,
                    Message = contenu,
                    Type = TypeNotification.DemandeCategorie
                });
            }
        }

        private static DemandeCategorieDto MapToDemandeCategorieDto(DemandeCategorie demande)
        {
            return new DemandeCategorieDto
            {
                Id = demande.Id,
                Nom = demande.Nom,
                Description = demande.Description,
                FournisseurId = demande.FournisseurId,
                FournisseurNom = $"{demande.Fournisseur?.Prenom} {demande.Fournisseur?.Nom}",
                FournisseurEmail = demande.Fournisseur?.Email ?? "",
                Statut = demande.Statut,
                StatutLibelle = GetStatutLibelle(demande.Statut),
                DateDemande = demande.DateDemande,
                DateTraitement = demande.DateTraitement,
                AdminTraitantNom = demande.AdminTraitant != null ? $"{demande.AdminTraitant.Prenom} {demande.AdminTraitant.Nom}" : null,
                CommentaireAdmin = demande.CommentaireAdmin,
                CategorieCreeeId = demande.CategorieCreeeId,
                CategorieCreeeNom = demande.CategorieCreee?.Nom
            };
        }

        private static DemandeSousCategorieDto MapToDemandeSousCategorieDto(DemandeSousCategorie demande)
        {
            return new DemandeSousCategorieDto
            {
                Id = demande.Id,
                Nom = demande.Nom,
                Description = demande.Description,
                CategorieId = demande.CategorieId,
                CategorieNom = demande.Categorie?.Nom ?? "",
                FournisseurId = demande.FournisseurId,
                FournisseurNom = $"{demande.Fournisseur?.Prenom} {demande.Fournisseur?.Nom}",
                FournisseurEmail = demande.Fournisseur?.Email ?? "",
                Statut = demande.Statut,
                StatutLibelle = GetStatutLibelle(demande.Statut),
                DateDemande = demande.DateDemande,
                DateTraitement = demande.DateTraitement,
                AdminTraitantNom = demande.AdminTraitant != null ? $"{demande.AdminTraitant.Prenom} {demande.AdminTraitant.Nom}" : null,
                CommentaireAdmin = demande.CommentaireAdmin,
                SousCategorieCreeeId = demande.SousCategorieCreeeId,
                SousCategorieCreeeNom = demande.SousCategorieCreee?.Nom
            };
        }

        private static string GetStatutLibelle(StatutDemande statut)
        {
            return statut switch
            {
                StatutDemande.EnAttente => "En attente",
                StatutDemande.Approuvee => "Acceptée",
                StatutDemande.Rejetee => "Refusée",
                _ => "Inconnu"
            };
        }

        #endregion
    }
}
