﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SousCategoriesController : ControllerBase
    {
        private readonly ISousCategorieService _sousCategorieService;

        public SousCategoriesController(ISousCategorieService sousCategorieService)
        {
            _sousCategorieService = sousCategorieService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<SousCategorie>>> GetAll()
        {
            return Ok(await _sousCategorieService.GetAllAsync());
        }

        [HttpGet("enriched")]
        public async Task<ActionResult<IEnumerable<SousCategorieDto>>> GetAllEnriched()
        {
            var sousCategories = await _sousCategorieService.GetAllAsync();
            var sousCategoriesDto = sousCategories.Select(sc => new SousCategorieDto
            {
                Id = sc.Id,
                Nom = sc.Nom,
                Description = sc.Description ?? "",
                EstValidee = sc.EstValidee,
                CategorieId = sc.CategorieId,
                CategorieNom = sc.Categorie?.Nom ?? "Non définie",
                ProduitsCount = sc.Produits?.Count ?? 0
            });

            // Log pour déboguer
            foreach (var sc in sousCategoriesDto)
            {
                Console.WriteLine($"Sous-catégorie: {sc.Nom}, Catégorie: {sc.CategorieNom}, Produits: {sc.ProduitsCount}, Validée: {sc.EstValidee}");
            }

            return Ok(sousCategoriesDto);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<SousCategorie>> GetById(int id)
        {
            var sousCategorie = await _sousCategorieService.GetByIdAsync(id);
            return sousCategorie != null ? Ok(sousCategorie) : NotFound();
        }

        [HttpGet("by-categorie/{categorieId}")]
        public async Task<ActionResult<IEnumerable<SousCategorie>>> GetByCategorie(int categorieId)
        {
            return Ok(await _sousCategorieService.GetByCategorieAsync(categorieId));
        }

        [HttpGet("{id}/produits-count")]
        public async Task<ActionResult<int>> GetProduitsCount(int id)
        {
            return Ok(await _sousCategorieService.GetProduitsCountAsync(id));
        }

        [HttpGet("dropdown/{categorieId}")]
        public async Task<ActionResult<Dictionary<int, string>>> GetForDropdown(int categorieId)
        {
            return Ok(await _sousCategorieService.GetSousCategoriesForDropdownAsync(categorieId));
        }

        [HttpPost]
        public async Task<ActionResult<SousCategorie>> Create([FromBody] SousCategorieDto.Create dto)
        {
            try
            {
                var sousCategorie = new SousCategorie
                {
                    Nom = dto.Nom,
                    CategorieId = dto.CategorieId,
                    Description = dto.Description,
                    EstValidee = dto.EstValidee
                };

                var created = await _sousCategorieService.CreateAsync(sousCategorie);
                return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] SousCategorieDto.Update dto)
        {
            if (id != dto.Id) return BadRequest("ID mismatch");

            var sousCategorie = new SousCategorie
            {
                Id = dto.Id,
                Nom = dto.Nom,
                CategorieId = dto.CategorieId,
                Description = dto.Description,
                EstValidee = dto.EstValidee
            };

            try
            {
                await _sousCategorieService.UpdateAsync(sousCategorie);
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }


        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _sousCategorieService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
