using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SousCategoriesController : ControllerBase
    {
        private readonly ISousCategorieService _sousCategorieService;
        private readonly ApplicationDbContext _context;

        public SousCategoriesController(ISousCategorieService sousCategorieService, ApplicationDbContext context)
        {
            _sousCategorieService = sousCategorieService;
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<SousCategorie>>> GetAll()
        {
            return Ok(await _sousCategorieService.GetAllAsync());
        }

        [HttpGet("debug")]
        public async Task<ActionResult> DebugProduits([FromQuery] int? fournisseurId = null)
        {
            Console.WriteLine($"🔍 DEBUG: fournisseurId = {fournisseurId}");

            // Vérifier tous les produits
            var allProduits = await _context.Produits
                .Include(p => p.SousCategorie)
                .Include(p => p.Fournisseur)
                .ToListAsync();

            Console.WriteLine($"📦 Total produits dans la base: {allProduits.Count}");

            foreach (var produit in allProduits)
            {
                Console.WriteLine($"  🔸 Produit {produit.Id}: {produit.Nom} - Fournisseur: {produit.FournisseurId} - SousCategorie: {produit.SousCategorieId}");
            }

            if (fournisseurId.HasValue)
            {
                var produitsFournisseur = allProduits.Where(p => p.FournisseurId == fournisseurId.Value).ToList();
                Console.WriteLine($"📦 Produits du fournisseur {fournisseurId}: {produitsFournisseur.Count}");

                foreach (var produit in produitsFournisseur)
                {
                    Console.WriteLine($"  ✅ Produit fournisseur: {produit.Nom} - SousCategorie: {produit.SousCategorie?.Nom}");
                }
            }

            return Ok(new {
                totalProduits = allProduits.Count,
                produitsFournisseur = fournisseurId.HasValue ? allProduits.Count(p => p.FournisseurId == fournisseurId.Value) : 0,
                fournisseurId = fournisseurId
            });
        }

        [HttpGet("enriched")]
        public async Task<ActionResult<IEnumerable<SousCategorieDto>>> GetAllEnriched([FromQuery] int? fournisseurId = null)
        {
            Console.WriteLine($"🔍 GetAllEnriched appelé avec fournisseurId: {fournisseurId}");

            // Récupération des sous-catégories avec leurs relations
            var sousCategories = await _context.SousCategories
                .Include(sc => sc.Categorie)
                .Include(sc => sc.Produits)
                .AsNoTracking()
                .OrderBy(sc => sc.Nom)
                .ToListAsync();

            Console.WriteLine($"📦 {sousCategories.Count} sous-catégories récupérées de la base");

            // Mapping manuel avec logs détaillés
            var sousCategoriesDto = new List<SousCategorieDto>();

            foreach (var sc in sousCategories)
            {
                var totalProduits = sc.Produits?.Count ?? 0;
                var produitsFournisseur = 0;

                if (fournisseurId.HasValue && sc.Produits != null)
                {
                    produitsFournisseur = sc.Produits.Count(p => p.FournisseurId == fournisseurId.Value);
                    Console.WriteLine($"  📋 {sc.Nom}: {totalProduits} total, {produitsFournisseur} pour fournisseur {fournisseurId}");

                    // Log des produits de cette sous-catégorie
                    foreach (var produit in sc.Produits)
                    {
                        Console.WriteLine($"    🔸 Produit: {produit.Nom} - FournisseurId: {produit.FournisseurId}");
                    }
                }
                else
                {
                    produitsFournisseur = totalProduits;
                    Console.WriteLine($"  📋 {sc.Nom}: {totalProduits} produits (pas de filtrage)");
                }

                sousCategoriesDto.Add(new SousCategorieDto
                {
                    Id = sc.Id,
                    Nom = sc.Nom,
                    Description = sc.Description ?? "",
                    EstValidee = sc.EstValidee,
                    CategorieId = sc.CategorieId,
                    CategorieNom = sc.Categorie?.Nom ?? "Non définie",
                    ProduitsCount = produitsFournisseur
                });
            }

            return Ok(sousCategoriesDto);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<SousCategorie>> GetById(int id)
        {
            var sousCategorie = await _sousCategorieService.GetByIdAsync(id);
            return sousCategorie != null ? Ok(sousCategorie) : NotFound();
        }

        [HttpGet("by-categorie/{categorieId}")]
        public async Task<ActionResult<IEnumerable<SousCategorie>>> GetByCategorie(int categorieId)
        {
            return Ok(await _sousCategorieService.GetByCategorieAsync(categorieId));
        }

        [HttpGet("{id}/produits-count")]
        public async Task<ActionResult<int>> GetProduitsCount(int id)
        {
            return Ok(await _sousCategorieService.GetProduitsCountAsync(id));
        }

        [HttpGet("dropdown/{categorieId}")]
        public async Task<ActionResult<Dictionary<int, string>>> GetForDropdown(int categorieId)
        {
            return Ok(await _sousCategorieService.GetSousCategoriesForDropdownAsync(categorieId));
        }

        [HttpPost]
        public async Task<ActionResult<SousCategorie>> Create([FromBody] SousCategorieDto.Create dto)
        {
            try
            {
                var sousCategorie = new SousCategorie
                {
                    Nom = dto.Nom,
                    CategorieId = dto.CategorieId,
                    Description = dto.Description,
                    EstValidee = dto.EstValidee
                };

                var created = await _sousCategorieService.CreateAsync(sousCategorie);
                return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] SousCategorieDto.Update dto)
        {
            if (id != dto.Id) return BadRequest("ID mismatch");

            var sousCategorie = new SousCategorie
            {
                Id = dto.Id,
                Nom = dto.Nom,
                CategorieId = dto.CategorieId,
                Description = dto.Description,
                EstValidee = dto.EstValidee
            };

            try
            {
                await _sousCategorieService.UpdateAsync(sousCategorie);
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }


        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _sousCategorieService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
