using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WebApiPfe.DTOs.ReclamationDTOs;
using WebApiPfe.Models;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReclamationsController : ControllerBase
    {
        private readonly IReclamationService _reclamationService;
        private readonly ILogger<ReclamationsController> _logger;

        public ReclamationsController(
            IReclamationService reclamationService,
            ILogger<ReclamationsController> logger)
        {
            _reclamationService = reclamationService;
            _logger = logger;
        }

        /// <summary>
        /// Créer une nouvelle réclamation (Client uniquement)
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Client")]
        public async Task<ActionResult<ReclamationReadDto>> CreerReclamation([FromForm] CreateReclamationDto dto)
        {
            try
            {
                var clientId = GetCurrentUserId();
                var reclamation = await _reclamationService.CreerReclamationAsync(dto, clientId);
                return CreatedAtAction(nameof(ObtenirReclamation), new { id = reclamation.Id }, reclamation);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la réclamation");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir une réclamation par ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ReclamationReadDto>> ObtenirReclamation(int id)
        {
            try
            {
                var reclamation = await _reclamationService.ObtenirReclamationAsync(id);
                if (reclamation == null)
                    return NotFound(new { message = "Réclamation non trouvée" });

                // Vérifier les permissions
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                if (userRole == "Client" && reclamation.ClientId != userId)
                    return Forbid();

                if (userRole == "Fournisseur" && !await _reclamationService.PeutTraiterReclamationAsync(userId, id))
                    return Forbid();

                return Ok(reclamation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de la réclamation {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les réclamations avec filtres
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<ReclamationReadDto>>> ObtenirReclamations([FromQuery] ReclamationFilterDto filter)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                List<ReclamationReadDto> reclamations = userRole switch
                {
                    "Client" => await _reclamationService.ObtenirReclamationsClientAsync(userId, filter),
                    "Fournisseur" => await _reclamationService.ObtenirReclamationsFournisseurAsync(userId, filter),
                    "Admin" => await _reclamationService.ObtenirReclamationsAdminAsync(filter),
                    _ => new List<ReclamationReadDto>()
                };

                return Ok(reclamations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des réclamations");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Traiter une réclamation (Admin/Fournisseur)
        /// </summary>
        [HttpPut("{id}/traiter")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<ReclamationReadDto>> TraiterReclamation(int id, [FromBody] UpdateReclamationDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();

                if (!await _reclamationService.PeutTraiterReclamationAsync(userId, id))
                    return Forbid();

                var reclamation = await _reclamationService.TraiterReclamationAsync(id, dto, userId);
                return Ok(reclamation);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du traitement de la réclamation {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Résoudre une réclamation (Admin/Fournisseur)
        /// </summary>
        [HttpPut("{id}/resoudre")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<ReclamationReadDto>> ResoudreReclamation(int id, [FromBody] string resolution)
        {
            try
            {
                var userId = GetCurrentUserId();

                if (!await _reclamationService.PeutTraiterReclamationAsync(userId, id))
                    return Forbid();

                var reclamation = await _reclamationService.ResoudreReclamationAsync(id, resolution, userId);
                return Ok(reclamation);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la résolution de la réclamation {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Fermer une réclamation (Admin/Fournisseur)
        /// </summary>
        [HttpPut("{id}/fermer")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<ReclamationReadDto>> FermerReclamation(int id, [FromBody] string commentaire)
        {
            try
            {
                var userId = GetCurrentUserId();

                if (!await _reclamationService.PeutTraiterReclamationAsync(userId, id))
                    return Forbid();

                var reclamation = await _reclamationService.FermerReclamationAsync(id, commentaire, userId);
                return Ok(reclamation);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la fermeture de la réclamation {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Rejeter une réclamation (Admin uniquement)
        /// </summary>
        [HttpPut("{id}/rejeter")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ReclamationReadDto>> RejeterReclamation(int id, [FromBody] string motif)
        {
            try
            {
                var userId = GetCurrentUserId();
                var reclamation = await _reclamationService.RejeterReclamationAsync(id, motif, userId);
                return Ok(reclamation);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du rejet de la réclamation {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Supprimer une réclamation (Admin uniquement)
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> SupprimerReclamation(int id)
        {
            try
            {
                var success = await _reclamationService.SupprimerReclamationAsync(id);
                if (!success)
                    return NotFound(new { message = "Réclamation non trouvée" });

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression de la réclamation {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les statistiques des réclamations
        /// </summary>
        [HttpGet("statistiques")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<ReclamationStatsDto>> ObtenirStatistiques()
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                ReclamationStatsDto stats = userRole switch
                {
                    "Admin" => await _reclamationService.ObtenirStatistiquesAsync(),
                    "Fournisseur" => await _reclamationService.ObtenirStatistiquesFournisseurAsync(userId),
                    _ => new ReclamationStatsDto()
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Vérifier si un client peut créer une réclamation pour une commande
        /// </summary>
        [HttpGet("peut-creer/{commandeId}")]
        [Authorize(Roles = "Client")]
        public async Task<ActionResult<bool>> PeutCreerReclamation(int commandeId)
        {
            try
            {
                var clientId = GetCurrentUserId();
                var peutCreer = await _reclamationService.PeutCreerReclamationAsync(clientId, commandeId);
                return Ok(peutCreer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la vérification des permissions");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les types de réclamations disponibles
        /// </summary>
        [HttpGet("types")]
        public ActionResult<Dictionary<int, string>> ObtenirTypesReclamations()
        {
            var types = new Dictionary<int, string>();
            foreach (TypeReclamation type in Enum.GetValues<TypeReclamation>())
            {
                types[(int)type] = type switch
                {
                    TypeReclamation.ProduitDefectueux => "Produit défectueux",
                    TypeReclamation.LivraisonRetard => "Retard de livraison",
                    TypeReclamation.ProduitNonConforme => "Produit non conforme",
                    TypeReclamation.ServiceClient => "Service client",
                    TypeReclamation.Remboursement => "Demande de remboursement",
                    TypeReclamation.Autre => "Autre",
                    _ => "Non défini"
                };
            }
            return Ok(types);
        }

        /// <summary>
        /// Obtenir les statuts de réclamations disponibles
        /// </summary>
        [HttpGet("statuts")]
        public ActionResult<Dictionary<int, string>> ObtenirStatutsReclamations()
        {
            var statuts = new Dictionary<int, string>();
            foreach (StatutReclamation statut in Enum.GetValues<StatutReclamation>())
            {
                statuts[(int)statut] = statut switch
                {
                    StatutReclamation.EnAttente => "En attente",
                    StatutReclamation.EnCours => "En cours de traitement",
                    StatutReclamation.Resolue => "Résolue",
                    StatutReclamation.Fermee => "Fermée",
                    StatutReclamation.Rejetee => "Rejetée",
                    _ => "Non défini"
                };
            }
            return Ok(statuts);
        }

        // Méthodes d'aide privées
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 0;
        }

        private string GetCurrentUserRole()
        {
            return User.FindFirst(ClaimTypes.Role)?.Value ?? "";
        }
    }
}
