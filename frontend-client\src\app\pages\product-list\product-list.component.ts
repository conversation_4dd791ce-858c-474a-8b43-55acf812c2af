import { CommonModule, CurrencyPipe } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { PaginatorModule } from 'primeng/paginator';
import { MatIconModule } from '@angular/material/icon';
import { FilterComponent } from 'src/app/components/filter/filter.component';
import { MarqueService } from 'src/app/services/marque.service';
import { FormeService } from 'src/app/services/forme.service';
import { ProduitService } from 'src/app/services/produit.service';
import { SousCategorieService } from 'src/app/services/sous-categorie.service';
import { CategorieService } from 'src/app/services/categorie.service';
import { Observable } from 'rxjs';

import { MarqueDto } from 'src/app/models/MarqueDto';
import { FormeDto } from 'src/app/models/FormeDto';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { CategorieDto } from 'src/app/models/CategorieDto';
import { SousCategorieDto } from 'src/app/models/SousCategorieDto';

import { forkJoin } from 'rxjs';
import { Router } from '@angular/router';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FavoritesService } from 'src/app/services/favorites.service';
import { CartService } from 'src/app/services/cart.service';
import { ImageUrlService } from 'src/app/services/image-url.service';
import { AuthService } from 'src/app/auth/auth.service';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    CurrencyPipe,
    ButtonModule,
    TagModule,
    PaginatorModule,
    MatIconModule,
    FilterComponent,
    MatProgressSpinnerModule,
  ],
  providers: [MarqueService, FormeService],
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss'],
})
export class ProductListComponent implements OnChanges {
  @Input() produits: ProduitCard[] = [];
  @Input() marques: MarqueDto[] = [];
  @Input() formes: FormeDto[] = [];
  @Input() categorieId: number | null = null;
  @Input() sousCategorieId: number | null = null;
  @Input() loading: boolean = false;

  showFilter = false;
  produitsFiltres: ProduitCard[] = [];
  produitsParPage = 20;
  pageActuelle = 0;
  categories: CategorieDto[] = [];
  sousCategories: SousCategorieDto[] = [];
  moyenneNote = 0;
  nombreAvis = 0;
  repartitionAvis: { [key: number]: number } = {};
  nouvelleNote = 0;
  prixMin: number = 0;
  prixMax: number = 0;

  constructor(
    private produitService: ProduitService,
    private authService: AuthService,
    private cartService: CartService,
    private favoritesService: FavoritesService,
    private marqueService: MarqueService,
    private formeService: FormeService,
    private sousCategorieService: SousCategorieService,
    private categorieService: CategorieService,
    private router: Router,
    private changeDetector: ChangeDetectorRef,
    public imageUrlService: ImageUrlService
  ) {}

  ngOnInit() {
    this.loadMarquesEtFormes();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.hasRelevantChange(changes)) {
      this.initializeFilteredProducts();
      this.calculatePriceRange();
      this.verifyDataConsistency();
      this.changeDetector.markForCheck();

      // Debug des données d'avis
      if (changes['produits'] && this.produits?.length > 0) {
        console.log('✅ Product-list - Produits reçus:', this.produits.length);
        console.log('📊 Product-list - Premier produit avis:', {
          nom: this.produits[0]?.nom,
          noteMoyenne: this.produits[0]?.noteMoyenne,
          nombreAvis: this.produits[0]?.nombreAvis
        });
      }
    }

    if (changes['categorieId'] || changes['sousCategorieId']) {
      console.log('🔄 Catégorie/Sous-catégorie changée, rechargement des formes...');
      this.loadFormesForCurrentCategorie().subscribe({
        next: (formes) => {
          this.formes = formes;
          console.log('✅ Formes rechargées:', formes.length);
        },
        error: (error) => {
          console.error('❌ Erreur lors du rechargement des formes:', error);
          this.formes = [];
        }
      });
    }
  }

  private hasRelevantChange(changes: SimpleChanges): boolean {
    return !!changes['produits'] || !!changes['marques'] || !!changes['formes'];
  }

  private initializeFilteredProducts(): void {
    this.produitsFiltres = [...this.produits];
  }

  private calculatePriceRange(): void {
    if (this.produits?.length > 0) {
      const prix = this.produits
        .map((p) => p.prixFinalTTC)
        .filter((p): p is number => p != null);
      this.prixMin = prix.length ? Math.min(...prix) : 0;
      this.prixMax = prix.length ? Math.max(...prix) : 0;
    } else {
      this.prixMin = 0;
      this.prixMax = 0;
    }
  }

  private verifyDataConsistency(): void {
    if (!this.produits?.length) return;

    this.produits.forEach((p) => {
      if (p.marque && !this.marques.some((m) => m.id === p.marque?.id)) {
        console.warn(`Marque inconnue pour produit ${p.id}:`, p.marque);
      }
      if (p.forme && !this.formes.some((f) => f.id === p.forme?.id)) {
        console.warn(`Forme inconnue pour produit ${p.id}:`, p.forme);
      }
    });
  }

  private loadMarquesEtFormes(): void {
    this.loading = true;

    forkJoin([
      this.marqueService.getAll(),
      this.loadFormesForCurrentCategorie(),
      this.sousCategorieService.getAll(),
      this.categorieService.getAll(),
    ]).subscribe({
      next: ([marques, formes, sousCategories, categories]) => {
        this.marques = marques;
        this.formes = formes;
        this.sousCategories = sousCategories;
        this.categories = categories;
        this.loading = false;
        this.extraireOptionsFiltre();
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  private loadFormesForCurrentCategorie(): Observable<FormeDto[]> {
    // Si on a une catégorie directe, utiliser l'endpoint filtré
    if (this.categorieId) {
      console.log('🔍 Chargement des formes pour catégorie:', this.categorieId);
      return this.formeService.getByCategorie(this.categorieId);
    }

    // Si on a une sous-catégorie, récupérer la catégorie parente
    if (this.sousCategorieId) {
      console.log('🔍 Chargement des formes pour sous-catégorie:', this.sousCategorieId);
      const sousCat = this.sousCategories.find(
        (sc) => sc.id === this.sousCategorieId
      );
      if (sousCat?.categorieId) {
        return this.formeService.getByCategorie(sousCat.categorieId);
      }
    }

    // Sinon, charger toutes les formes
    console.log('🔍 Chargement de toutes les formes');
    return this.formeService.getAll();
  }

  private filterFormesByCategorie(formes: FormeDto[]): FormeDto[] {
    if (this.categorieId) {
      return formes.filter((f) => f.categorieId === this.categorieId);
    }
    if (this.sousCategorieId) {
      const sousCat = this.sousCategories.find(
        (sc) => sc.id === this.sousCategorieId
      );
      if (!sousCat) return formes;

      const categorieParenteId = sousCat.categorieId;
      return formes.filter((f) => f.categorieId === categorieParenteId);
    }
    return formes;
  }

  private extraireOptionsFiltre(): void {}

  toggleFilter(): void {
    this.showFilter = !this.showFilter;
  }

  closeFilter() {
    this.showFilter = false;
  }

  onFilterChanged(filters: {
    sexe?: string[];
    marques?: number[];
    formes?: number[];
    prixMin?: number | null;
    prixMax?: number | null;
    sort?: string | null;
  }): void {
    if (!this.produits?.length) return;

    let filtered = structuredClone(this.produits);

    if (filters.sexe?.length) {
      filtered = filtered.filter((p) =>
        filters.sexe!.includes(
          this.determinerSexeParSousCategorie(p.sousCategorie?.id ?? 0) ?? ''
        )
      );
    }

    if (filters.marques?.length) {
      filtered = filtered.filter(
        (p) => p.marque && filters.marques!.includes(p.marque.id)
      );
    }

    if (filters.formes?.length) {
      filtered = filtered.filter(
        (p) => p.forme && filters.formes!.includes(p.forme.id)
      );
    }

    if (filters.prixMin != null) {
      filtered = filtered.filter(
        (p) => (p.prixFinalTTC ?? 0) >= filters.prixMin!
      );
    }

    if (filters.prixMax != null) {
      filtered = filtered.filter(
        (p) => (p.prixFinalTTC ?? 0) <= filters.prixMax!
      );
    }

    if (filters.sort) {
      this.applySorting(filtered, filters.sort);
    }

    this.produitsFiltres = filtered;
    this.pageActuelle = 0;
  }

  private applySorting(products: ProduitCard[], sortOption: string): void {
    switch (sortOption) {
      case 'priceAsc':
        products.sort((a, b) => (a.prixFinalTTC ?? 0) - (b.prixFinalTTC ?? 0));
        break;
      case 'priceDesc':
        products.sort((a, b) => (b.prixFinalTTC ?? 0) - (a.prixFinalTTC ?? 0));
        break;
      case 'nameAsc':
        products.sort((a, b) => a.nom.localeCompare(b.nom));
        break;
      case 'discountDesc':
        products.sort(
          (a, b) => (b.tauxRemiseTotale ?? 0) - (a.tauxRemiseTotale ?? 0)
        );
        break;
      case 'recent':
        products.sort(
          (a, b) =>
            new Date(b.dateAjout).getTime() - new Date(a.dateAjout).getTime()
        );
        break;
    }
  }

  onClearFilters(): void {
    this.produitsFiltres = structuredClone(this.produits);
    this.pageActuelle = 0;
  }

  get produitsPagination(): ProduitCard[] {
    const debut = this.pageActuelle * this.produitsParPage;
    return this.produitsFiltres.slice(debut, debut + this.produitsParPage);
  }

  get totalProduits(): number {
    return this.produitsFiltres.length;
  }
onFilterApplied() {
  this.closeFilter();
}

  changerPage(pageEvent: any): void {
    this.pageActuelle = pageEvent.page;
    window.scrollTo(0, 0);
  }

  determinerSexeParSousCategorie(sousCategorieId: number): string | null {
    if ([2, 5].includes(sousCategorieId)) return 'femme';
    if ([3, 6].includes(sousCategorieId)) return 'homme';
    if ([4, 7].includes(sousCategorieId)) return 'enfant';
    return null;
  }

  estNouvelleArrivee(produit: ProduitCard): boolean {
    const aujourdHui = new Date();
    const dateAjout = new Date(produit.dateAjout);
    const differenceTemps = aujourdHui.getTime() - dateAjout.getTime();
    const differenceJours = differenceTemps / (1000 * 3600 * 24);
    return differenceJours <= 30;
  }

  goToProductDetails(productId: number, queryParams?: Record<string, any>): void {
    if (!productId || productId <= 0) {
      console.error('ID produit invalide:', productId);
      return;
    }
    this.router
      .navigate(['/products', productId], { queryParams })
      .catch((err) => console.error('Erreur de navigation:', err));
  }
  ajouterAuPanier(produit: ProduitCard) {
    const clientId = this.getClientId();
    if (!clientId) {
      alert('Veuillez vous connecter pour ajouter au panier.');
      return;
    }

    const item = { produitId: produit.id, quantite: 1 };

    this.cartService.addItemByClientId(clientId, item).subscribe({
      next: (result) => {
        console.log('Item ajouté/mis à jour avec succès:', result);
        // Message plus informatif selon le cas
        if (result.quantite > 1) {
          alert(`Quantité mise à jour ! Vous avez maintenant ${result.quantite} de ce produit dans votre panier.`);
        } else {
          alert('Produit ajouté au panier !');
        }
      },
      error: err => {
        console.error('Erreur lors de l\'ajout au panier:', err);
        console.error('Détails:', err.error);

        // Messages d'erreur plus spécifiques
        let errorMessage = 'Erreur lors de l\'ajout au panier';
        if (err.error?.message) {
          errorMessage += ': ' + err.error.message;
        } else if (typeof err.error === 'string') {
          errorMessage += ': ' + err.error;
        } else if (err.message) {
          errorMessage += ': ' + err.message;
        }

        alert(errorMessage);
      }
    });
  }

  toggleFavori(produit: ProduitCard) {
    const clientId = this.getClientId();
    if (!clientId) {
      alert('Veuillez vous connecter pour gérer vos favoris.');
      return;
    }
    this.favoritesService.verifierFavori(clientId, produit.id).subscribe({
      next: isFav => {
        if (isFav) {
          this.favoritesService.supprimerFavoriParProduitId(clientId, produit.id).subscribe(() => {
            alert('Produit retiré des favoris');
          });
        } else {
          this.favoritesService.ajouterFavori(clientId, produit.id).subscribe(() => {
            alert('Produit ajouté aux favoris');
          });
        }
      },
      error: err => console.error(err)
    });
  }
  private getClientId(): number | null {
    const user = this.authService.getCurrentUser();
    return user ? user.id : null;
  }

  getMainImage(produit: ProduitCard): string {
    const main = produit.images?.find((img) => img.isMain);
    const imageUrl = main?.imageUrl || produit.imageUrl;
    return this.imageUrlService.getProduitImageUrl(imageUrl);
  }
}
