import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subscription, interval, forkJoin } from 'rxjs';
import { NotificationService, NotificationDto } from '../../services/notification.service';
import { AuthService } from '../../services/auth.service';
import { DemandeService, StatutDemande } from '../../services/demande.service';
import { AdminAuthService } from '../../services/admin-auth.service';


@Component({
  selector: 'app-notification-icon',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="notification-wrapper">
      <button
        class="notification-btn"
        [title]="'Notifications'"
        (click)="toggleMenu()">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
          <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
        </svg>
        <span *ngIf="totalUnreadCount > 0" class="notification-badge">{{ totalUnreadCount }}</span>
      </button>

      <div *ngIf="showMenu" class="notification-menu" (click)="$event.stopPropagation()">
        <div class="notification-header">
          <h4>Notifications</h4>
          <button
            *ngIf="unreadCount > 0"
            (click)="markAllAsRead()"
            class="mark-all-read-btn">
            Tout marquer comme lu
          </button>
        </div>

        <div class="notification-divider"></div>

        <!-- Section demandes pour les admins -->
        <div *ngIf="isAdmin() && demandesEnAttente > 0" class="demandes-section">
          <div class="section-header">
            <span>🔔 Demandes en attente</span>
            <span class="demandes-count">{{ demandesEnAttente }}</span>
          </div>
          <div class="demande-item" (click)="viewDemandes()">
            <div class="demande-content">
              <div class="demande-title">Nouvelles demandes de catégories</div>
              <div class="demande-message">{{ demandesEnAttente }} demande(s) en attente de traitement</div>
            </div>
          </div>
        </div>

        <div *ngIf="isAdmin() && demandesEnAttente > 0" class="notification-divider"></div>

        <div class="notification-list">
          <div *ngIf="notifications.length === 0" class="no-notifications">
            <svg class="empty-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <p>Aucune notification</p>
          </div>

          <div
            *ngFor="let notification of notifications.slice(0, 5)"
            class="notification-item"
            [class.unread]="!notification.estLue"
            (click)="markAsRead(notification)">

            <div class="notification-content">
              <p class="notification-text">{{ notification.contenu }}</p>
              <span class="notification-date">{{ formatDate(notification.dateEnvoi) }}</span>
            </div>

            <button
              class="delete-btn"
              (click)="deleteNotification(notification.id, $event)">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <div *ngIf="notifications.length > 5" class="view-all">
            <button (click)="viewAllNotifications()">
              Voir toutes les notifications
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .notification-wrapper {
      position: relative;
      display: inline-block;
    }

    .notification-btn {
      position: relative;
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      transition: background-color 0.2s;
      color: #666;
    }

    .notification-btn:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }

    .notification-badge {
      position: absolute;
      top: 2px;
      right: 2px;
      background: #f44336;
      color: white;
      border-radius: 50%;
      min-width: 18px;
      height: 18px;
      font-size: 11px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .notification-menu {
      position: absolute;
      top: 100%;
      right: 0;
      width: 350px;
      max-height: 400px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      overflow: hidden;
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .notification-header h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .mark-all-read-btn {
      background: none;
      border: none;
      color: white;
      font-size: 12px;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .mark-all-read-btn:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .notification-divider {
      height: 1px;
      background-color: #e0e0e0;
    }

    .notification-list {
      max-height: 300px;
      overflow-y: auto;
    }

    .no-notifications {
      text-align: center;
      padding: 32px 16px;
      color: #666;
    }

    .empty-icon {
      margin-bottom: 16px;
      color: #ccc;
    }

    .notification-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px;
      border-bottom: 1px solid #e0e0e0;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .notification-item:hover {
      background-color: #f5f5f5;
    }

    .notification-item.unread {
      background-color: #e3f2fd;
      border-left: 3px solid #2196f3;
    }

    .notification-content {
      flex: 1;
      margin-right: 8px;
    }

    .notification-text {
      margin: 0 0 4px 0;
      font-size: 14px;
      line-height: 1.4;
    }

    .notification-date {
      font-size: 12px;
      color: #666;
    }

    .delete-btn {
      background: none;
      border: none;
      cursor: pointer;
      opacity: 0;
      transition: opacity 0.2s;
      width: 32px;
      height: 32px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
    }

    .delete-btn:hover {
      background-color: rgba(244, 67, 54, 0.1);
      color: #f44336;
    }

    .notification-item:hover .delete-btn {
      opacity: 1;
    }

    .view-all {
      text-align: center;
      padding: 16px;
      border-top: 1px solid #e0e0e0;
    }

    .view-all button {
      background: none;
      border: none;
      color: #2196f3;
      cursor: pointer;
      font-size: 14px;
      padding: 8px 16px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .view-all button:hover {
      background-color: rgba(33, 150, 243, 0.1);
    }

    .demandes-section {
      padding: 12px 16px;
      background: #f8f9ff;
      border-left: 4px solid #3b82f6;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-weight: 500;
      color: #1e293b;
      font-size: 14px;
    }

    .demandes-count {
      background: #3b82f6;
      color: white;
      border-radius: 12px;
      padding: 2px 8px;
      font-size: 12px;
      font-weight: bold;
    }

    .demande-item {
      cursor: pointer;
      padding: 8px 0;
      transition: opacity 0.2s;
    }

    .demande-item:hover {
      opacity: 0.8;
    }

    .demande-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .demande-title {
      font-weight: 500;
      color: #1e293b;
      font-size: 14px;
    }

    .demande-message {
      color: #64748b;
      font-size: 12px;
    }
  `]
})
export class NotificationIconComponent implements OnInit, OnDestroy {
  notifications: NotificationDto[] = [];
  unreadCount = 0;
  showMenu = false;
  demandesEnAttente = 0;
  private subscriptions = new Subscription();

  constructor(
    private notificationService: NotificationService,
    private authService: AuthService,
    private router: Router,
    private demandeService: DemandeService,
    private adminAuthService: AdminAuthService
  ) {}

  ngOnInit() {
    this.subscriptions.add(
      this.notificationService.notifications$.subscribe(notifications => {
        this.notifications = notifications;
      })
    );

    this.subscriptions.add(
      this.notificationService.unreadCount$.subscribe(count => {
        this.unreadCount = count;
      })
    );

    this.loadNotifications();
    this.loadDemandesEnAttente();

    // Actualiser les demandes toutes les 30 secondes pour les admins
    if (this.isAdmin()) {
      this.subscriptions.add(
        interval(30000).subscribe(() => {
          this.loadDemandesEnAttente();
        })
      );
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  toggleMenu() {
    this.showMenu = !this.showMenu;
    if (this.showMenu) {
      this.loadNotifications();
      // Fermer le menu si on clique ailleurs
      document.addEventListener('click', this.closeMenu.bind(this));
    } else {
      document.removeEventListener('click', this.closeMenu.bind(this));
    }
  }

  closeMenu() {
    this.showMenu = false;
    document.removeEventListener('click', this.closeMenu.bind(this));
  }

  loadNotifications() {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser?.id) {
      this.notificationService.getUserNotifications(currentUser.id).subscribe();
    }
  }

  markAsRead(notification: NotificationDto) {
    if (!notification.estLue) {
      this.notificationService.markAsRead(notification.id).subscribe();
    }
  }

  markAllAsRead() {
    const unreadNotifications = this.notifications.filter(n => !n.estLue);
    unreadNotifications.forEach(notification => {
      this.notificationService.markAsRead(notification.id).subscribe();
    });
  }

  deleteNotification(notificationId: number, event: Event) {
    event.stopPropagation();
    this.notificationService.deleteNotification(notificationId).subscribe();
  }

  viewAllNotifications() {
    this.router.navigate(['/notifications']);
  }

  viewDemandes() {
    this.router.navigate(['/admin/dashboard/demandes']);
    this.closeMenu();
  }

  isAdmin(): boolean {
    // Vérifier si l'utilisateur est admin via le service principal
    const isAdminViaAuth = this.authService.isAdmin();
    // Vérifier si l'utilisateur est admin via le service admin
    const isAdminViaAdminAuth = this.adminAuthService.getCurrentUser() !== null;

    return isAdminViaAuth || isAdminViaAdminAuth;
  }

  loadDemandesEnAttente() {
    if (this.isAdmin()) {
      forkJoin({
        categories: this.demandeService.getDemandesCategoriesByStatut(StatutDemande.EnAttente),
        sousCategories: this.demandeService.getDemandesSousCategoriesByStatut(StatutDemande.EnAttente)
      }).subscribe({
        next: (result) => {
          this.demandesEnAttente = result.categories.length + result.sousCategories.length;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des demandes en attente:', error);
        }
      });
    }
  }

  get totalUnreadCount(): number {
    return this.unreadCount + this.demandesEnAttente;
  }

  formatDate(date: Date): string {
    const now = new Date();
    const notifDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays}j`;

    return notifDate.toLocaleDateString('fr-FR');
  }
}
