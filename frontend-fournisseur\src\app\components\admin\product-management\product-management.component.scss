// Variables de couleurs directement définies
$primary-color: #2563eb;
$secondary-color: #64748b;
$accent-color: #f59e0b;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-heavy: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

.product-management-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;

  .header {
    text-align: center;
    margin-bottom: 2rem;

    h1 {
      color: $primary-color;
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }

    .subtitle {
      color: $text-secondary;
      font-size: 1.1rem;
    }
  }

  // Filtres et recherche
  .filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: $shadow-light;
    margin-bottom: 2rem;

    .search-bar {
      margin-bottom: 1rem;

      .search-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid $border-color;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: $primary-color;
        }
      }
    }

    .filters-row {
      display: flex;
      gap: 1.5rem;
      flex-wrap: wrap;
      align-items: end;

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          font-weight: 600;
          color: $text-primary;
          font-size: 0.9rem;
        }

        .filter-select,
        .filter-input {
          padding: 0.5rem;
          border: 1px solid $border-color;
          border-radius: 6px;
          font-size: 0.9rem;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: $primary-color;
          }
        }

        .checkbox-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;
          font-weight: normal;

          .filter-checkbox {
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }

  // Statistiques rapides
  .stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .stat-card {
      background: white;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: $shadow-light;
      text-align: center;
      border-left: 4px solid $primary-color;

      .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: bold;
        color: $primary-color;
        margin-bottom: 0.5rem;
      }

      .stat-label {
        color: $text-secondary;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }

  // Messages
  .error-message {
    background: #fee;
    color: #c33;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid #c33;
  }

  .loading-spinner {
    text-align: center;
    padding: 3rem;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid $primary-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
  }

  // Tableau des produits
  .table-container {
    background: white;
    border-radius: 12px;
    box-shadow: $shadow-light;
    overflow: hidden;
    margin-bottom: 2rem;

    .products-table {
      width: 100%;
      border-collapse: collapse;

      thead {
        background: $primary-color;
        color: white;

        th {
          padding: 1rem 0.75rem;
          text-align: left;
          font-weight: 600;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      tbody {
        .product-row {
          border-bottom: 1px solid $border-color;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: #f8f9fa;
          }

          &.status-critique {
            background-color: #fff5f5;
            border-left: 4px solid #e53e3e;
          }

          &.status-attente {
            background-color: #fffbf0;
            border-left: 4px solid #f6ad55;
          }

          &.status-valide {
            background-color: #f0fff4;
            border-left: 4px solid #38a169;
          }

          td {
            padding: 1rem 0.75rem;
            vertical-align: top;
          }

          .image-cell {
            width: 80px;

            .product-image {
              width: 60px;
              height: 60px;
              object-fit: cover;
              border-radius: 8px;
              border: 1px solid $border-color;
            }
          }

          .product-info {
            min-width: 200px;

            .product-name {
              font-weight: 600;
              color: $text-primary;
              margin-bottom: 0.25rem;
              line-height: 1.3;
            }

            .product-meta {
              display: flex;
              gap: 0.5rem;
              align-items: center;
              flex-wrap: wrap;

              .date {
                font-size: 0.8rem;
                color: $text-secondary;
              }

              .featured-badge {
                background: $accent-color;
                color: white;
                padding: 0.2rem 0.5rem;
                border-radius: 12px;
                font-size: 0.7rem;
                font-weight: 600;
              }
            }
          }

          .reference-cell {
            .ref-original {
              font-weight: 600;
              color: $text-primary;
            }

            .ref-fournisseur {
              font-size: 0.8rem;
              color: $text-secondary;
              margin-top: 0.25rem;
            }
          }

          .price-cell {
            text-align: right;

            .price-container {
              display: flex;
              flex-direction: column;
              gap: 4px;
              position: relative;

              .price-initial {
                .price-label {
                  font-size: 0.75rem;
                  color: $text-secondary;
                  margin-right: 4px;
                  font-weight: 500;
                }

                .price-value.initial {
                  color: $text-primary;
                  font-size: 0.95rem;
                  font-weight: 500;

                  &.crossed {
                    text-decoration: line-through;
                    color: $text-secondary;
                  }
                }
              }

              .price-outlet {
                .price-label {
                  font-size: 0.8rem;
                  color: $accent-color;
                  margin-right: 4px;
                  font-weight: 500;
                }

                .price-value.outlet {
                  color: $accent-color;
                  font-weight: 600;
                  font-size: 1rem;
                }
              }

              .price-final {
                .price-label {
                  font-size: 0.8rem;
                  color: $primary-color;
                  margin-right: 4px;
                  font-weight: 500;
                }

                .price-value.final {
                  color: $primary-color;
                  font-weight: 700;
                  font-size: 1.1rem;
                }
              }

              .discount-badge {
                position: absolute;
                top: -8px;
                right: -8px;
                background: linear-gradient(135deg, $accent-color, #f7931e);
                color: white;
                font-size: 0.7rem;
                padding: 3px 8px;
                border-radius: 12px;
                font-weight: 700;
                box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
              }
            }

            // Fallback pour l'ancien style
            .price {
              font-weight: 600;
              color: $primary-color;
              font-size: 1.1rem;
            }
          }

          .stock-cell {
            .stock-container {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              gap: 0.25rem;

              .stock-value {
                font-weight: 600;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                background: #e6fffa;
                color: #234e52;
                font-size: 1.1rem;

                &.stock-critique {
                  background: #fed7d7;
                  color: #742a2a;
                  border: 2px solid #e53e3e;
                  animation: pulse-warning 2s infinite;
                }
              }

              .stock-alert {
                font-size: 0.75rem;
                color: #e53e3e;
                font-weight: 600;
                background: #fed7d7;
                padding: 2px 6px;
                border-radius: 10px;
                border: 1px solid #e53e3e;
              }

              .btn-stock {
                background: none;
                border: none;
                cursor: pointer;
                font-size: 1.2rem;
                padding: 0.25rem;
                border-radius: 4px;
                transition: background-color 0.2s ease;
                margin-top: 0.25rem;

                &:hover {
                  background: #f0f0f0;
                }
              }
            }
          }

          @keyframes pulse-warning {
            0%, 100% {
              opacity: 1;
            }
            50% {
              opacity: 0.7;
            }
          }

          .category-cell {
            .category {
              font-weight: 600;
              color: $text-primary;
            }

            .subcategory {
              font-size: 0.8rem;
              color: $text-secondary;
              margin-top: 0.25rem;
            }
          }

          .status-cell {
            .status-badge {
              padding: 0.25rem 0.75rem;
              border-radius: 12px;
              font-size: 0.8rem;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;

              &.status-valide {
                background: #c6f6d5;
                color: #22543d;
              }

              &.status-attente {
                background: #feebc8;
                color: #744210;
              }

              &.status-critique {
                background: #fed7d7;
                color: #742a2a;
              }
            }

            .stock-warning {
              display: block;
              font-size: 0.7rem;
              color: #e53e3e;
              margin-top: 0.25rem;
            }
          }

          .actions-cell {
            .action-buttons {
              display: flex;
              gap: 0.25rem;
              flex-wrap: wrap;

              .btn-action {
                background: none;
                border: 1px solid transparent;
                padding: 0.5rem;
                border-radius: 6px;
                cursor: pointer;
                font-size: 1rem;
                transition: all 0.2s ease;
                min-width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                  transform: translateY(-1px);
                }

                &.btn-validate {
                  background: #c6f6d5;
                  color: #22543d;

                  &:hover {
                    background: #9ae6b4;
                  }
                }

                &.btn-reject {
                  background: #fed7d7;
                  color: #742a2a;

                  &:hover {
                    background: #feb2b2;
                  }
                }

                &.btn-feature {
                  background: #feebc8;
                  color: #744210;

                  &:hover {
                    background: #fbd38d;
                  }
                }

                &.btn-unfeature {
                  background: $accent-color;
                  color: white;

                  &:hover {
                    background: darken($accent-color, 10%);
                  }
                }

                &.btn-moderate {
                  background: #e6fffa;
                  color: #234e52;

                  &:hover {
                    background: #b2f5ea;
                  }
                }

                &.btn-delete {
                  background: #fed7d7;
                  color: #742a2a;

                  &:hover {
                    background: #feb2b2;
                  }
                }
              }
            }
          }
        }
      }
    }

    .no-data {
      text-align: center;
      padding: 3rem;
      color: $text-secondary;
    }
  }

  // Pagination
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: $shadow-light;

    .pagination-info {
      color: $text-secondary;
      font-size: 0.9rem;
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .btn-page {
        padding: 0.5rem 1rem;
        border: 1px solid $border-color;
        background: white;
        color: $text-primary;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          background: $primary-color;
          color: white;
          border-color: $primary-color;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .page-numbers {
        display: flex;
        gap: 0.25rem;

        .btn-page-number {
          width: 36px;
          height: 36px;
          border: 1px solid $border-color;
          background: white;
          color: $text-primary;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: $primary-color;
            color: white;
            border-color: $primary-color;
          }

          &.active {
            background: $primary-color;
            color: white;
            border-color: $primary-color;
          }
        }
      }
    }
  }

  // Modals
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background: white;
      border-radius: 12px;
      box-shadow: $shadow-heavy;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid $border-color;

        h3 {
          margin: 0;
          color: $primary-color;
          font-size: 1.3rem;
        }

        .btn-close {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: $text-secondary;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: background-color 0.2s ease;

          &:hover {
            background: #f0f0f0;
          }
        }
      }

      .modal-body {
        padding: 1.5rem;

        .form-group {
          margin-bottom: 1.5rem;

          label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: $text-primary;
          }

          .form-input,
          .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid $border-color;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;

            &:focus {
              outline: none;
              border-color: $primary-color;
            }
          }

          .form-textarea {
            resize: vertical;
            min-height: 80px;
          }
        }

        .stock-info {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 6px;
          margin-top: 1rem;

          p {
            margin: 0.5rem 0;

            &.warning {
              color: #e53e3e;
              font-weight: 600;
            }
          }
        }
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        padding: 1.5rem;
        border-top: 1px solid $border-color;

        .btn-secondary,
        .btn-primary {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 600;
          transition: all 0.2s ease;
        }

        .btn-secondary {
          background: #f8f9fa;
          color: $text-primary;

          &:hover {
            background: #e9ecef;
          }
        }

        .btn-primary {
          background: $primary-color;
          color: white;

          &:hover {
            background: darken($primary-color, 10%);
          }
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive
@media (max-width: 768px) {
  .product-management-container {
    padding: 1rem;

    .filters-section .filters-row {
      flex-direction: column;
      align-items: stretch;

      .filter-group {
        width: 100%;

        .filter-select,
        .filter-input {
          min-width: auto;
          width: 100%;
        }
      }
    }

    .stats-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .table-container {
      overflow-x: auto;

      .products-table {
        min-width: 1000px;
      }
    }

    .pagination-container {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }
}
