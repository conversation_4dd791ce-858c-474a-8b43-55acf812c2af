import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, forkJoin, map, catchError, of } from 'rxjs';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';
import { ProduitService } from './produit.service';
import { Produit } from '../models/produit.model';

export interface DashboardStats {
  totalProducts: number;
  activeOrders: number;
  pendingDeliveries: number;
  monthlyRevenue: number;
}

export interface RecentOrder {
  id: number;
  reference: string;
  client: string;
  date: Date;
  amount: number;
  status: string;
}

export interface RecentActivity {
  icon: string;
  title: string;
  time: string;
}

export interface AdminStats {
  nombreUtilisateurs: number;
  nombreVentes: number;
  nombreProduits: number;
  nombreCommandesAnnulees: number;
  nombreCommandes: number;
  nombreFournisseurs: number;
  nombreClients: number;
}

export interface Fournisseur {
  id: number;
  email: string;
  nom: string;
  prenom: string;
  phoneNumber: string;
  role: string;
  dateNaissance: string;
  dateInscription: string;
  derniereConnexion: string | null;
  estActif: boolean;
  matriculeFiscale: string;
  raisonSociale: string;
  description: string;
  ribMasque: string;
  codeBanque: string;
  commission: number;
  delaiPreparationJours: number;
  fraisLivraisonBase: number;
  logoFile: string;
  adresses: any[];
}

// Interface Produit supprimée - utilise celle du modèle importé

export interface CommandeFournisseur {
  id: number;
  reference: string;
  fournisseurId: number;
  nomFournisseur: string;
  dateCommande: string;
  dateLivraison: string;
  statut: string;
  montantTotal: number;
  lignes: any[];
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private apiUrl = environment.apiUrl;

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private produitService: ProduitService
  ) {}

  /**
   * Récupérer l'ID du fournisseur connecté (utilise la même logique que la page produits)
   */
  private getFournisseurId(): number | null {
    // Utiliser directement le service d'authentification comme dans la page produits
    const currentUser = this.authService.getCurrentUser();
    return currentUser?.id || null;
  }

  /**
   * Headers avec token d'authentification
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  /**
   * Récupérer les statistiques admin (globales)
   */
  getAdminStats(): Observable<AdminStats> {
    return this.http.get<AdminStats>(`${this.apiUrl}/api/Admin/statistiques`, {
      headers: this.getAuthHeaders()
    });
  }

  /**
   * Récupérer les informations du fournisseur connecté (priorité localStorage)
   */
  getFournisseurInfo(): Observable<Fournisseur | null> {
    console.log('🔄 Dashboard: Récupération des infos fournisseur...');

    // Essayer d'abord localStorage
    const storedUser = localStorage.getItem('user');
    const currentUser = this.authService.getCurrentUser();

    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        console.log('📦 Dashboard: Données depuis localStorage:', userData);

        // Créer un objet Fournisseur depuis localStorage
        const fournisseur: Fournisseur = {
          id: userData.id || currentUser?.id || 0,
          email: userData.email || currentUser?.email || '',
          nom: userData.nom || currentUser?.nom || '',
          prenom: userData.prenom || currentUser?.prenom || '',
          phoneNumber: userData.phoneNumber || userData.telephone || '',
          role: userData.role || 'Fournisseur',
          dateNaissance: userData.dateNaissance || '',
          dateInscription: userData.dateInscription || new Date().toISOString(),
          derniereConnexion: userData.derniereConnexion || null,
          estActif: userData.estActif !== undefined ? userData.estActif : true,
          matriculeFiscale: userData.matriculeFiscale || '',
          raisonSociale: userData.raisonSociale || '',
          description: userData.description || '',
          ribMasque: userData.ribMasque || userData.rib || '',
          codeBanque: userData.codeBanque || '',
          commission: userData.commission || 0,
          delaiPreparationJours: userData.delaiPreparationJours || 3,
          fraisLivraisonBase: userData.fraisLivraisonBase || 5.00,
          logoFile: userData.logoFile || '',
          adresses: userData.adresses || []
        };

        console.log('✅ Dashboard: Fournisseur créé depuis localStorage:', fournisseur);
        return of(fournisseur);
      } catch (error) {
        console.warn('⚠️ Dashboard: Erreur parsing localStorage:', error);
      }
    }

    // Fallback: créer depuis currentUser si disponible
    if (currentUser) {
      const fournisseur: Fournisseur = {
        id: currentUser.id,
        email: currentUser.email,
        nom: currentUser.nom,
        prenom: currentUser.prenom,
        phoneNumber: (currentUser as any).phoneNumber || (currentUser as any).telephone || '',
        role: currentUser.role || 'Fournisseur',
        dateNaissance: '',
        dateInscription: new Date().toISOString(),
        derniereConnexion: null,
        estActif: true,
        matriculeFiscale: '',
        raisonSociale: '',
        description: '',
        ribMasque: '',
        codeBanque: '',
        commission: 0,
        delaiPreparationJours: 3,
        fraisLivraisonBase: 5.00,
        logoFile: '',
        adresses: []
      };

      console.log('✅ Dashboard: Fournisseur créé depuis currentUser:', fournisseur);
      return of(fournisseur);
    }

    console.warn('⚠️ Dashboard: Aucune donnée utilisateur disponible');
    return of(null);
  }

  /**
   * Récupérer les produits du fournisseur (utilise le même service que la page produits)
   */
  getFournisseurProducts(): Observable<Produit[]> {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser?.id) {
      console.warn('Aucun utilisateur connecté trouvé');
      return of([]);
    }

    console.log(`🔄 Dashboard: Récupération des produits pour le fournisseur ${currentUser.id}`);
    return this.produitService.getByFournisseur(currentUser.id).pipe(
      map(products => {
        console.log(`✅ Dashboard: ${products.length} produits récupérés:`, products);
        return products || [];
      }),
      catchError(error => {
        console.error('❌ Dashboard: Erreur lors de la récupération des produits:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupérer les commandes fournisseur
   */
  getFournisseurOrders(): Observable<CommandeFournisseur[]> {
    const fournisseurId = this.getFournisseurId();
    console.log('Récupération des commandes...');

    return this.http.get<CommandeFournisseur[]>(`${this.apiUrl}/api/CommandeFournisseur`, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(orders => {
        console.log(`${orders?.length || 0} commandes récupérées:`, orders);
        // Filtrer les commandes du fournisseur si nécessaire
        if (fournisseurId && orders) {
          return orders.filter(order => order.fournisseurId === fournisseurId);
        }
        return orders || [];
      }),
      catchError(error => {
        console.error('Erreur lors de la récupération des commandes:', error);
        console.log('Aucune commande disponible - retour d\'un tableau vide');
        // Retourner un tableau vide en cas d'erreur
        return of([]);
      })
    );
  }



  /**
   * Calculer les statistiques du dashboard à partir des données réelles
   */
  getDashboardStats(): Observable<DashboardStats> {
    const fournisseurId = this.getFournisseurId();
    if (!fournisseurId) {
      return of({
        totalProducts: 0,
        activeOrders: 0,
        pendingDeliveries: 0,
        monthlyRevenue: 0
      });
    }

    return forkJoin({
      products: this.getFournisseurProducts(),
      orders: this.getFournisseurOrders()
    }).pipe(
      map(({ products, orders }) => {
        // Calculer les statistiques
        const totalProducts = products.length;
        const activeOrders = orders.filter(order =>
          order.statut !== 'Livré' && order.statut !== 'Annulé'
        ).length;

        // Calculer le chiffre d'affaires du mois (approximation)
        const currentMonth = new Date().getMonth();
        const monthlyRevenue = orders
          .filter(order => {
            const orderDate = new Date(order.dateCommande);
            return orderDate.getMonth() === currentMonth;
          })
          .reduce((total, order) => total + order.montantTotal, 0);

        // Livraisons en cours (approximation basée sur les commandes)
        const pendingDeliveries = orders.filter(order =>
          order.statut === 'En préparation' || order.statut === 'Expédié'
        ).length;

        return {
          totalProducts,
          activeOrders,
          pendingDeliveries,
          monthlyRevenue
        };
      }),
      catchError(error => {
        console.error('Erreur lors du calcul des statistiques:', error);
        return of({
          totalProducts: 0,
          activeOrders: 0,
          pendingDeliveries: 0,
          monthlyRevenue: 0
        });
      })
    );
  }

  /**
   * Récupérer les commandes récentes
   */
  getRecentOrders(): Observable<RecentOrder[]> {
    return this.getFournisseurOrders().pipe(
      map(orders => {
        return orders
          .sort((a, b) => new Date(b.dateCommande).getTime() - new Date(a.dateCommande).getTime())
          .slice(0, 5) // Les 5 plus récentes
          .map(order => ({
            id: order.id,
            reference: order.reference,
            client: order.nomFournisseur, // Nom du client (à adapter selon votre modèle)
            date: new Date(order.dateCommande),
            amount: order.montantTotal,
            status: order.statut
          }));
      })
    );
  }

  /**
   * Récupérer l'activité récente
   */
  getRecentActivity(): Observable<RecentActivity[]> {
    return forkJoin({
      products: this.getFournisseurProducts(),
      orders: this.getFournisseurOrders()
    }).pipe(
      map(({ products, orders }) => {
        const activities: RecentActivity[] = [];

        // Derniers produits ajoutés
        const recentProducts = products
          .sort((a, b) => new Date(b.dateAjout).getTime() - new Date(a.dateAjout).getTime())
          .slice(0, 2);

        recentProducts.forEach(product => {
          activities.push({
            icon: '📦',
            title: `Produit "${product.nom}" ajouté`,
            time: this.getRelativeTime(new Date(product.dateAjout))
          });
        });

        // Dernières commandes
        const recentOrders = orders
          .sort((a, b) => new Date(b.dateCommande).getTime() - new Date(a.dateCommande).getTime())
          .slice(0, 2);

        recentOrders.forEach(order => {
          activities.push({
            icon: '📋',
            title: `Commande ${order.reference} reçue`,
            time: this.getRelativeTime(new Date(order.dateCommande))
          });
        });

        return activities.slice(0, 3); // Limiter à 3 activités
      })
    );
  }

  /**
   * Calculer le temps relatif (il y a X heures/jours)
   */
  private getRelativeTime(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return diffDays === 1 ? 'Hier' : `Il y a ${diffDays} jours`;
    } else if (diffHours > 0) {
      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else {
      return 'À l\'instant';
    }
  }

  /**
   * Formater le prix en dinars tunisiens
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(amount);
  }
}