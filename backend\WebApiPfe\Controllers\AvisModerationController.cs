using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AvisModerationController : ControllerBase
    {
        private readonly IAvisService _avisService;
        private readonly ILogger<AvisModerationController> _logger;

        public AvisModerationController(
            IAvisService avisService,
            ILogger<AvisModerationController> logger)
        {
            _avisService = avisService;
            _logger = logger;
        }

        /// <summary>
        /// Obtenir tous les avis pour modération (Admin uniquement)
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<AvisModerationDto>>> GetAvisForModeration([FromQuery] AvisFilterDto filter)
        {
            try
            {
                var avis = await _avisService.GetAvisForModerationAsync(filter);
                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des avis pour modération");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir un avis spécifique pour modération
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<AvisModerationDto>> GetAvisModeration(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                var avis = await _avisService.GetAvisModerationAsync(id);
                if (avis == null)
                    return NotFound(new { message = "Avis non trouvé" });

                // Vérifier les permissions pour les fournisseurs
                if (userRole == "Fournisseur" && avis.FournisseurId != userId)
                    return Forbid();

                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Modérer un avis (Admin uniquement)
        /// </summary>
        [HttpPut("{id}/moderer")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AvisModerationDto>> ModererAvis(int id, [FromBody] ModererAvisDto dto)
        {
            try
            {
                var moderateurId = GetCurrentUserId();
                var avis = await _avisService.ModererAvisAsync(id, dto, moderateurId);
                return Ok(avis);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la modération de l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les avis d'un fournisseur (Fournisseur uniquement)
        /// </summary>
        [HttpGet("fournisseur")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<List<AvisModerationDto>>> GetAvisFournisseur([FromQuery] AvisFilterDto filter)
        {
            try
            {
                var fournisseurId = GetCurrentUserId();
                var avis = await _avisService.GetAvisFournisseurAsync(fournisseurId, filter);
                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des avis du fournisseur");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Répondre à un avis (Fournisseur uniquement)
        /// </summary>
        [HttpPut("{id}/repondre")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<AvisModerationDto>> RepondreAvis(int id, [FromBody] string reponse)
        {
            try
            {
                var fournisseurId = GetCurrentUserId();
                var avis = await _avisService.RepondreAvisAsync(id, reponse, fournisseurId);
                return Ok(avis);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la réponse à l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les statistiques des avis (Admin)
        /// </summary>
        [HttpGet("statistiques")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AvisStatsDto>> GetAvisStats()
        {
            try
            {
                var stats = await _avisService.GetAvisStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des statistiques des avis");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les statistiques des avis pour un fournisseur
        /// </summary>
        [HttpGet("statistiques/fournisseur")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<AvisStatsDto>> GetAvisStatsFournisseur()
        {
            try
            {
                var fournisseurId = GetCurrentUserId();
                var stats = await _avisService.GetAvisStatsFournisseurAsync(fournisseurId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des statistiques des avis du fournisseur");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        // Méthodes d'aide privées
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 0;
        }

        private string GetCurrentUserRole()
        {
            return User.FindFirst(ClaimTypes.Role)?.Value ?? "";
        }
    }
}
