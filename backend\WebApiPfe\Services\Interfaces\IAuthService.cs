﻿using WebApiPfe.DTOs.AuthDTO;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;

namespace WebApiPfe.Services.Interfaces
{
    public interface IAuthService
    {
        Task<AuthResponseDto> AuthentifierAsync(LoginDto dto);
        Task<ClientDto> CreerClientAsync(ClientCreateDto dto);
        Task<FournisseurDto> CreerFournisseurAsync(FournisseurCreateDto dto);
        Task<UtilisateurReadDto> CreerAdminAsync(AdminCreateDto dto);
        Task<UtilisateurReadDto> InitialiserAdminParDefautAsync();
        Task<string> GenerateJwtTokenAsync(string email);
    }
}
