﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WebApiPfe.Migrations
{
    /// <inheritdoc />
    public partial class AjoutDemandesCategorieSousCategorie : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CommentaireModeration",
                table: "Avis",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateModeration",
                table: "Avis",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ModerePar",
                table: "Avis",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Statut",
                table: "Avis",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.CreateTable(
                name: "DemandesCategories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FournisseurId = table.Column<int>(type: "int", nullable: false),
                    Statut = table.Column<int>(type: "int", nullable: false),
                    DateDemande = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateTraitement = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AdminTraitantId = table.Column<int>(type: "int", nullable: true),
                    CommentaireAdmin = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CategorieCreeeId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DemandesCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DemandesCategories_AspNetUsers_AdminTraitantId",
                        column: x => x.AdminTraitantId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DemandesCategories_AspNetUsers_FournisseurId",
                        column: x => x.FournisseurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DemandesCategories_Categories_CategorieCreeeId",
                        column: x => x.CategorieCreeeId,
                        principalTable: "Categories",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DemandesSousCategories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CategorieId = table.Column<int>(type: "int", nullable: false),
                    FournisseurId = table.Column<int>(type: "int", nullable: false),
                    Statut = table.Column<int>(type: "int", nullable: false),
                    DateDemande = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateTraitement = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AdminTraitantId = table.Column<int>(type: "int", nullable: true),
                    CommentaireAdmin = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    SousCategorieCreeeId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DemandesSousCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DemandesSousCategories_AspNetUsers_AdminTraitantId",
                        column: x => x.AdminTraitantId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DemandesSousCategories_AspNetUsers_FournisseurId",
                        column: x => x.FournisseurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DemandesSousCategories_Categories_CategorieId",
                        column: x => x.CategorieId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DemandesSousCategories_SousCategories_SousCategorieCreeeId",
                        column: x => x.SousCategorieCreeeId,
                        principalTable: "SousCategories",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Avis_ModerePar",
                table: "Avis",
                column: "ModerePar");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesCategories_AdminTraitantId",
                table: "DemandesCategories",
                column: "AdminTraitantId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesCategories_CategorieCreeeId",
                table: "DemandesCategories",
                column: "CategorieCreeeId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesCategories_FournisseurId",
                table: "DemandesCategories",
                column: "FournisseurId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesSousCategories_AdminTraitantId",
                table: "DemandesSousCategories",
                column: "AdminTraitantId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesSousCategories_CategorieId",
                table: "DemandesSousCategories",
                column: "CategorieId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesSousCategories_FournisseurId",
                table: "DemandesSousCategories",
                column: "FournisseurId");

            migrationBuilder.CreateIndex(
                name: "IX_DemandesSousCategories_SousCategorieCreeeId",
                table: "DemandesSousCategories",
                column: "SousCategorieCreeeId");

            migrationBuilder.AddForeignKey(
                name: "FK_Avis_AspNetUsers_ModerePar",
                table: "Avis",
                column: "ModerePar",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Avis_AspNetUsers_ModerePar",
                table: "Avis");

            migrationBuilder.DropTable(
                name: "DemandesCategories");

            migrationBuilder.DropTable(
                name: "DemandesSousCategories");

            migrationBuilder.DropIndex(
                name: "IX_Avis_ModerePar",
                table: "Avis");

            migrationBuilder.DropColumn(
                name: "CommentaireModeration",
                table: "Avis");

            migrationBuilder.DropColumn(
                name: "DateModeration",
                table: "Avis");

            migrationBuilder.DropColumn(
                name: "ModerePar",
                table: "Avis");

            migrationBuilder.DropColumn(
                name: "Statut",
                table: "Avis");
        }
    }
}
