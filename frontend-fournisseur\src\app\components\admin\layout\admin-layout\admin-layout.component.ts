import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router } from '@angular/router';
import { AdminAuthService } from '../../../../services/admin-auth.service';
import { AdminUser } from '../../../../models/admin.model';
import { NotificationIconComponent } from '../../../notification-icon/notification-icon.component';

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, NotificationIconComponent],
  template: `
    <div class="admin-layout">
      <div class="admin-sidebar">
        <div class="sidebar-header">
          <h2>🔧 Admin Panel</h2>
          <p>{{ currentUser?.prenom }} {{ currentUser?.nom }}</p>
        </div>
        <nav class="sidebar-nav">
          <a routerLink="/admin/dashboard" class="nav-item">📊 Dashboard</a>
          <a routerLink="/admin/dashboard/users" class="nav-item">👥 Utilisateurs</a>
          <a routerLink="/admin/dashboard/products" class="nav-item">📦 Produits</a>
          <a routerLink="/admin/dashboard/demandes" class="nav-item">🔔 Demandes</a>
          <a routerLink="/admin/dashboard/avis-moderation" class="nav-item">⭐ Modération Avis</a>
          <a routerLink="/admin/dashboard/audit" class="nav-item">📋 Audit</a>
          <a routerLink="/admin/dashboard/alerts" class="nav-item">🚨 Alertes</a>
        </nav>
        <div class="sidebar-footer">
          <button (click)="logout()" class="logout-btn">🚪 Déconnexion</button>
        </div>
      </div>
      
      <div class="admin-main">
        <header class="admin-header">
          <h1>Administration - Optique Vision</h1>
          <div class="header-actions">
            <app-notification-icon></app-notification-icon>
            <span class="user-role">{{ currentUser?.role }}</span>
          </div>
        </header>
        
        <main class="admin-content">
          <router-outlet></router-outlet>
        </main>
      </div>
    </div>
  `,
  styles: [`
    .admin-layout {
      display: flex;
      min-height: 100vh;
      background: #f8fafc;
    }
    
    .admin-sidebar {
      width: 280px;
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      color: white;
      display: flex;
      flex-direction: column;
    }
    
    .sidebar-header {
      padding: 2rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .sidebar-header h2 {
      margin: 0 0 0.5rem 0;
      font-size: 1.5rem;
    }
    
    .sidebar-header p {
      margin: 0;
      opacity: 0.8;
      font-size: 0.875rem;
    }
    
    .sidebar-nav {
      flex: 1;
      padding: 1rem 0;
    }
    
    .nav-item {
      display: block;
      padding: 1rem 2rem;
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: all 0.3s ease;
      border-left: 3px solid transparent;
    }
    
    .nav-item:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border-left-color: #3b82f6;
    }
    
    .sidebar-footer {
      padding: 2rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .logout-btn {
      width: 100%;
      padding: 0.75rem;
      background: rgba(239, 68, 68, 0.2);
      color: #fca5a5;
      border: 1px solid rgba(239, 68, 68, 0.3);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .logout-btn:hover {
      background: rgba(239, 68, 68, 0.3);
      color: white;
    }
    
    .admin-main {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .admin-header {
      background: white;
      padding: 1rem 2rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .admin-header h1 {
      margin: 0;
      font-size: 1.5rem;
      color: #1e293b;
    }
    
    .user-role {
      padding: 0.5rem 1rem;
      background: #3b82f6;
      color: white;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
    }
    
    .admin-content {
      flex: 1;
      padding: 2rem;
      overflow-y: auto;
    }
    
    @media (max-width: 768px) {
      .admin-layout {
        flex-direction: column;
      }
      
      .admin-sidebar {
        width: 100%;
        height: auto;
      }
      
      .sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding: 0;
      }
      
      .nav-item {
        white-space: nowrap;
        border-left: none;
        border-bottom: 3px solid transparent;
      }
      
      .nav-item:hover {
        border-left: none;
        border-bottom-color: #3b82f6;
      }
    }
  `]
})
export class AdminLayoutComponent implements OnInit {
  currentUser: AdminUser | null = null;

  constructor(
    private adminAuthService: AdminAuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.adminAuthService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  logout(): void {
    this.adminAuthService.logout().subscribe(() => {
      this.router.navigate(['/admin/login']);
    });
  }
}
