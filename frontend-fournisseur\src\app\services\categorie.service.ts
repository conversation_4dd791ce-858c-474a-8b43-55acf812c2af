import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
  Categorie,
  CategorieCreate,
  CategorieUpdate,
  CategorieResponse,
  CategorieListResponse,
  CategorieDropdown,
  CategorieAdmin
} from '../models';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CategorieService {
  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/Categories`;

  constructor(private http: HttpClient) {}

  /**
   * GET /api/Categories - Obtenir toutes les catégories
   */
  getAll(): Observable<Categorie[]> {
    console.log('📦 Récupération des catégories');
    return this.http.get<Categorie[]>(`${this.API_URL}/enriched`)
      .pipe(
        tap(response => console.log('✅ Catégories récupérées:', response))
      );
  }

  /**
   * GET /api/Categories/admin - Obtenir toutes les catégories pour les admins (y compris non validées)
   */
  getAllForAdmin(): Observable<CategorieAdmin[]> {
    console.log('📦 Récupération des catégories pour admin');
    return this.http.get<CategorieAdmin[]>(`${this.API_URL}/admin`)
      .pipe(
        tap(response => console.log('✅ Catégories admin récupérées:', response))
      );
  }

  /**
   * GET /api/Categories/{id} - Obtenir une catégorie par ID
   */
  getById(id: number): Observable<Categorie> {
    console.log('🔍 Récupération de la catégorie ID:', id);
    return this.http.get<Categorie>(`${this.API_URL}/${id}`)
      .pipe(
        tap(response => console.log('✅ Catégorie récupérée:', response))
      );
  }

  /**
   * POST /api/Categories - Créer une nouvelle catégorie
   */
  create(categorie: CategorieCreate): Observable<Categorie> {
    console.log('➕ Création d\'une nouvelle catégorie:', categorie);
    return this.http.post<Categorie>(this.API_URL, categorie)
      .pipe(
        tap(response => console.log('✅ Catégorie créée:', response))
      );
  }

  /**
   * PUT /api/Categories/{id} - Mettre à jour une catégorie
   */
  update(id: number, categorie: CategorieUpdate): Observable<Categorie> {
    console.log('✏️ Mise à jour de la catégorie ID:', id, categorie);
    return this.http.put<Categorie>(`${this.API_URL}/${id}`, categorie)
      .pipe(
        tap(response => console.log('✅ Catégorie mise à jour:', response))
      );
  }

  /**
   * DELETE /api/Categories/{id} - Supprimer une catégorie
   */
  delete(id: number): Observable<void> {
    console.log('🗑️ Suppression de la catégorie ID:', id);
    return this.http.delete<void>(`${this.API_URL}/${id}`)
      .pipe(
        tap(() => console.log('✅ Catégorie supprimée:', id))
      );
  }

  /**
   * GET /api/Categories/{id}/sous-categories - Obtenir les sous-catégories d'une catégorie
   */
  getSousCategories(id: number): Observable<any[]> {
    console.log('📂 Récupération des sous-catégories pour la catégorie:', id);
    return this.http.get<any[]>(`${this.API_URL}/${id}/sous-categories`)
      .pipe(
        tap(response => console.log('✅ Sous-catégories récupérées:', response))
      );
  }

  /**
   * GET /api/Categories/{id}/produits-count - Obtenir le nombre de produits d'une catégorie
   */
  getProduitsCount(id: number): Observable<number> {
    console.log('🔢 Récupération du nombre de produits pour la catégorie:', id);
    return this.http.get<number>(`${this.API_URL}/${id}/produits-count`)
      .pipe(
        tap(response => console.log('✅ Nombre de produits récupéré:', response))
      );
  }

  /**
   * GET /api/Categories/dropdown - Obtenir les catégories pour dropdown
   */
  getDropdown(): Observable<CategorieDropdown[]> {
    console.log('📋 Récupération des catégories pour dropdown');
    return this.http.get<CategorieDropdown[]>(`${this.API_URL}/dropdown`)
      .pipe(
        tap(response => console.log('✅ Dropdown catégories récupéré:', response))
      );
  }

  /**
   * PATCH /api/Categories/{id}/toggle-visibility - Basculer la visibilité d'une catégorie
   */
  toggleVisibility(id: number): Observable<Categorie> {
    console.log('👁️ Basculer la visibilité de la catégorie:', id);
    return this.http.patch<Categorie>(`${this.API_URL}/${id}/toggle-visibility`, {})
      .pipe(
        tap(response => console.log('✅ Visibilité basculée:', response))
      );
  }
}
