import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface StatistiquesGeneralesDto {
  // Statistiques générales
  totalVentes: number;
  totalCommandes: number;
  totalClients: number;
  totalProduits: number;

  // Évolution
  evolutionVentes: number; // Pourcentage d'évolution
  evolutionCommandes: number;
  evolutionClients: number;

  // Moyennes
  panierMoyen: number;
  commandeMoyenne: number;

  // Top produits
  topProduits: TopProduitDto[];

  // Répartition par période
  ventesParMois: VentesParPeriodeDto[];
  commandesParJour: CommandesParJourDto[];
}

export interface StatistiquesFournisseurDto {
  // Statistiques du fournisseur
  totalVentes: number;
  totalCommandes: number;
  totalProduits: number;
  totalAvis: number;

  // Moyennes
  noteMoyenne: number;
  panierMoyen: number;

  // Évolution
  evolutionVentes: number;
  evolutionCommandes: number;

  // Top produits du fournisseur
  topProduits: TopProduitDto[];

  // Avis et promotions
  avisParNote: { [key: number]: number };
  promotionsActives: number;

  // Évolution temporelle
  ventesParMois: VentesParPeriodeDto[];
  commandesParStatut: CommandesParStatutDto[];
}

export interface TopProduitDto {
  id: number;
  nom: string;
  reference: string;
  quantiteVendue: number;
  chiffreAffaires: number;
  nombreCommandes: number;
  noteMoyenne: number;
  stock: number;
  fournisseurNom?: string;
}

export interface VentesParPeriodeDto {
  periode: string; // Format: "2024-01" ou "2024-01-15"
  montant: number;
  nombreCommandes: number;
  nombreClients: number;
}

export interface CommandesParJourDto {
  date: string; // Format: "2024-01-15"
  nombreCommandes: number;
  montantTotal: number;
}

export interface CommandesParStatutDto {
  statut: string;
  nombre: number;
  pourcentage: number;
}

export interface StatistiquesFilterDto {
  dateDebut?: Date;
  dateFin?: Date;
  fournisseurId?: number;
  categorieId?: number;
  periode?: 'jour' | 'semaine' | 'mois' | 'annee';
}

export interface StatistiquesComparaisonDto {
  periodeActuelle: StatistiquesGeneralesDto;
  periodePrecedente: StatistiquesGeneralesDto;
  evolution: {
    ventes: number;
    commandes: number;
    clients: number;
    panierMoyen: number;
  };
}

export interface StatistiquesExportDto {
  format: 'pdf' | 'excel' | 'csv';
  donnees: 'ventes' | 'commandes' | 'produits' | 'clients' | 'complet';
  periode: StatistiquesFilterDto;
}

// Interfaces de compatibilité avec l'ancien code
export interface StatistiquesDashboard {
  totalProduits: number;
  commandesActives: number;
  livraisonsEnCours: number;
  chiffreAffaireMensuel: number;
  evolutionVentes: {
    moisActuel: number;
    moisPrecedent: number;
    pourcentageEvolution: number;
  };
  topProduits: {
    id: number;
    nom: string;
    quantiteVendue: number;
    chiffreAffaire: number;
  }[];
  commandesRecentes: {
    id: string;
    client: string;
    amount: number;
    status: string;
    date: Date;
  }[];
}

export interface StatistiquesAdmin {
  totalUtilisateurs: number;
  totalFournisseurs: number;
  totalClients: number;
  totalCommandes: number;
  chiffreAffaireTotal: number;
  commandesParMois: { [mois: string]: number };
  topFournisseurs: { [fournisseur: string]: number };
}

@Injectable({
  providedIn: 'root'
})
export class StatistiquesService {
  private apiUrl = `${environment.apiUrl}/statistiques`;

  constructor(private http: HttpClient) { }

  // Statistiques générales (Admin)
  getStatistiquesGenerales(filter: StatistiquesFilterDto = {}): Observable<StatistiquesGeneralesDto> {
    let params = new HttpParams();

    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.fournisseurId) params = params.set('fournisseurId', filter.fournisseurId.toString());
    if (filter.categorieId) params = params.set('categorieId', filter.categorieId.toString());
    if (filter.periode) params = params.set('periode', filter.periode);

    return this.http.get<StatistiquesGeneralesDto>(`${this.apiUrl}/generales`, { params });
  }

  // Statistiques fournisseur
  getStatistiquesFournisseur(filter: StatistiquesFilterDto = {}): Observable<StatistiquesFournisseurDto> {
    let params = new HttpParams();

    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.categorieId) params = params.set('categorieId', filter.categorieId.toString());
    if (filter.periode) params = params.set('periode', filter.periode);

    return this.http.get<StatistiquesFournisseurDto>(`${this.apiUrl}/fournisseur`, { params });
  }

  // Comparaison de périodes
  getComparaisonPeriodes(
    periodeActuelle: StatistiquesFilterDto,
    periodePrecedente: StatistiquesFilterDto
  ): Observable<StatistiquesComparaisonDto> {
    const body = {
      periodeActuelle,
      periodePrecedente
    };

    return this.http.post<StatistiquesComparaisonDto>(`${this.apiUrl}/comparaison`, body);
  }

  // Top produits
  getTopProduits(limit: number = 10, filter: StatistiquesFilterDto = {}): Observable<TopProduitDto[]> {
    let params = new HttpParams();
    params = params.set('limit', limit.toString());

    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.fournisseurId) params = params.set('fournisseurId', filter.fournisseurId.toString());
    if (filter.categorieId) params = params.set('categorieId', filter.categorieId.toString());

    return this.http.get<TopProduitDto[]>(`${this.apiUrl}/top-produits`, { params });
  }

  // Évolution des ventes
  getEvolutionVentes(filter: StatistiquesFilterDto = {}): Observable<VentesParPeriodeDto[]> {
    let params = new HttpParams();

    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.fournisseurId) params = params.set('fournisseurId', filter.fournisseurId.toString());
    if (filter.periode) params = params.set('periode', filter.periode);

    return this.http.get<VentesParPeriodeDto[]>(`${this.apiUrl}/evolution-ventes`, { params });
  }

  // Évolution des commandes
  getEvolutionCommandes(filter: StatistiquesFilterDto = {}): Observable<CommandesParJourDto[]> {
    let params = new HttpParams();

    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.fournisseurId) params = params.set('fournisseurId', filter.fournisseurId.toString());

    return this.http.get<CommandesParJourDto[]>(`${this.apiUrl}/evolution-commandes`, { params });
  }

  // Export des statistiques
  exportStatistiques(exportDto: StatistiquesExportDto): Observable<Blob> {
    return this.http.post(`${this.apiUrl}/export`, exportDto, {
      responseType: 'blob'
    });
  }

  // Méthodes utilitaires
  formatEvolution(evolution: number): string {
    const signe = evolution >= 0 ? '+' : '';
    return `${signe}${evolution.toFixed(1)}%`;
  }

  getEvolutionColor(evolution: number): string {
    if (evolution > 0) return 'success';
    if (evolution < 0) return 'danger';
    return 'secondary';
  }

  getEvolutionIcon(evolution: number): string {
    if (evolution > 0) return 'trending-up';
    if (evolution < 0) return 'trending-down';
    return 'minus';
  }

  formatMontant(montant: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(montant);
  }

  formatNombre(nombre: number): string {
    return new Intl.NumberFormat('fr-FR').format(nombre);
  }

  // Calcul de périodes
  getPeriodePrecedente(dateDebut: Date, dateFin: Date): { dateDebut: Date, dateFin: Date } {
    const duree = dateFin.getTime() - dateDebut.getTime();
    const nouvelleDateFin = new Date(dateDebut.getTime() - 1);
    const nouvelleDateDebut = new Date(nouvelleDateFin.getTime() - duree);

    return {
      dateDebut: nouvelleDateDebut,
      dateFin: nouvelleDateFin
    };
  }

  // Périodes prédéfinies
  getPeriodesSemaine(): { dateDebut: Date, dateFin: Date } {
    const fin = new Date();
    const debut = new Date();
    debut.setDate(fin.getDate() - 7);

    return { dateDebut: debut, dateFin: fin };
  }

  getPeriodesMois(): { dateDebut: Date, dateFin: Date } {
    const fin = new Date();
    const debut = new Date();
    debut.setMonth(fin.getMonth() - 1);

    return { dateDebut: debut, dateFin: fin };
  }

  getPeriodesAnnee(): { dateDebut: Date, dateFin: Date } {
    const fin = new Date();
    const debut = new Date();
    debut.setFullYear(fin.getFullYear() - 1);

    return { dateDebut: debut, dateFin: fin };
  }

  // Génération de couleurs pour les graphiques
  generateColors(count: number): string[] {
    const baseColors = [
      '#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1',
      '#fd7e14', '#20c997', '#6c757d', '#e83e8c', '#17a2b8'
    ];

    const colors = [];
    for (let i = 0; i < count; i++) {
      colors.push(baseColors[i % baseColors.length]);
    }

    return colors;
  }

  // Méthodes de compatibilité avec l'ancien code
  getStatistiquesAdmin(): Observable<StatistiquesAdmin> {
    return this.getStatistiquesGenerales().pipe(
      map((stats: StatistiquesGeneralesDto) => ({
        totalUtilisateurs: stats.totalClients,
        totalFournisseurs: 0, // À implémenter
        totalClients: stats.totalClients,
        totalCommandes: stats.totalCommandes,
        chiffreAffaireTotal: stats.totalVentes,
        commandesParMois: {},
        topFournisseurs: {}
      } as StatistiquesAdmin))
    );
  }

  getStatistiquesDashboard(): Observable<StatistiquesDashboard> {
    return this.getStatistiquesFournisseur().pipe(
      map((stats: StatistiquesFournisseurDto) => ({
        totalProduits: stats.totalProduits,
        commandesActives: stats.totalCommandes,
        livraisonsEnCours: 0, // À implémenter
        chiffreAffaireMensuel: stats.totalVentes,
        evolutionVentes: {
          moisActuel: stats.totalVentes,
          moisPrecedent: 0, // À calculer
          pourcentageEvolution: stats.evolutionVentes
        },
        topProduits: stats.topProduits.map((p: TopProduitDto) => ({
          id: p.id,
          nom: p.nom,
          quantiteVendue: p.quantiteVendue,
          chiffreAffaire: p.chiffreAffaires
        })),
        commandesRecentes: [] // À implémenter
      } as StatistiquesDashboard))
    );
  }
}
