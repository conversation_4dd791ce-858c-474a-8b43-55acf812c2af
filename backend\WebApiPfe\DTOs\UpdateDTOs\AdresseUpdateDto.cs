﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.UpdateDTOs
{
    public class AdresseUpdateDto
    {
        public string? Rue { get; set; }
        public string? Ville { get; set; }

        [RegularExpression(@"^\d{4}$", ErrorMessage = "Le code postal doit contenir exactement 4 chiffres")]
        public string? CodePostal { get; set; }
        public string? Pays { get; set; }
        public bool? EstPrincipale { get; set; }
    }

}
