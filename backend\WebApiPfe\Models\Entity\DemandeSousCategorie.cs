using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.Models.Entity
{
    public class DemandeSousCategorie
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Nom { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        public int CategorieId { get; set; }

        [ForeignKey("CategorieId")]
        public virtual Categorie Categorie { get; set; } = null!;

        [Required]
        public int FournisseurId { get; set; }

        [ForeignKey("FournisseurId")]
        public virtual Fournisseur Fournisseur { get; set; } = null!;

        [Required]
        public StatutDemande Statut { get; set; } = StatutDemande.EnAttente;

        public DateTime DateDemande { get; set; } = DateTime.UtcNow;

        public DateTime? DateTraitement { get; set; }

        public int? AdminTraitantId { get; set; }

        [ForeignKey("AdminTraitantId")]
        public virtual Admin? AdminTraitant { get; set; }

        [StringLength(500)]
        public string? CommentaireAdmin { get; set; }

        // ID de la sous-catégorie créée si la demande est acceptée
        public int? SousCategorieCreeeId { get; set; }

        [ForeignKey("SousCategorieCreeeId")]
        public virtual SousCategorie? SousCategorieCreee { get; set; }
    }
}
