import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CategorieService } from '../../../../services/categorie.service';
import { CategorieAdmin } from '../../../../models/categorie.model';

@Component({
  selector: 'app-category-tree',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="category-management">
      <div class="page-header">
        <h1>🌳 Gestion des Catégories</h1>
        <p>Gérez les catégories et sous-catégories de la plateforme</p>
      </div>

      <div class="filters">
        <select [(ngModel)]="filtreStatut" (change)="applyFilter()">
          <option value="">Tous les statuts</option>
          <option value="validee">Validées</option>
          <option value="en-attente">En attente</option>
        </select>
        <div class="search-box">
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="applyFilter()"
            placeholder="Rechercher une catégorie...">
        </div>
      </div>

      <div *ngIf="isLoading" class="loading">
        <p>Chargement des catégories...</p>
      </div>

      <div *ngIf="!isLoading" class="categories-grid">
        <div *ngFor="let categorie of filteredCategories" class="category-card">
          <div class="category-header">
            <h3>{{ categorie.nom }}</h3>
            <div class="category-status">
              <span
                class="status-badge"
                [class.validated]="categorie.estValidee"
                [class.pending]="!categorie.estValidee">
                {{ categorie.estValidee ? '✅ Validée' : '⏳ En attente' }}
              </span>
            </div>
          </div>

          <div class="category-content">
            <p *ngIf="categorie.description" class="category-description">
              {{ categorie.description }}
            </p>

            <div class="subcategories-section" *ngIf="categorie.sousCategories && categorie.sousCategories.length > 0">
              <h4>Sous-catégories ({{ categorie.sousCategories.length }})</h4>
              <div class="subcategories-list">
                <div *ngFor="let sousCategorie of categorie.sousCategories" class="subcategory-item">
                  <span class="subcategory-name">{{ sousCategorie.nom }}</span>
                  <span
                    class="status-badge small"
                    [class.validated]="sousCategorie.estValidee"
                    [class.pending]="!sousCategorie.estValidee">
                    {{ sousCategorie.estValidee ? '✅' : '⏳' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="category-actions" *ngIf="!categorie.estValidee">
            <button class="btn btn-success" (click)="validateCategory(categorie.id)">
              ✅ Valider
            </button>
            <button class="btn btn-danger" (click)="deleteCategory(categorie.id)">
              🗑️ Supprimer
            </button>
          </div>
        </div>
      </div>

      <div *ngIf="!isLoading && filteredCategories.length === 0" class="empty-state">
        <p>Aucune catégorie trouvée</p>
      </div>
    </div>
  `,
  styles: [`
    .category-management {
      padding: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      margin-bottom: 2rem;
    }

    .page-header h1 {
      margin: 0 0 0.5rem 0;
      color: #1e293b;
    }

    .page-header p {
      margin: 0;
      color: #64748b;
    }

    .filters {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
      align-items: center;
    }

    .filters select {
      padding: 0.5rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      background: white;
    }

    .search-box {
      flex: 1;
      max-width: 300px;
    }

    .search-box input {
      width: 100%;
      padding: 0.5rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
    }

    .loading {
      text-align: center;
      padding: 3rem;
      color: #64748b;
    }

    .categories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 1.5rem;
    }

    .category-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
    }

    .category-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .category-header h3 {
      margin: 0;
      color: #1e293b;
    }

    .status-badge {
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .status-badge.validated {
      background: #dcfce7;
      color: #166534;
    }

    .status-badge.pending {
      background: #fef3c7;
      color: #92400e;
    }

    .status-badge.small {
      padding: 0.125rem 0.5rem;
      font-size: 0.75rem;
    }

    .category-description {
      color: #64748b;
      margin-bottom: 1rem;
    }

    .subcategories-section h4 {
      margin: 1rem 0 0.5rem 0;
      color: #374151;
      font-size: 1rem;
    }

    .subcategories-list {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .subcategory-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem;
      background: #f8fafc;
      border-radius: 6px;
    }

    .subcategory-name {
      color: #475569;
    }

    .category-actions {
      display: flex;
      gap: 0.5rem;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #e2e8f0;
    }

    .btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: all 0.3s ease;
    }

    .btn-success {
      background: #10b981;
      color: white;
    }

    .btn-success:hover {
      background: #059669;
    }

    .btn-danger {
      background: #ef4444;
      color: white;
    }

    .btn-danger:hover {
      background: #dc2626;
    }

    .empty-state {
      text-align: center;
      padding: 3rem;
      color: #64748b;
      background: #f8fafc;
      border-radius: 8px;
    }
  `]
})
export class CategoryTreeComponent implements OnInit {
  categories: CategorieAdmin[] = [];
  filteredCategories: CategorieAdmin[] = [];
  isLoading = false;
  filtreStatut = '';
  searchTerm = '';

  constructor(private categorieService: CategorieService) {}

  ngOnInit() {
    this.loadCategories();
  }

  loadCategories() {
    this.isLoading = true;
    this.categorieService.getAllForAdmin().subscribe({
      next: (categories) => {
        this.categories = categories;
        this.applyFilter();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des catégories:', error);
        this.isLoading = false;
      }
    });
  }

  applyFilter() {
    let filtered = [...this.categories];

    // Filtre par statut
    if (this.filtreStatut === 'validee') {
      filtered = filtered.filter(c => c.estValidee);
    } else if (this.filtreStatut === 'en-attente') {
      filtered = filtered.filter(c => !c.estValidee);
    }

    // Filtre par recherche
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(c =>
        c.nom.toLowerCase().includes(term) ||
        (c.description && c.description.toLowerCase().includes(term))
      );
    }

    this.filteredCategories = filtered;
  }

  validateCategory(id: number) {
    // TODO: Implémenter la validation de catégorie
    console.log('Valider catégorie:', id);
  }

  deleteCategory(id: number) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {
      // TODO: Implémenter la suppression de catégorie
      console.log('Supprimer catégorie:', id);
    }
  }
}
