﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FormesController : ControllerBase
    {
        private readonly IFormeService _formeService;

        public FormesController(IFormeService formeService)
        {
            _formeService = formeService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<FormeDto>>> GetAll()
        {
            var formes = await _formeService.GetAllAsync();
            var formesDto = formes.Select(f => new FormeDto
            {
                Id = f.Id,
                Nom = f.Nom,
                CategorieId = f.CategorieId,
                CategorieNom = f.Categorie?.Nom ?? "Non définie",
                ImageUrl = f.ImageUrl
            });
            return Ok(formesDto);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Forme>> GetById(int id)
        {
            var forme = await _formeService.GetByIdAsync(id);
            return forme != null ? Ok(forme) : NotFound();
        }

        [HttpGet("by-categorie/{categorieId}")]
        public async Task<ActionResult<IEnumerable<Forme>>> GetByCategorie(int categorieId)
        {
            return Ok(await _formeService.GetByCategorieAsync(categorieId));
        }

        [HttpGet("dropdown")]
        public async Task<ActionResult<Dictionary<int, string>>> GetForDropdown()
        {
            return Ok(await _formeService.GetFormesForDropdownAsync());
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateFormeDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var forme = new Forme
            {
                Nom = dto.Nom,
                CategorieId = dto.CategorieId,
                ImageUrl = dto.ImageUrl
            };

            var createdForme = await _formeService.CreateAsync(forme);

            return CreatedAtAction(nameof(GetById), new { id = createdForme.Id }, createdForme);
        }
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] Forme forme)
        {
            if (id != forme.Id) return BadRequest("ID mismatch");

            try
            {
                await _formeService.UpdateAsync(forme);
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _formeService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
