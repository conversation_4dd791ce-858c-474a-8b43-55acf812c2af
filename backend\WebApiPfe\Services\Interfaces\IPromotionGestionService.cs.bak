using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Services.Interfaces
{
    public interface IPromotionGestionService
    {
        // Gestion CRUD des promotions
        Task<List<PromotionGestionDto>> GetPromotionsAsync(PromotionFilterDto filter);
        Task<PromotionGestionDto?> GetPromotionAsync(int id);
        Task<PromotionGestionDto> CreatePromotionAsync(PromotionCreateDto dto, int? fournisseurId = null);
        Task<PromotionGestionDto> UpdatePromotionAsync(int id, PromotionUpdateDto dto);
        Task DeletePromotionAsync(int id);
        
        // Activation/Désactivation
        Task<PromotionGestionDto> TogglePromotionAsync(int id);
        Task<PromotionGestionDto> ActivatePromotionAsync(int id);
        Task<PromotionGestionDto> DeactivatePromotionAsync(int id);
        
        // Validation et application
        Task<PromotionValidationResultDto> ValidatePromotionAsync(ValidatePromotionDto dto);
        Task<List<PromotionGestionDto>> GetApplicablePromotionsAsync(int? produitId = null, decimal? montantCommande = null);
        
        // Statistiques
        Task<PromotionStatsDto> GetPromotionStatsAsync();
        Task<PromotionStatsDto> GetPromotionStatsFournisseurAsync(int fournisseurId);
        
        // Gestion par fournisseur
        Task<List<PromotionGestionDto>> GetPromotionsFournisseurAsync(int fournisseurId, PromotionFilterDto filter);
        
        // Gestion par admin
        Task<List<PromotionGestionDto>> GetPromotionsExpirantsAsync(int jours = 7);
        Task<List<PromotionGestionDto>> GetPromotionsPopulairesAsync(int limit = 10);
        
        // Utilitaires
        Task<bool> CodeExistsAsync(string code, int? excludeId = null);
        Task<decimal> CalculateDiscountAsync(int promotionId, decimal montantOriginal);
        Task IncrementUtilisationAsync(int promotionId);
        
        // Nettoyage automatique
        Task<int> CleanupExpiredPromotionsAsync();
        Task<List<PromotionGestionDto>> GetPromotionsToExpireAsync(int jours = 3);
    }
}
