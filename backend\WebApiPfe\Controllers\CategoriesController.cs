﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;
using AutoMapper;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CategoriesController : ControllerBase
    {
        private readonly ICategorieService _categorieService;
        private readonly IMapper _mapper;

        public CategoriesController(ICategorieService categorieService, IMapper mapper)
        {
            _categorieService = categorieService;
            _mapper = mapper;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<CategorieDto>>> GetAll()
        {
            var categories = await _categorieService.GetAllAsync();
            var categoriesDto = categories.Select(c => new CategorieDto
            {
                Id = c.Id,
                Nom = c.Nom,
                Description = c.Description,
                EstValidee = c.EstValidee,
                SousCategoriesCount = c.Sous<PERSON>ategories?.Count ?? 0
            });
            return Ok(categoriesDto);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<CategorieDto>> GetById(int id)
        {
            if (!await _categorieService.ExistsAsync(id))
                return NotFound("Catégorie n'existe pas.");
            try
            {
                var categorie = await _categorieService.GetByIdAsync(id);
                return Ok(categorie);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpGet("{id}/sous-categories")]
        public async Task<ActionResult<IEnumerable<SousCategorie>>> GetSousCategories(int id)
        {
            if (!await _categorieService.ExistsAsync(id))
                return NotFound("Catégorie n'existe pas.");
            var sousCategories = await _categorieService.GetSousCategoriesAsync(id);
            return Ok(sousCategories);
        }

        [HttpGet("{id}/produits-count")]
        public async Task<ActionResult<int>> GetProduitsCount(int id)
        {
            if (!await _categorieService.ExistsAsync(id))
                return NotFound("Catégorie n'existe pas.");
            var count = await _categorieService.GetProduitsCountAsync(id);
            return Ok(count);
        }

        [HttpGet("dropdown")]
        public async Task<ActionResult<Dictionary<int, string>>> GetForDropdown()
        {
            var categories = await _categorieService.GetCategoriesForDropdownAsync();
            return Ok(categories);
        }

        [HttpPost]
        public async Task<ActionResult<CategorieDto>> Create([FromBody] CreateCategorieDto dto)
        {
            try
            {
                var categorie = _mapper.Map<Categorie>(dto);
                var created = await _categorieService.CreateAsync(categorie);
                return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateCategorieDto dto)
        {
            if (id != dto.Id)
                return BadRequest("ID mismatch");

            if (!await _categorieService.ExistsAsync(id))
                return NotFound();

            try
            {
                var categorie = _mapper.Map<Categorie>(dto);
                await _categorieService.UpdateAsync(categorie);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _categorieService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPatch("{id}/toggle-visibility")]
        public async Task<IActionResult> ToggleVisibility(int id)
        {
            if (!await _categorieService.ExistsAsync(id))
                return NotFound("Catégorie n'existe pas.");
            try
            {
                await _categorieService.ToggleVisibilityAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }
    }
}
