@import '../../../../styles.scss';

.statistiques-admin-container {
  padding: 1.5rem;
  
  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
    
    h2 {
      color: $primary-color;
      font-weight: 600;
      margin-bottom: 0.5rem;
      
      i {
        margin-right: 0.5rem;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 1rem;
      align-items: center;
      
      .periode-selector {
        .form-select {
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          
          &:focus {
            border-color: $primary-color;
            box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
          }
        }
      }
      
      .export-buttons {
        .btn-group {
          .btn {
            border-radius: 6px;
            font-size: 0.875rem;
            
            &:first-child {
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }
            
            &:last-child {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
            }
            
            &:not(:first-child):not(:last-child) {
              border-radius: 0;
            }
          }
        }
      }
    }
  }
  
  .kpis-section {
    .kpi-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      border: 1px solid #e0e0e0;
      height: 100%;
      display: flex;
      align-items: center;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
      }
      
      .kpi-icon {
        font-size: 2.5rem;
        color: $primary-color;
        margin-right: 1.5rem;
        background: rgba($primary-color, 0.1);
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .kpi-content {
        flex: 1;
        
        h3 {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 0.25rem;
          color: $text-color;
        }
        
        p {
          margin: 0 0 0.5rem 0;
          color: #6c757d;
          font-size: 0.9rem;
          font-weight: 500;
        }
        
        .kpi-evolution {
          font-size: 0.875rem;
          font-weight: 600;
          
          i {
            margin-right: 0.25rem;
          }
        }
      }
    }
  }
  
  .secondary-metrics {
    .metric-card {
      background: white;
      border-radius: 8px;
      padding: 1.25rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border: 1px solid #e0e0e0;
      text-align: center;
      
      h6 {
        color: $text-color;
        font-weight: 600;
        margin-bottom: 0.75rem;
        
        i {
          margin-right: 0.5rem;
          color: $primary-color;
        }
      }
      
      .metric-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: $primary-color;
      }
    }
  }
  
  .tabs-section {
    margin-bottom: 1.5rem;
    
    .nav-tabs {
      border-bottom: 2px solid #e0e0e0;
      
      .nav-item {
        .nav-link {
          border: none;
          color: #6c757d;
          font-weight: 500;
          padding: 1rem 1.5rem;
          border-radius: 0;
          border-bottom: 3px solid transparent;
          
          &:hover {
            border-color: transparent;
            color: $primary-color;
          }
          
          &.active {
            color: $primary-color;
            background-color: transparent;
            border-bottom-color: $primary-color;
          }
          
          i {
            margin-right: 0.5rem;
          }
        }
      }
    }
  }
  
  .tab-content {
    .tab-pane {
      .card {
        border: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
        
        .card-header {
          background-color: #f8f9fa;
          border-bottom: 1px solid #e0e0e0;
          padding: 1rem 1.5rem;
          
          h5 {
            color: $text-color;
            font-weight: 600;
            margin: 0;
            
            i {
              margin-right: 0.5rem;
              color: $primary-color;
            }
          }
        }
        
        .card-body {
          padding: 1.5rem;
        }
      }
      
      .chart-container {
        display: flex;
        align-items: end;
        gap: 1rem;
        padding: 1rem 0;
        min-height: 200px;
        
        .chart-bar {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .bar-container {
            height: 150px;
            width: 100%;
            display: flex;
            align-items: end;
            justify-content: center;
            
            .bar {
              width: 80%;
              background: linear-gradient(to top, $primary-color, lighten($primary-color, 20%));
              border-radius: 4px 4px 0 0;
              min-height: 4px;
              transition: all 0.3s ease;
              
              &:hover {
                background: linear-gradient(to top, darken($primary-color, 10%), $primary-color);
              }
              
              &.bg-info {
                background: linear-gradient(to top, #17a2b8, lighten(#17a2b8, 20%));
                
                &:hover {
                  background: linear-gradient(to top, darken(#17a2b8, 10%), #17a2b8);
                }
              }
            }
          }
          
          .bar-label {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            color: #6c757d;
            text-align: center;
          }
          
          .bar-value {
            margin-top: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: $text-color;
            text-align: center;
          }
        }
      }
      
      .top-product-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .rank-badge {
          background-color: $primary-color;
          color: white;
          border-radius: 50%;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.8rem;
          font-weight: 600;
          margin-right: 1rem;
          
          &.rank-top {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
          }
        }
        
        .product-info {
          flex: 1;
          
          h6 {
            margin-bottom: 0.25rem;
            color: $text-color;
            font-size: 0.9rem;
          }
          
          small {
            color: #6c757d;
          }
        }
        
        .product-value {
          font-weight: 600;
          color: $primary-color;
          font-size: 0.9rem;
        }
      }
      
      .table {
        th {
          border-top: none;
          font-weight: 600;
          color: $text-color;
          background-color: #f8f9fa;
        }
        
        td {
          vertical-align: middle;
        }
        
        .rank-badge {
          background-color: $primary-color;
          color: white;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          font-size: 0.8rem;
          font-weight: 600;
          
          &.rank-top {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
          }
        }
        
        .badge {
          font-size: 0.75rem;
          
          &.bg-warning {
            color: #000;
          }
        }
      }
    }
  }
  
  .alert {
    border: none;
    border-radius: 6px;
    
    &.alert-danger {
      background-color: #f8d7da;
      color: #721c24;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

// Responsive
@media (max-width: 768px) {
  .statistiques-admin-container {
    padding: 1rem;
    
    .header-section {
      flex-direction: column;
      align-items: flex-start;
      
      .header-actions {
        width: 100%;
        justify-content: space-between;
        
        .export-buttons {
          .btn-group {
            .btn {
              font-size: 0.75rem;
              padding: 0.375rem 0.5rem;
            }
          }
        }
      }
    }
    
    .kpis-section {
      .kpi-card {
        margin-bottom: 1rem;
        
        .kpi-icon {
          font-size: 2rem;
          width: 50px;
          height: 50px;
          margin-right: 1rem;
        }
        
        .kpi-content {
          h3 {
            font-size: 1.5rem;
          }
        }
      }
    }
    
    .chart-container {
      .chart-bar {
        .bar-container {
          height: 100px;
        }
        
        .bar-label,
        .bar-value {
          font-size: 0.7rem;
        }
      }
    }
    
    .tabs-section {
      .nav-tabs {
        .nav-item {
          .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
          }
        }
      }
    }
    
    .table-responsive {
      font-size: 0.875rem;
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.kpi-card,
.metric-card,
.card {
  animation: fadeInUp 0.5s ease-out;
}

// Couleurs d'évolution
.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-info {
  color: #17a2b8 !important;
}
