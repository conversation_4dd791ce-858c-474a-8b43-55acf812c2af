using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class DemandeCategorieDto
    {
        public int Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int FournisseurId { get; set; }
        public string FournisseurNom { get; set; } = string.Empty;
        public string FournisseurEmail { get; set; } = string.Empty;
        public StatutDemande Statut { get; set; }
        public string StatutLibelle { get; set; } = string.Empty;
        public DateTime DateDemande { get; set; }
        public DateTime? DateTraitement { get; set; }
        public string? AdminTraitantNom { get; set; }
        public string? CommentaireAdmin { get; set; }
        public int? CategorieCreeeId { get; set; }
        public string? CategorieCreeeNom { get; set; }
    }
}
