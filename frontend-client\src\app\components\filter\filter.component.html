<div class="filter-container">
  <!-- Trier par -->
  <section>
    <h3>Trier par</h3>
    <div class="radio-group">
      <label *ngFor="let opt of sortOptions">
        <input 
          type="radio" 
          name="sort" 
          [value]="opt.value"
          [checked]="selectedSort === opt.value"
          (change)="onSortChange(opt.value)"
        >
        {{ opt.label }}
      </label>
    </div>
  </section>
  <!-- Sexe -->
  <section>
    <h3>Sexe</h3>
    <div class="checkbox-group">
      <label>
        <input 
          type="checkbox" 
          value="femme" 
          [checked]="selectedSexe.has('femme')"
          (change)="toggleSexe('femme')"
          >
        Femme
      </label>
      <label>
        <input 
          type="checkbox" 
          value="homme" 
          [checked]="selectedSexe.has('homme')"
          (change)="toggleSexe('homme')"
          >
        homme
      </label>
    <label>
      <input 
        type="checkbox" 
        value="enfant" 
        [checked]="selectedSexe.has('enfant')"
        (change)="toggleSexe('enfant')"
        >
        enfant
    </label>
    </div>
  </section>
  <!-- Choix populaires -->
  <section>
    <h3>Choix populaires</h3>
    <label>
      <input
        type="radio"
        name="populaire"
        value="nouveautes"
        [checked]="choixPopulaires === 'nouveautes'"
      (change)="onChoixPopulaireChange('nouveautes')"
      />
      Nouveautés
    </label>
    <label>
      <input
        type="radio"
        name="populaire"
        value="petitprix"
        [checked]="choixPopulaires === 'Lunettes à petit prix'"
      (change)="onChoixPopulaireChange('Lunettes à petit prix')"
      />
      Lunettes à petit prix
    </label>
  </section>
  <!-- Marques -->
  <section>
    <h3>Marques</h3>
    <input 
    type="text" 
    [(ngModel)]="filterTextMarque" 
    placeholder="Rechercher une marque..."
  >
    <div class="dropdown-list">
      <label *ngFor="let marque of marquesFiltrees">
        <input 
          type="checkbox" 
          [value]="marque.id"
          [checked]="selectedMarques.has(marque.id)"
          (change)="toggleMarque(marque.id)"
        >
        {{ marque.name }}
      </label>
    </div>
  </section>
  <!-- Formes -->
  <section>
    <h3>Formes</h3>
    <div class="dropdown-list">
      <label *ngFor="let forme of formes">
        <input 
          type="checkbox" 
          [value]="forme.id"
          [checked]="selectedFormes.has(forme.id)"
          (change)="toggleForme(forme.id)"
        >
        <img [src]="forme.imageUrl" [alt]="forme.nom" width="20">
        {{ forme.nom }}
      </label>
    </div>
  </section>
  <!-- Couleurs -->
  <section *ngIf="couleurs.length">
    <h3>Couleur de monture</h3>
    <div class="color-list">
      <span
        *ngFor="let couleur of couleurs"
        class="color-circle"
        [style.backgroundColor]="couleur"
        (click)="toggleCouleur(couleur)"
        [class.selected]="selectedCouleurs.has(couleur)"
        [title]="couleur"
      >
      </span>
    </div>
  </section>
  <!-- Prix -->
  <section>
    <h3>Prix</h3>
    <div class="prix-inputs">
      <input
        type="number"
        [(ngModel)]="prixMin"
        (ngModelChange)="onPrixMinChange(prixMin)"
        placeholder="Min DT"
        min="0"
      />
      <input
        type="number"
        [(ngModel)]="prixMax"
        (ngModelChange)="onPrixMaxChange(prixMax)"
        placeholder="Max DT"
        min="0"
      />
    </div>
    <input
      type="range"
      [min]="0"
      [max]="2000"
      [(ngModel)]="prixMax"
      (ngModelChange)="onPrixMaxChange(prixMax)"
    />
  </section>
  <!-- Boutons -->
  <section class="buttons">
    <button (click)="resetFilters()">Tout effacer</button>
    <button (click)="onAppliquerClicked()">Appliquer</button>
  </section>
</div>
