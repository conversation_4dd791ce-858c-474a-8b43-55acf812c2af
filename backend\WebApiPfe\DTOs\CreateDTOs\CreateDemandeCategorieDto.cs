using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class CreateDemandeCategorieDto
    {
        [Required(ErrorMessage = "Le nom de la catégorie est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom ne peut pas dépasser 100 caractères")]
        public string Nom { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "La description ne peut pas dépasser 500 caractères")]
        public string? Description { get; set; }
    }
}
