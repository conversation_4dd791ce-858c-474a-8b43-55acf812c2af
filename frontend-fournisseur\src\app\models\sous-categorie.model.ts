import { Categorie } from "./categorie.model";

// Modèle SousCategorie selon l'API backend
export interface SousCategorie {
  id: number;
  nom: string;
  description?: string;
  estValidee: boolean;
  categorieId: number;
  categorieNom?: string;
  produitsCount?: number;
}

// Interface pour créer une sous-catégorie
export interface SousCategorieCreate {
  nom: string;
  categorieId: number;
  description?: string;
  estValidee?: boolean;
}

// Interface pour mettre à jour une sous-catégorie
export interface SousCategorieUpdate {
  nom?: string;
  categorieId?: number;
  description?: string;
  estValidee?: boolean;
}

// Interface pour les réponses API
export interface SousCategorieResponse {
  success: boolean;
  data?: SousCategorie;
  message?: string;
}

export interface SousCategorieListResponse {
  success: boolean;
  data?: SousCategorie[];
  total?: number;
  message?: string;
}

// Interface pour dropdown
export interface SousCategorieDropdown {
  id: number;
  nom: string;
  categorieId: number;
}
