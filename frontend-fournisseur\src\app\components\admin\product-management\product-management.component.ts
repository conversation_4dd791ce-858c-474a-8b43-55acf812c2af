import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AdminService, ProduitAdmin, ProduitsPageResponse } from '../../../services/admin.service';
import { ImageUrlService } from '../../../services/image-url.service';

@Component({
  selector: 'app-product-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './product-management.component.html',
  styleUrls: ['./product-management.component.scss']
})
export class ProductManagementComponent implements OnInit {
  // Signals pour la gestion d'état
  produits = signal<ProduitAdmin[]>([]);
  isLoading = signal<boolean>(false);
  error = signal<string>('');
  
  // Pagination
  currentPage = signal<number>(1);
  pageSize = signal<number>(10);
  totalItems = signal<number>(0);
  totalPages = computed(() => Math.ceil(this.totalItems() / this.pageSize()));

  // Filtres
  searchTerm = signal<string>('');
  selectedStatut = signal<string>('');
  selectedFournisseur = signal<string>('');
  showStockCritique = signal<boolean>(false);

  // Options pour les filtres
  statutOptions = [
    { value: '', label: 'Tous les statuts' },
    { value: 'valide', label: 'Validés' },
    { value: 'attente', label: 'En attente' }
  ];

  // Modals et actions
  showModerationModal = signal<boolean>(false);
  showStockModal = signal<boolean>(false);
  selectedProduit = signal<ProduitAdmin | null>(null);
  moderationData = signal<{ nouveauNom: string; nouvelleDescription: string; raison: string }>({
    nouveauNom: '',
    nouvelleDescription: '',
    raison: ''
  });
  nouveauStock = signal<number>(0);

  constructor(
    private adminService: AdminService,
    public imageUrlService: ImageUrlService
  ) {}

  ngOnInit(): void {
    this.loadProduits();
  }

  loadProduits(): void {
    this.isLoading.set(true);
    this.error.set('');

    const params = {
      page: this.currentPage(),
      pageSize: this.pageSize(),
      search: this.searchTerm() || undefined,
      statut: this.selectedStatut() || undefined,
      fournisseur: this.selectedFournisseur() || undefined,
      stockCritique: this.showStockCritique() || undefined
    };

    this.adminService.getProduits(params).subscribe({
      next: (response: ProduitsPageResponse) => {
        console.log('✅ Produits reçus:', response);
        this.produits.set(response.produits || []);
        this.totalItems.set(response.totalCount || 0);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des produits:', error);
        this.error.set(`Erreur lors du chargement des produits: ${error.status || 'Erreur réseau'}`);
        this.isLoading.set(false);
      }
    });
  }

  // Méthodes de filtrage
  onSearchChange(): void {
    this.currentPage.set(1);
    this.loadProduits();
  }

  onStatutChange(): void {
    this.currentPage.set(1);
    this.loadProduits();
  }

  onFournisseurChange(): void {
    this.currentPage.set(1);
    this.loadProduits();
  }

  onStockCritiqueToggle(): void {
    this.currentPage.set(1);
    this.loadProduits();
  }

  // Méthodes de pagination
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.loadProduits();
    }
  }

  // Actions sur les produits
  validerProduit(produit: ProduitAdmin): void {
    if (confirm(`Êtes-vous sûr de vouloir valider le produit "${produit.nom}" ?`)) {
      this.adminService.validerProduit(produit.id).subscribe({
        next: (success) => {
          if (success) {
            console.log('✅ Produit validé avec succès');
            this.loadProduits(); // Recharger la liste
          }
        },
        error: (error) => {
          console.error('❌ Erreur validation produit:', error);
          this.error.set('Erreur lors de la validation du produit');
        }
      });
    }
  }

  refuserProduit(produit: ProduitAdmin): void {
    const raison = prompt(`Raison du refus pour "${produit.nom}" :`);
    if (raison !== null) {
      this.adminService.refuserProduit(produit.id, raison).subscribe({
        next: (success) => {
          if (success) {
            console.log('✅ Produit refusé avec succès');
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error('❌ Erreur refus produit:', error);
          this.error.set('Erreur lors du refus du produit');
        }
      });
    }
  }

  toggleMiseEnAvant(produit: ProduitAdmin): void {
    const action = produit.estEnAvant ? 'retirer la mise en avant' : 'mettre en avant';
    if (confirm(`Êtes-vous sûr de vouloir ${action} le produit "${produit.nom}" ?`)) {
      if (produit.estEnAvant) {
        // Retirer la mise en avant
        this.adminService.retirerMiseEnAvantProduit(produit.id).subscribe({
          next: (success: boolean) => {
            if (success) {
              console.log('✅ Mise en avant retirée avec succès');
              this.loadProduits();
            }
          },
          error: (error: any) => {
            console.error('❌ Erreur retrait mise en avant:', error);
            this.error.set('Erreur lors du retrait de la mise en avant');
          }
        });
      } else {
        // Mettre en avant
        this.adminService.mettreEnAvantProduit(produit.id).subscribe({
          next: (response: any) => {
            console.log('✅ Produit mis en avant avec succès');
            this.loadProduits();
          },
          error: (error: any) => {
            console.error('❌ Erreur mise en avant:', error);
            this.error.set('Erreur lors de la mise en avant');
          }
        });
      }
    }
  }

  supprimerProduit(produit: ProduitAdmin): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer définitivement le produit "${produit.nom}" ?`)) {
      this.adminService.supprimerProduit(produit.id).subscribe({
        next: (success) => {
          if (success) {
            console.log('✅ Produit supprimé avec succès');
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error('❌ Erreur suppression produit:', error);
          this.error.set('Erreur lors de la suppression du produit');
        }
      });
    }
  }

  // Modals
  openModerationModal(produit: ProduitAdmin): void {
    this.selectedProduit.set(produit);
    this.moderationData.set({
      nouveauNom: produit.nom,
      nouvelleDescription: '', // À récupérer si nécessaire
      raison: ''
    });
    this.showModerationModal.set(true);
  }

  closeModerationModal(): void {
    this.showModerationModal.set(false);
    this.selectedProduit.set(null);
  }

  submitModeration(): void {
    const produit = this.selectedProduit();
    const data = this.moderationData();
    
    if (produit) {
      this.adminService.modererContenuProduit(produit.id, {
        nouveauNom: data.nouveauNom !== produit.nom ? data.nouveauNom : undefined,
        nouvelleDescription: data.nouvelleDescription || undefined,
        raison: data.raison || undefined
      }).subscribe({
        next: (success) => {
          if (success) {
            console.log('✅ Contenu modéré avec succès');
            this.closeModerationModal();
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error('❌ Erreur modération:', error);
          this.error.set('Erreur lors de la modération du contenu');
        }
      });
    }
  }

  openStockModal(produit: ProduitAdmin): void {
    this.selectedProduit.set(produit);
    this.nouveauStock.set(produit.stock);
    this.showStockModal.set(true);
  }

  closeStockModal(): void {
    this.showStockModal.set(false);
    this.selectedProduit.set(null);
  }

  submitStockUpdate(): void {
    const produit = this.selectedProduit();
    
    if (produit) {
      this.adminService.updateStockProduit(produit.id, this.nouveauStock()).subscribe({
        next: (success) => {
          if (success) {
            console.log('✅ Stock mis à jour avec succès');
            this.closeStockModal();
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error('❌ Erreur mise à jour stock:', error);
          this.error.set('Erreur lors de la mise à jour du stock');
        }
      });
    }
  }

  // Utilitaires
  getStatusClass(produit: ProduitAdmin): string {
    if (produit.estStockCritique) return 'status-critique';
    if (produit.estValide) return 'status-valide';
    return 'status-attente';
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR');
  }

  onImageError(event: any): void {
    console.log('❌ Erreur de chargement d\'image:', event.target.src);
    event.target.src = this.imageUrlService.getPlaceholderUrl();
  }

  // Méthodes pour les statistiques
  getProduitsStockCritique(): number {
    return this.produits().filter(p => p.estStockCritique).length;
  }

  getProduitsEnAvant(): number {
    return this.produits().filter(p => p.estEnAvant).length;
  }

  getProduitsEnAttente(): number {
    return this.produits().filter(p => !p.estValide).length;
  }

  // Méthode pour la pagination
  getPageNumbers(): number[] {
    const maxPages = Math.min(5, this.totalPages());
    const pages: number[] = [];

    let startPage = Math.max(1, this.currentPage() - 2);
    let endPage = Math.min(this.totalPages(), startPage + maxPages - 1);

    // Ajuster si on est près de la fin
    if (endPage - startPage + 1 < maxPages) {
      startPage = Math.max(1, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }
}
