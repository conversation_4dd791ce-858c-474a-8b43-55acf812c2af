import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
  Forme,
  FormeCreate,
  FormeUpdate,
  FormeDropdown
} from '../models';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FormeService {
  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/Formes`;

  constructor(private http: HttpClient) {}

  /**
   * GET /api/Formes - Obtenir toutes les formes
   */
  getAll(): Observable<Forme[]> {
    console.log('📦 Récupération des formes');
    return this.http.get<Forme[]>(`${this.API_URL}/enriched`)
      .pipe(
        tap(response => console.log('✅ Formes récupérées:', response))
      );
  }

  /**
   * GET /api/Formes/{id} - Obtenir une forme par ID
   */
  getById(id: number): Observable<Forme> {
    console.log('🔍 Récupération de la forme ID:', id);
    return this.http.get<Forme>(`${this.API_URL}/${id}`)
      .pipe(
        tap(response => console.log('✅ Forme récupérée:', response))
      );
  }

  /**
   * POST /api/Formes - Créer une nouvelle forme
   */
  create(forme: FormeCreate): Observable<Forme> {
    console.log('➕ Création d\'une nouvelle forme:', forme);
    return this.http.post<Forme>(this.API_URL, forme)
      .pipe(
        tap(response => console.log('✅ Forme créée:', response))
      );
  }

  /**
   * PUT /api/Formes/{id} - Mettre à jour une forme
   */
  update(id: number, forme: FormeUpdate): Observable<Forme> {
    console.log('✏️ Mise à jour de la forme ID:', id, forme);
    return this.http.put<Forme>(`${this.API_URL}/${id}`, forme)
      .pipe(
        tap(response => console.log('✅ Forme mise à jour:', response))
      );
  }

  /**
   * DELETE /api/Formes/{id} - Supprimer une forme
   */
  delete(id: number): Observable<void> {
    console.log('🗑️ Suppression de la forme ID:', id);
    return this.http.delete<void>(`${this.API_URL}/${id}`)
      .pipe(
        tap(() => console.log('✅ Forme supprimée:', id))
      );
  }

  /**
   * GET /api/Formes/by-categorie/{categorieId} - Obtenir les formes d'une catégorie
   */
  getByCategorie(categorieId: number): Observable<Forme[]> {
    console.log('📂 Récupération des formes pour la catégorie:', categorieId);
    return this.http.get<Forme[]>(`${this.API_URL}/by-categorie/${categorieId}`)
      .pipe(
        tap(response => console.log('✅ Formes par catégorie récupérées:', response))
      );
  }

  /**
   * GET /api/Formes/dropdown - Obtenir les formes pour dropdown
   */
  getDropdown(): Observable<FormeDropdown[]> {
    console.log('📋 Récupération des formes pour dropdown');
    return this.http.get<FormeDropdown[]>(`${this.API_URL}/dropdown`)
      .pipe(
        tap(response => console.log('✅ Dropdown formes récupéré:', response))
      );
  }
}
