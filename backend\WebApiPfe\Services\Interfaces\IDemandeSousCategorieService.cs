using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.Services.Interfaces
{
    public interface IDemandeSousCategorieService
    {
        Task<IEnumerable<DemandeSousCategorieDto>> GetAllDemandesAsync();
        Task<IEnumerable<DemandeSousCategorieDto>> GetDemandesByFournisseurAsync(int fournisseurId);
        Task<IEnumerable<DemandeSousCategorieDto>> GetDemandesByStatutAsync(StatutDemande statut);
        Task<DemandeSousCategorieDto?> GetDemandeByIdAsync(int id);
        Task<DemandeSousCategorieDto> CreateDemandeAsync(CreateDemandeSousCategorieDto createDto, int fournisseurId);
        Task<DemandeSousCategorieDto> TraiterDemandeAsync(int id, TraiterDemandeSousCategorieDto traiterDto, int adminId);
        Task<bool> DeleteDemandeAsync(int id);
    }
}
