// Modèle Forme selon l'API backend
export interface Forme {
  id: number;
  nom: string;
  categorieId: number;
  categorieNom?: string;
  imageUrl: string;

  // Navigation properties (optionnelles)
  categorie?: any;
}

// Interface pour créer une forme
export interface FormeCreate {
  nom: string;
  categorieId: number;
  imageUrl: string;
}

// Interface pour mettre à jour une forme
export interface FormeUpdate {
  nom?: string;
  categorieId?: number;
  imageUrl?: string;
}

// Interface pour les réponses API
export interface FormeResponse {
  success: boolean;
  data?: Forme;
  message?: string;
}

export interface FormeListResponse {
  success: boolean;
  data?: Forme[];
  total?: number;
  message?: string;
}

// Interface pour dropdown (compatible avec le backend)
export interface FormeDropdown {
  id: number;
  nom: string;
  categorieId: number;
  imageUrl: string;
}
