import { Component, OnInit, signal, computed, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { User } from '../../models/user.model';

interface Activity {
  id: number;
  type: string;
  icon: string;
  title: string;
  description: string;
  time: string;
  meta?: string[];
}

@Component({
  selector: 'app-dashboard-ng19',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="dashboard-ng19">
      <!-- Header avec informations utilisateur -->
      <div class="dashboard-header">
        <div class="welcome-section">
          <div class="user-info">
            <div class="avatar">👤</div>
            <div class="user-details">
              <h1>Bienvenue, {{ supplierName() }}</h1>
              <p>Tableau de bord fournisseur - Angular 19</p>
            </div>
          </div>
          <div class="quick-stats">
            <div class="quick-stat">
              <span class="stat-number">{{ todayOrders() }}</span>
              <span class="stat-label">Commandes aujourd'hui</span>
            </div>
            <div class="quick-stat">
              <span class="stat-number">{{ todayRevenueFormatted() }}</span>
              <span class="stat-label">Revenus du jour</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Métriques principales -->
      <div class="metrics-grid">
        <div class="metric-card sales">
          <div class="metric-icon">💰</div>
          <div class="metric-content">
            <div class="metric-value">{{ totalRevenueFormatted() }}</div>
            <div class="metric-label">Ventes totales</div>
            <div class="metric-progress">
              <div class="progress-bar" [style.width.%]="salesProgress()"></div>
            </div>
            <div class="metric-detail">+{{ salesGrowth() }}% ce mois</div>
          </div>
        </div>

        <div class="metric-card orders">
          <div class="metric-icon">📦</div>
          <div class="metric-content">
            <div class="metric-value">{{ totalOrders() }}</div>
            <div class="metric-label">Commandes</div>
            <div class="metric-progress">
              <div class="progress-bar" [style.width.%]="ordersProgress()"></div>
            </div>
            <div class="metric-detail">{{ pendingOrders() }} en attente</div>
          </div>
        </div>

        <div class="metric-card products">
          <div class="metric-icon">👓</div>
          <div class="metric-content">
            <div class="metric-value">{{ totalProducts() }}</div>
            <div class="metric-label">Produits</div>
            <div class="metric-progress">
              <div class="progress-bar" [style.width.%]="stockHealthPercentage()"></div>
            </div>
            <div class="metric-detail">{{ lowStockProducts() }} en rupture</div>
          </div>
        </div>

        <div class="metric-card customers">
          <div class="metric-icon">👥</div>
          <div class="metric-content">
            <div class="metric-value">{{ totalCustomers() }}</div>
            <div class="metric-label">Clients</div>
            <div class="metric-progress">
              <div class="progress-bar" [style.width.%]="customersProgress()"></div>
            </div>
            <div class="metric-detail">{{ newCustomers() }} nouveaux ce mois</div>
          </div>
        </div>
      </div>

      <!-- Activités récentes avec nouveau control flow -->
      <div class="activities-section">
        <div class="section-header">
          <h2>📋 Activités récentes</h2>
          <button (click)="refreshActivities()" class="btn-refresh">🔄 Actualiser</button>
        </div>
        
        <div class="activities-list">
          @if (recentActivities().length > 0) {
            @for (activity of recentActivities(); track activity.id) {
              <div class="activity-item" [class]="getActivityTypeClass(activity.type)">
                <div class="activity-icon">{{ activity.icon }}</div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-description">{{ activity.description }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                  @if (activity.meta && activity.meta.length > 0) {
                    <div class="activity-meta">
                      @for (meta of activity.meta; track $index) {
                        <span class="meta-tag">{{ meta }}</span>
                      }
                    </div>
                  }
                </div>
              </div>
            } @empty {
              <div class="no-activities">
                <p>Aucune activité récente</p>
              </div>
            }
          } @else {
            <div class="loading-activities">
              <p>Chargement des activités...</p>
            </div>
          }
        </div>
      </div>

      <!-- Actions rapides -->
      <div class="quick-actions">
        <h2>⚡ Actions rapides</h2>
        <div class="actions-grid">
          <button (click)="navigateToProducts()" class="action-btn products-btn">
            <span class="action-icon">👓</span>
            <span class="action-label">Gérer Produits</span>
          </button>
          <button (click)="navigateToOrders()" class="action-btn orders-btn">
            <span class="action-icon">📋</span>
            <span class="action-label">Voir Commandes</span>
          </button>
          <button (click)="navigateToProfile()" class="action-btn profile-btn">
            <span class="action-icon">👤</span>
            <span class="action-label">Mon Profil</span>
          </button>
          <button (click)="addNewProduct()" class="action-btn add-btn">
            <span class="action-icon">➕</span>
            <span class="action-label">Nouveau Produit</span>
          </button>
        </div>
      </div>

      <!-- Statistiques en temps réel -->
      <div class="realtime-stats">
        <h2>📊 Statistiques temps réel</h2>
        <div class="stats-info">
          <p>Panier moyen: <strong>{{ averageOrderValue() }} DT</strong></p>
          <p>Taux de conversion: <strong>{{ conversionRate() }}%</strong></p>
          <p>Dernière mise à jour: <strong>{{ lastUpdate() }}</strong></p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-ng19 {
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
      background: #f8fafc;
      min-height: 100vh;
    }

    .dashboard-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      color: white;
    }

    .welcome-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .avatar {
      width: 60px;
      height: 60px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
    }

    .user-details h1 {
      margin: 0;
      font-size: 1.8rem;
    }

    .user-details p {
      margin: 0.5rem 0 0 0;
      opacity: 0.9;
    }

    .quick-stats {
      display: flex;
      gap: 2rem;
    }

    .quick-stat {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 1.5rem;
      font-weight: bold;
    }

    .stat-label {
      font-size: 0.875rem;
      opacity: 0.9;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .metric-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 1rem;
      transition: transform 0.3s ease;
    }

    .metric-card:hover {
      transform: translateY(-2px);
    }

    .metric-icon {
      font-size: 2rem;
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sales .metric-icon { background: #fef3c7; }
    .orders .metric-icon { background: #dbeafe; }
    .products .metric-icon { background: #d1fae5; }
    .customers .metric-icon { background: #fce7f3; }

    .metric-content {
      flex: 1;
    }

    .metric-value {
      font-size: 1.5rem;
      font-weight: bold;
      color: #1e293b;
    }

    .metric-label {
      color: #64748b;
      font-size: 0.875rem;
      margin: 0.25rem 0;
    }

    .metric-progress {
      width: 100%;
      height: 4px;
      background: #e2e8f0;
      border-radius: 2px;
      margin: 0.5rem 0;
    }

    .progress-bar {
      height: 100%;
      background: #3b82f6;
      border-radius: 2px;
      transition: width 0.3s ease;
    }

    .metric-detail {
      font-size: 0.75rem;
      color: #64748b;
    }

    .activities-section {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .section-header h2 {
      margin: 0;
      color: #1e293b;
    }

    .btn-refresh {
      background: #f1f5f9;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .btn-refresh:hover {
      background: #e2e8f0;
    }

    .activities-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .activity-item {
      display: flex;
      gap: 1rem;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 0.5rem;
      transition: background 0.3s ease;
    }

    .activity-item:hover {
      background: #f8fafc;
    }

    .activity-icon {
      font-size: 1.5rem;
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f1f5f9;
    }

    .activity-content {
      flex: 1;
    }

    .activity-title {
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.25rem;
    }

    .activity-description {
      color: #64748b;
      font-size: 0.875rem;
      margin-bottom: 0.25rem;
    }

    .activity-time {
      color: #94a3b8;
      font-size: 0.75rem;
    }

    .activity-meta {
      display: flex;
      gap: 0.5rem;
      margin-top: 0.5rem;
    }

    .meta-tag {
      background: #e2e8f0;
      color: #475569;
      padding: 0.125rem 0.5rem;
      border-radius: 4px;
      font-size: 0.75rem;
    }

    .quick-actions {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .quick-actions h2 {
      margin: 0 0 1rem 0;
      color: #1e293b;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      padding: 1.5rem;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #f8fafc;
    }

    .action-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .action-icon {
      font-size: 1.5rem;
    }

    .action-label {
      font-weight: 500;
      color: #1e293b;
    }

    .products-btn:hover { background: #dbeafe; }
    .orders-btn:hover { background: #fef3c7; }
    .profile-btn:hover { background: #d1fae5; }
    .add-btn:hover { background: #fce7f3; }

    .realtime-stats {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .realtime-stats h2 {
      margin: 0 0 1rem 0;
      color: #1e293b;
    }

    .stats-info {
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;
    }

    .stats-info p {
      margin: 0;
      color: #64748b;
    }

    @media (max-width: 768px) {
      .dashboard-ng19 {
        padding: 1rem;
      }
      
      .welcome-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
      }
      
      .quick-stats {
        justify-content: center;
      }
      
      .stats-info {
        flex-direction: column;
        gap: 0.5rem;
      }
    }
  `]
})
export class DashboardNg19Component implements OnInit {
  currentUser: User | null = null;

  // Angular 19: Signals pour l'état réactif
  supplierName = signal('Optique Vision Pro');
  todayOrders = signal(12);
  todayRevenue = signal(1850);
  
  // Métriques principales avec signals
  totalSales = signal(67890);
  totalOrders = signal(234);
  totalProducts = signal(156);
  totalCustomers = signal(389);
  pendingOrders = signal(18);
  lowStockProducts = signal(7);
  newCustomers = signal(24);
  
  // Pourcentages de progression
  salesProgress = signal(85);
  ordersProgress = signal(72);
  customersProgress = signal(91);
  
  // Angular 19: Computed signals pour des valeurs dérivées
  totalRevenueFormatted = computed(() =>
    new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(this.totalSales())
  );

  todayRevenueFormatted = computed(() =>
    new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(this.todayRevenue())
  );

  averageOrderValue = computed(() => {
    const orders = this.totalOrders();
    const sales = this.totalSales();
    return orders > 0 ? Math.round(sales / orders) : 0;
  });

  stockHealthPercentage = computed(() => {
    const total = this.totalProducts();
    const lowStock = this.lowStockProducts();
    return total > 0 ? Math.round(((total - lowStock) / total) * 100) : 100;
  });

  salesGrowth = computed(() => {
    // Simulation d'une croissance basée sur les ventes
    const sales = this.totalSales();
    return Math.round((sales / 50000) * 10);
  });

  conversionRate = computed(() => {
    const customers = this.totalCustomers();
    const orders = this.totalOrders();
    return customers > 0 ? Math.round((orders / customers) * 100) : 0;
  });

  lastUpdate = signal(new Date().toLocaleTimeString());

  // Activités récentes
  recentActivities = signal<Activity[]>([
    {
      id: 1,
      type: 'order',
      icon: '🛒',
      title: 'Nouvelle commande',
      description: 'Commande #CMD-2024-189 de Sophie Martin pour Ray-Ban Aviator',
      time: 'Il y a 3 minutes',
      meta: ['Ray-Ban Aviator', '159€']
    },
    {
      id: 2,
      type: 'delivery',
      icon: '🚚',
      title: 'Livraison confirmée',
      description: 'Commande #CMD-2024-187 livrée à Lyon',
      time: 'Il y a 15 minutes',
      meta: ['Oakley Holbrook', 'Livré']
    },
    {
      id: 3,
      type: 'stock',
      icon: '📦',
      title: 'Stock mis à jour',
      description: 'Réapprovisionnement Tom Ford FT5401 - 25 unités ajoutées',
      time: 'Il y a 1 heure',
      meta: ['Tom Ford', '+25 unités']
    },
    {
      id: 4,
      type: 'customer',
      icon: '👤',
      title: 'Nouveau client',
      description: 'Marc Dubois s\'est inscrit depuis Marseille',
      time: 'Il y a 2 heures',
      meta: ['Marseille', 'Vérifié']
    }
  ]);

  constructor(private authService: AuthService) {
    // Angular 19: Effects pour logger les changements
    effect(() => {
      console.log('📊 Dashboard NG19 - Ventes:', this.totalRevenueFormatted());
      console.log('📈 Dashboard NG19 - Commandes du jour:', this.todayOrders());
      console.log('💰 Dashboard NG19 - Panier moyen:', this.averageOrderValue(), 'DT');
      console.log('📦 Dashboard NG19 - Santé stock:', this.stockHealthPercentage(), '%');
    });

    // Effect pour mettre à jour l'heure
    setInterval(() => {
      this.lastUpdate.set(new Date().toLocaleTimeString());
    }, 30000); // Toutes les 30 secondes
  }

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      if (user) {
        this.supplierName.set(`${user.prenom} ${user.nom} - Optique`);
      }
    });
    
    // Simulation de données en temps réel
    this.simulateRealTimeData();
  }

  /**
   * Simulation de données en temps réel avec Angular 19 signals
   */
  private simulateRealTimeData(): void {
    setInterval(() => {
      if (Math.random() > 0.8) {
        // Nouvelle commande
        this.todayOrders.update(value => value + 1);
        this.todayRevenue.update(value => value + Math.floor(Math.random() * 300) + 100);
        this.totalOrders.update(value => value + 1);
        this.totalSales.update(value => value + Math.floor(Math.random() * 300) + 100);
        
        // Mise à jour des activités
        const newActivity: Activity = {
          id: Date.now(),
          type: 'order',
          icon: '🛒',
          title: 'Nouvelle commande',
          description: `Commande #CMD-2024-${190 + this.todayOrders()} reçue`,
          time: 'À l\'instant',
          meta: ['Nouvelle', `${Math.floor(Math.random() * 200) + 100}€`]
        };
        
        this.recentActivities.update(activities => [newActivity, ...activities.slice(0, 4)]);
      }
    }, 15000); // Toutes les 15 secondes
  }

  // Méthodes pour les actions
  refreshActivities(): void {
    console.log('🔄 Actualisation des activités');
    // Simulation d'un refresh
    this.lastUpdate.set(new Date().toLocaleTimeString());
  }

  navigateToProducts(): void {
    console.log('👓 Navigation vers les produits');
  }

  navigateToOrders(): void {
    console.log('📋 Navigation vers les commandes');
  }

  navigateToProfile(): void {
    console.log('👤 Navigation vers le profil');
  }

  addNewProduct(): void {
    console.log('➕ Ajout d\'un nouveau produit');
  }

  getActivityTypeClass(type: string): string {
    return `activity-${type}`;
  }
}
