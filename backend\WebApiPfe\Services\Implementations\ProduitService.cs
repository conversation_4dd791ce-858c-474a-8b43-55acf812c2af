﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class ProduitService : IProduitService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly ISousCategorieService _sousCategorieService;
        private readonly IFournisseurService _fournisseurService;
        private readonly IMarqueService _marqueService;
        private readonly ITauxTVAService _tauxTVAService;
        private readonly IFormeService _formeService;
        private readonly ILogger<ProduitService> _logger;
        private readonly IPromotionService _promotionService;

        private const decimal REMISE_OUTLET_PAR_DEFAUT = 30m;
        public ProduitService(
            AppDbContext context,
            IMapper mapper,
            ISousCategorieService sousCategorieService,
            IFournisseurService fournisseurService,
            IMarqueService marqueService,
            ITauxTVAService tauxTVAService,
            IFormeService formeService,
            ILogger<ProduitService> logger,
            IPromotionService promotionService)
        {
            _context = context;
            _mapper = mapper;
            _sousCategorieService = sousCategorieService;
            _fournisseurService = fournisseurService;
            _marqueService = marqueService;
            _tauxTVAService = tauxTVAService;
            _formeService = formeService;
            _logger = logger;
            _promotionService = promotionService;
        }
        private async Task AppliquerRemisesAsync(Produit produit, ProduitDto dto)
        {
            if (produit == null) return;

            await _context.Entry(produit)
                .Collection(p => p.PromotionsApplicables)
                .Query()
                .Where(
                    promo => 
                    promo.DateDebut <= DateTime.Now && 
                    (promo.DateFin == null || promo.DateFin >= DateTime.Now)
                )
                .Select(p => new
                {
                    p.Id,
                    p.Type,
                    p.PourcentageRemise,
                    p.DateDebut,
                    p.DateFin,
                    p.AppliquerSurHT
                })
                .LoadAsync();

            var prixBaseHT = produit.PrixVenteHT;
            var tauxTVA = produit.TauxTVA?.Taux ?? 0;

            var remiseOutlet = produit.PromotionsApplicables.FirstOrDefault(p => p.Type == TypePromotion.Outlet);

            decimal prixApresOutletHT = remiseOutlet != null
                ? remiseOutlet.CalculerPrixRemise(prixBaseHT, produit.TauxTVA)
                : prixBaseHT;

            dto.PrixApresRemisesOutlet = Math.Round(prixApresOutletHT * (1 + tauxTVA / 100m), 2);

             var autresPromos = produit.PromotionsApplicables
                .Where(p => p.Type != TypePromotion.Outlet && p.EstValide())
                .OrderByDescending(p => p.PourcentageRemise)
                .ToList();

            decimal prixFinalHT = prixApresOutletHT;

            foreach (var promo in autresPromos)
            {
                prixFinalHT = promo.CalculerPrixRemise(prixFinalHT, produit.TauxTVA);
                
                if (prixFinalHT < 0) prixFinalHT = 0; 
            }

            dto.PrixApresAutresPromotions = Math.Round(prixFinalHT * (1 + tauxTVA / 100m), 2);
            dto.PrixApresRemises = dto.PrixApresAutresPromotions;

            if (prixBaseHT > 0)
            {
                decimal remiseTotale = 100 * (1 - prixFinalHT / prixBaseHT);
                dto.PourcentageRemiseTotale = Math.Round(remiseTotale, 2);
            }
            else
            {
                dto.PourcentageRemiseTotale = 0;
            }

             dto.Promotions = _mapper.Map<List<PromotionDto>>(produit.PromotionsApplicables);
        }
        //CRUD
        public async Task<ProduitDto> CreateAsync(ProduitCreateDto produitDto)
        {
            if (!await IsReferenceUniqueAsync(produitDto.ReferenceOriginal))
                throw new ArgumentException("La référence originale existe déjà");

            if (!string.IsNullOrEmpty(produitDto.ReferenceFournisseur) &&
                !await IsReferenceUniqueAsync(produitDto.ReferenceFournisseur))
                throw new ArgumentException("La référence fournisseur existe déjà");

            if (!await IsCodeABarreUniqueAsync(produitDto.CodeABarre))
                throw new ArgumentException("Le code à barre existe déjà");

            if (!await _sousCategorieService.ExistsAsync(produitDto.SousCategorieId))
                throw new ArgumentException("La sous-catégorie spécifiée n'existe pas");

            if (!await _fournisseurService.ExistsAsync(produitDto.FournisseurId))
                throw new ArgumentException("Le fournisseur spécifié n'existe pas");

            if (!await _marqueService.ExistsAsync(produitDto.MarqueId))
                throw new ArgumentException("La marque spécifiée n'existe pas");

            if (!await _tauxTVAService.ExistsAsync(produitDto.TauxTVAId))
                throw new ArgumentException("Le taux de TVA spécifié n'existe pas");

            if (!await _formeService.ExistsAsync(produitDto.FormeId))
                throw new ArgumentException("La forme spécifiée n'existe pas");

            var produit = _mapper.Map<Produit>(produitDto);
            produit.DateAjout = DateTime.UtcNow;

            if (produitDto.ImageFiles != null && produitDto.ImageFiles.Any())
            {
                int ordre = 0;
                foreach (var file in produitDto.ImageFiles)
                {
                    if (file.Length > 0)
                    {
                        var imgPath = await ImageHandler.SaveImageAsync(file);
                        produit.Images.Add(new ImagesProduit
                        {
                            ImageUrl = imgPath,
                            Ordre = ordre,
                            IsMain = ordre == 0
                        });
                        ordre++;
                    }
                }
            }

            await _context.Produits.AddAsync(produit);
            await _context.SaveChangesAsync();

            var pourcentageOutlet = produitDto.PourcentageRemise ?? REMISE_OUTLET_PAR_DEFAUT;
            var now = DateTime.UtcNow;

            _logger.LogInformation($"🎯 Création promotion outlet - Pourcentage: {pourcentageOutlet}% pour produit ID: {produit.Id}");

            var promoOutletExistante = await _context.Promotions
                .Include(p => p.ProduitsApplicables)
                .Where(p =>
                    p.Type == TypePromotion.Outlet &&
                    p.PourcentageRemise == pourcentageOutlet &&
                    p.DateDebut <= now &&
                    (p.DateFin == null || p.DateFin >= now))
                .FirstOrDefaultAsync();
            if (promoOutletExistante != null)
            {
                if (!promoOutletExistante.ProduitsApplicables.Any(p => p.Id == produit.Id))
                {
                    promoOutletExistante.ProduitsApplicables.Add(produit);
                    await _context.SaveChangesAsync();
                    _logger.LogInformation($"✅ Produit ajouté à promotion outlet existante ID: {promoOutletExistante.Id}");
                }
                else
                {
                    _logger.LogInformation($"ℹ️ Produit déjà associé à la promotion outlet ID: {promoOutletExistante.Id}");
                }
            }
            else
            {
                var nouvellePromo = new Promotion
                {
                    Type = TypePromotion.Outlet,
                    PourcentageRemise = pourcentageOutlet,
                    DateDebut = now,
                    DateFin = null,
                    AppliquerSurHT = true,
                    ProduitsApplicables = new List<Produit> { produit }
                };

                await _context.Promotions.AddAsync(nouvellePromo);
                await _context.SaveChangesAsync();
                _logger.LogInformation($"✅ Nouvelle promotion outlet créée ID: {nouvellePromo.Id} avec {pourcentageOutlet}% de remise");
            }

            var produitComplet = await _context.Produits
                .Include(p => p.Images)
                .Include(p => p.PromotionsApplicables)
                .Include(p => p.TauxTVA)
                .FirstOrDefaultAsync(p => p.Id == produit.Id);

            var result = _mapper.Map<ProduitDto>(produitComplet);

            await AppliquerRemisesAsync(produitComplet, result);
            return result;
        }
        public async Task UpdateAsync(ProduitUpdateDto produitDto)
        {
            var produit = await _context.Produits
                .Include(p => p.Images)
                .Include(p => p.PromotionsApplicables)
                .Include(p => p.TauxTVA)
                .FirstOrDefaultAsync(p => p.Id == produitDto.Id);

            if (produit == null)
                throw new KeyNotFoundException("Produit non trouvé");

            produit.Nom = produitDto.Nom ?? produit.Nom;
            produit.Description = produitDto.Description ?? produit.Description;

            if (produitDto.PrixVenteHT.HasValue)
                produit.PrixVenteHT = produitDto.PrixVenteHT.Value;

            if (produitDto.PourcentageRemise.HasValue)
            {
                var remise = produitDto.PourcentageRemise.Value;

                var promoOutlet = produit.PromotionsApplicables
                    .FirstOrDefault(p => p.Type == TypePromotion.Outlet);

                if (promoOutlet != null)
                {
                    promoOutlet.PourcentageRemise = remise;
                    promoOutlet.AppliquerSurHT = true;
                }
                else
                {
                    var nouvellePromo = new Promotion
                    {
                        Type = TypePromotion.Outlet,
                        PourcentageRemise = remise,
                        DateDebut = DateTime.UtcNow,
                        DateFin = null, 
                        AppliquerSurHT = true,
                        ProduitsApplicables = new List<Produit> { produit }
                    };

                    await _context.Promotions.AddAsync(nouvellePromo);
                    produit.PromotionsApplicables.Add(nouvellePromo);
                }
            }

            if (produitDto.ImageFiles != null && produitDto.ImageFiles.Any())
            {
                int maxOrdre = produit.Images.Any() ? produit.Images.Max(i => i.Ordre) + 1 : 0;

                foreach (var imageFile in produitDto.ImageFiles)
                {
                    if (imageFile.Length > 0)
                    {
                        var imagePath = await ImageHandler.SaveImageAsync(imageFile);
                        produit.Images.Add(new ImagesProduit
                        {
                            ImageUrl = imagePath,
                            Ordre = maxOrdre++,
                            IsMain = produit.Images.Count == 0
                        });
                    }
                }
            }

            _context.Produits.Update(produit);
            await _context.SaveChangesAsync();
        }
        public async Task DeleteAsync(int id)
        {
            var produit = await _context.Produits
                .Include(p => p.DetailsCommandes)
                .Include(p => p.LignesCommandeFournisseur)
                .Include(p => p.Images)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (produit == null)
                throw new KeyNotFoundException();

            foreach (var img in produit.Images)
            {
                ImageHandler.DeleteImage(img.ImageUrl);
            }
            _context.Produits.Remove(produit);
            await _context.SaveChangesAsync();
        }
        public async Task<bool> ExistsAsync(int id) =>
            await _context.Produits.AnyAsync(p => p.Id == id);
        //Recuperation
        public async Task<IEnumerable<ProduitDto>> GetAllAsync()
        {
            var produits = await _context.Produits
                .Include(p => p.Images)
                .Include(p => p.PromotionsApplicables)
                .Include(p => p.SousCategorie)
                .Include(p => p.Fournisseur)
                .Include(p => p.Marque)
                .Include(p => p.Forme)
                .Include(p => p.TauxTVA)
                .AsNoTracking()
                .OrderBy(p => p.Nom)
                .ToListAsync();

            var result = new List<ProduitDto>();

            foreach (var produit in produits)
            {
                var dto = _mapper.Map<ProduitDto>(produit);
                await AppliquerRemisesAsync(produit, dto);
                result.Add(dto);
            }

            return result;
        }
        public async Task<ProduitDto> GetByIdAsync(int id)
        {
            var produit = await _context.Produits
                .Include(p => p.SousCategorie)
                .Include(p => p.Fournisseur)
                .Include(p => p.Marque)
                .Include(p => p.TauxTVA)
                .Include(p => p.Forme)
                .Include(p => p.Images)
                .Include(p => p.PromotionsApplicables)
                .FirstOrDefaultAsync(p => p.Id == id);
            if (produit == null)
                throw new KeyNotFoundException("Produit non trouvé");
            var dto = _mapper.Map<ProduitDto>(produit);
            await AppliquerRemisesAsync(produit, dto);
            return dto;
        }
        public async Task<ProduitDto> GetByReferenceOriginalAsync(string reference)
        {
            var produit = await _context.Produits
               .Include(p => p.SousCategorie)
                .Include(p => p.Fournisseur)
                .Include(p => p.Marque)
                .Include(p => p.TauxTVA)
                .Include(p => p.Forme)
                .Include(p => p.Images)
                .Include(p => p.PromotionsApplicables)
                .FirstOrDefaultAsync(p => p.ReferenceOriginal == reference);

            if (produit == null)
                return null;

            var dto = _mapper.Map<ProduitDto>(produit);
            await AppliquerRemisesAsync(produit, dto);
            return dto;
        }
        public async Task<ProduitDto> GetByReferenceFournisseurAsync(string reference)
        {
            var produit = await _context.Produits
               .Include(p => p.SousCategorie)
                .Include(p => p.Fournisseur)
                .Include(p => p.Marque)
                .Include(p => p.TauxTVA)
                .Include(p => p.Forme)
                .Include(p => p.Images)
                .Include(p => p.PromotionsApplicables)
                .FirstOrDefaultAsync(p => p.ReferenceFournisseur == reference);

            if (produit == null)
                return null;

            var dto = _mapper.Map<ProduitDto>(produit);
            await AppliquerRemisesAsync(produit, dto);

            return dto;
        }
        public async Task<ProduitDto> GetByCodeABarreAsync(string codeABarre)
        {
            var produit = await _context.Produits
                .Include(p => p.SousCategorie)
                .Include(p => p.Fournisseur)
                .Include(p => p.Marque)
                .Include(p => p.TauxTVA)
                .Include(p => p.Forme)
                .Include(p => p.Images)
                .Include(p => p.PromotionsApplicables)
                .FirstOrDefaultAsync(p => p.CodeABarre == codeABarre);

            if (produit == null)
                throw new KeyNotFoundException("Produit non trouvé avec ce code à barre");

            var dto = _mapper.Map<ProduitDto>(produit);
            await AppliquerRemisesAsync(produit, dto);

            return dto;
        }

        //Recherche/Filtrage
        public async Task<IEnumerable<Produit>> SearchAsync(string searchTerm) =>
            await _context.Produits
                .Where(p => p.Nom.Contains(searchTerm) ||
                            p.Description.Contains(searchTerm) ||
                            p.ReferenceOriginal.Contains(searchTerm) ||
                            p.ReferenceFournisseur != null && p.ReferenceFournisseur.Contains(searchTerm)||
                            p.CodeABarre.Contains(searchTerm))
                .OrderBy(p => p.Nom)
                .ToListAsync();
        public async Task<IEnumerable<Produit>> GetBySousCategorieAsync(int sousCategorieId) =>
           await _context.Produits
               .Where(p => p.SousCategorieId == sousCategorieId)
               .OrderBy(p => p.Nom)
               .ToListAsync();
        public async Task<IEnumerable<Produit>> GetByCategorieAsync(int categorieId) =>
            await _context.Produits
                .Where(p => p.SousCategorie.CategorieId == categorieId)
                .OrderBy(p => p.Nom)
                .ToListAsync();
        public async Task<IEnumerable<Produit>> GetByFournisseurAsync(int fournisseurId) =>
            await _context.Produits
                .Where(p => p.FournisseurId == fournisseurId)
                .OrderBy(p => p.Nom)
                .ToListAsync();
        public async Task<IEnumerable<Produit>> GetByMarqueAsync(int marqueId) =>
            await _context.Produits
                .Where(p => p.MarqueId == marqueId)
                .OrderBy(p => p.Nom)
                .ToListAsync();
        public async Task<IEnumerable<Produit>> GetByFormeAsync(int formeId)
        {
            return await _context.Produits
                .Where(p => p.FormeId == formeId)
                .Include(p => p.SousCategorie)
                .OrderBy(p => p.Nom)
                .ToListAsync();
        }
        public async Task<IEnumerable<ProduitDto>> GetProduitsEnPromotionAsync()
        {
            var produits = await _context.Produits
                .Include(p => p.TauxTVA)
                .Include(p => p.PromotionsApplicables)
                .Include(p => p.Images)
                .Where(p => p.PromotionsApplicables.Any(pr =>
                    pr.DateDebut <= DateTime.Now && (pr.DateFin == null || pr.DateFin >= DateTime.Now)))
                .AsNoTracking()
                .ToListAsync();

            var result = new List<ProduitDto>();

            foreach (var produit in produits)
            {
                var dto = _mapper.Map<ProduitDto>(produit);
                await AppliquerRemisesAsync(produit, dto);
                result.Add(dto);
            }

            return result;
        }
        public async Task<IEnumerable<ProduitDto>> GetProduitsEnStockAsync()
        {
            var produits = await _context.Produits
                .Include(p => p.TauxTVA)
                .Include(p => p.PromotionsApplicables)
                .Include(p => p.Images)
                .Where(p => p.Stock > 0)
                .AsNoTracking()
                .ToListAsync();

            var result = new List<ProduitDto>();

            foreach (var produit in produits)
            {
                var dto = _mapper.Map<ProduitDto>(produit);
                await AppliquerRemisesAsync(produit, dto);
                result.Add(dto);
            }

            return result;
        }
        public async Task<IEnumerable<ImageProduitDto>> GetImagesForProduitAsync(int produitId)
        {
            return await _context.ImagesProduit
                .Where(img => img.ProduitId == produitId)
                .OrderBy(img => img.Ordre)
                .Select(img => _mapper.Map<ImageProduitDto>(img))
                .AsNoTracking()
                .ToListAsync();
        }
        
        //Autre
        public async Task<IEnumerable<Produit>> GetMeilleuresVentesAsync(int limit = 20)
        {
            return await _context.Produits
                .Include(p => p.TauxTVA)
                .Include(p => p.Marque)
                .Include(p => p.Images)
                .Where(p => p.DetailsCommandes.Any())
                .OrderByDescending(p => p.DetailsCommandes.Sum(d => d.Quantite))
                .Take(limit)
                .Select(p => new Produit
                {
                    Id = p.Id,
                    Nom = p.Nom,
                    Description = p.Description,
                    PrixVenteHT = p.PrixVenteHT,
                    TauxTVA = p.TauxTVA,
                    Images = p.Images.Take(1).ToList(),
                    NoteMoyenne = p.NoteMoyenne,
                    DateAjout = p.DateAjout,
                    Marque = p.Marque,
                    DetailsCommandes = new List<DetailsCommande>
                    {
                new DetailsCommande
                {
                    Quantite = p.DetailsCommandes.Sum(d => d.Quantite)
                }
                    }
                })
                .AsNoTracking()
                .ToListAsync();
        }
        public async Task<IEnumerable<Produit>> GetNouveauxArrivagesAsync(int limit = 10)
        {
            var cutoffDate = DateTime.Now.AddDays(-30);

            return await _context.Produits
                .Include(p => p.TauxTVA)
                .Include(p => p.Marque)
                .Include(p => p.Images)
                .Where(p => p.DateAjout >= cutoffDate)
                .OrderByDescending(p => p.DateAjout)
                .Take(limit)
                .Select(p => new Produit
                {
                    Id = p.Id,
                    Nom = p.Nom,
                    Description = p.Description,
                    PrixVenteHT = p.PrixVenteHT,
                    TauxTVA = p.TauxTVA,
                    Images = p.Images.Take(1).ToList(),
                    NoteMoyenne = p.NoteMoyenne,
                    DateAjout = p.DateAjout,
                    Marque = p.Marque,
                    PromotionsApplicables = p.PromotionsApplicables
                        .Where(pr => pr.DateDebut <= DateTime.Now && pr.DateFin >= DateTime.Now)
                        .ToList()
                })
                .AsNoTracking()
                .ToListAsync();
        }
        public async Task<Dictionary<int, string>> GetProduitsForDropdownAsync() =>
            await _context.Produits
                .OrderBy(p => p.Nom)
                .ToDictionaryAsync(p => p.Id, p => $"{p.Nom} ({p.ReferenceOriginal})");
        
        //Images
        public async Task AddImageToProduitAsync(int produitId, string imagePath, bool? isMain = null)
        {
            var produit = await _context.Produits
                .Include(p => p.Images)
                .FirstOrDefaultAsync(p => p.Id == produitId)
                ?? throw new KeyNotFoundException("Produit introuvable");

            var newImage = new ImagesProduit
            {
                ImageUrl = imagePath,
                Ordre = produit.Images.Count + 1,
                IsMain = isMain ?? (produit.Images.Count == 0)
            };

            if (newImage.IsMain)
            {
                foreach (var img in produit.Images)
                    img.IsMain = false;
            }

            produit.Images.Add(newImage);
            await _context.SaveChangesAsync();
        }
        public async Task<string> RemoveImageFromProduitAsync(int produitId, int imageId)
        {
            var produit = await _context.Produits
                .Include(p => p.Images)
                .FirstOrDefaultAsync(p => p.Id == produitId) ?? throw new KeyNotFoundException();

            var image = produit.Images.FirstOrDefault(img => img.Id == imageId)
                ?? throw new KeyNotFoundException("Image introuvable");
            var imageUrl = image.ImageUrl;
            _context.ImagesProduit.Remove(image);
            await _context.SaveChangesAsync();
            return imageUrl;
        }
        public async Task UpdateImageAsync(int produitId, ImageProduitUpdateDto imageDto)
        {
            if (imageDto == null)
                throw new ArgumentNullException(nameof(imageDto));

            var image = await _context.ImagesProduit
                .FirstOrDefaultAsync(i => i.Id == imageDto.Id && i.ProduitId == produitId)
                ?? throw new KeyNotFoundException("Image non trouvée");

            _logger.LogInformation($"Modification image {image.Id} du produit {produitId}");

            if (imageDto.Ordre.HasValue)
            {
                image.Ordre = imageDto.Ordre.Value;
            }

            if (imageDto.IsMain.HasValue && imageDto.IsMain.Value)
            {
                await SetMainImageAsync(produitId, image.Id);
            }

            await _context.SaveChangesAsync();
        }
        public async Task SetMainImageAsync(int produitId, int imageId)
        {
            var produit = await _context.Produits
                .Include(p => p.Images)
                .FirstOrDefaultAsync(p => p.Id == produitId)
                ?? throw new KeyNotFoundException("Produit non trouvé");

            var targetImage = produit.Images.FirstOrDefault(img => img.Id == imageId)
                ?? throw new KeyNotFoundException("Image non trouvée pour ce produit");

            foreach (var img in produit.Images)
                img.IsMain = false;

            targetImage.IsMain = true;

            await _context.SaveChangesAsync();
        }
        
        //Validation
        public async Task<bool> IsReferenceUniqueAsync(string reference, int? ignoreId = null)
        {
            if (string.IsNullOrEmpty(reference)) return true;

            var query = _context.Produits
                .Where(p => p.ReferenceOriginal == reference || p.ReferenceFournisseur == reference);

            if (ignoreId.HasValue)
                query = query.Where(p => p.Id != ignoreId.Value);

            return !await query.AnyAsync();
        }
        public async Task<bool> IsCodeABarreUniqueAsync(string codeABarre, int? ignoreId = null)
        {
            var query = _context.Produits
                .Where(p => p.CodeABarre == codeABarre);

            if (ignoreId.HasValue)
                query = query.Where(p => p.Id != ignoreId.Value);

            return !await query.AnyAsync();
        }
        
        //Mise à jour specifiques
        public async Task UpdateStockAsync(ProduitStockUpdateDto stockDto)
        {
            var produit = await _context.Produits.FindAsync(stockDto.Id);
            if (produit == null)
                throw new KeyNotFoundException("Produit non trouvé");

            produit.Stock = stockDto.Stock;
            await _context.SaveChangesAsync();
        }
        public async Task UpdatePrixAsync(int produitId, decimal nouveauPrixHT)
        {
            var produit = await _context.Produits.FindAsync(produitId);
            if (produit == null) throw new KeyNotFoundException("Produit non trouvé");

            produit.MettreAJourPrix(nouveauPrixHT);
            await _context.SaveChangesAsync();
        }        
    }
}
