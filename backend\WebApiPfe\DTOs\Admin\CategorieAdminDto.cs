using WebApiPfe.Models.Entity;

namespace WebApiPfe.DTOs.Admin
{
    public class CategorieAdminDto
    {
        public int Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool EstValidee { get; set; }
        public List<SousCategorieAdminDto> SousCategories { get; set; } = new List<SousCategorieAdminDto>();
    }

    public class SousCategorieAdminDto
    {
        public int Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool EstValidee { get; set; }
        public int CategorieId { get; set; }
    }
}
