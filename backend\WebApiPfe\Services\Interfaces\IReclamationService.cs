using WebApiPfe.DTOs.ReclamationDTOs;
using WebApiPfe.Models;

namespace WebApiPfe.Services.Interfaces
{
    public interface IReclamationService
    {
        // CRUD de base
        Task<ReclamationReadDto> CreerReclamationAsync(CreateReclamationDto dto, int clientId);
        Task<ReclamationReadDto?> ObtenirReclamationAsync(int id);
        Task<List<ReclamationReadDto>> ObtenirReclamationsAsync(ReclamationFilterDto filter);
        Task<ReclamationReadDto> MettreAJourReclamationAsync(int id, UpdateReclamationDto dto, int utilisateurId);
        Task<bool> SupprimerReclamationAsync(int id);

        // Gestion par rôle
        Task<List<ReclamationReadDto>> ObtenirReclamationsClientAsync(int clientId, ReclamationFilterDto filter);
        Task<List<ReclamationReadDto>> ObtenirReclamationsFournisseurAsync(int fournisseurId, ReclamationFilterDto filter);
        Task<List<ReclamationReadDto>> ObtenirReclamationsAdminAsync(ReclamationFilterDto filter);

        // Actions spécifiques
        Task<ReclamationReadDto> TraiterReclamationAsync(int id, UpdateReclamationDto dto, int traiteurId);
        Task<ReclamationReadDto> ResoudreReclamationAsync(int id, string resolution, int traiteurId);
        Task<ReclamationReadDto> FermerReclamationAsync(int id, string commentaire, int traiteurId);
        Task<ReclamationReadDto> RejeterReclamationAsync(int id, string motif, int traiteurId);

        // Statistiques
        Task<ReclamationStatsDto> ObtenirStatistiquesAsync();
        Task<ReclamationStatsDto> ObtenirStatistiquesFournisseurAsync(int fournisseurId);

        // Notifications
        Task EnvoyerNotificationReclamationAsync(int reclamationId, string action);

        // Validation
        Task<bool> PeutCreerReclamationAsync(int clientId, int commandeId);
        Task<bool> PeutTraiterReclamationAsync(int utilisateurId, int reclamationId);

        // Upload de fichiers
        Task<List<string>> UploadPiecesJointesAsync(List<IFormFile> fichiers, int reclamationId);
    }
}
