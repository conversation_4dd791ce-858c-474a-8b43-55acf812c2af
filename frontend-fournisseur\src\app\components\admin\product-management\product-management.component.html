<div class="product-management-container">
  <div class="header">
    <h1>🛍️ Gestion des Produits</h1>
    <p class="subtitle"><PERSON><PERSON><PERSON>, mod<PERSON><PERSON> et gérez les produits de la plateforme</p>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section">
    <div class="search-bar">
      <input 
        type="text" 
        [(ngModel)]="searchTerm" 
        (input)="onSearchChange()"
        placeholder="🔍 Rechercher par nom, référence..."
        class="search-input">
    </div>

    <div class="filters-row">
      <div class="filter-group">
        <label>Statut :</label>
        <select [(ngModel)]="selectedStatut" (change)="onStatutChange()" class="filter-select">
          <option *ngFor="let option of statutOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <div class="filter-group">
        <label>Fournisseur :</label>
        <input 
          type="text" 
          [(ngModel)]="selectedFournisseur" 
          (input)="onFournisseurChange()"
          placeholder="Nom du fournisseur"
          class="filter-input">
      </div>

      <div class="filter-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            [(ngModel)]="showStockCritique" 
            (change)="onStockCritiqueToggle()"
            class="filter-checkbox">
          <span class="checkmark"></span>
          Stock critique uniquement
        </label>
      </div>
    </div>
  </div>

  <!-- Statistiques rapides -->
  <div class="stats-row">
    <div class="stat-card">
      <span class="stat-number">{{ totalItems() }}</span>
      <span class="stat-label">Produits total</span>
    </div>
    <div class="stat-card">
      <span class="stat-number">{{ getProduitsStockCritique() }}</span>
      <span class="stat-label">Stock critique</span>
    </div>
    <div class="stat-card">
      <span class="stat-number">{{ getProduitsEnAvant() }}</span>
      <span class="stat-label">Mis en avant</span>
    </div>
    <div class="stat-card">
      <span class="stat-number">{{ getProduitsEnAttente() }}</span>
      <span class="stat-label">En attente</span>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error()" class="error-message">
    ❌ {{ error() }}
  </div>

  <!-- Indicateur de chargement -->
  <div *ngIf="isLoading()" class="loading-spinner">
    <div class="spinner"></div>
    <p>Chargement des produits...</p>
  </div>

  <!-- Tableau des produits -->
  <div class="table-container" *ngIf="!isLoading()">
    <table class="products-table">
      <thead>
        <tr>
          <th>Image</th>
          <th>Produit</th>
          <th>Référence</th>
          <th>Prix</th>
          <th>Stock</th>
          <th>Fournisseur</th>
          <th>Catégorie</th>
          <th>Statut</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let produit of produits()" class="product-row" [ngClass]="getStatusClass(produit)">
          <td class="image-cell">
            <img
              [src]="imageUrlService.getProduitImageUrl(produit.imagePrincipale || (produit.images && produit.images.length > 0 ? produit.images[0] : null))"
              [alt]="produit.nom"
              class="product-image"
              (error)="onImageError($event)">
          </td>
          
          <td class="product-info">
            <div class="product-name">{{ produit.nom }}</div>
            <div class="product-meta">
              <span class="date">Ajouté le {{ formatDate(produit.dateCreation) }}</span>
              <span *ngIf="produit.estEnAvant" class="featured-badge">⭐ En avant</span>
            </div>
          </td>
          
          <td class="reference-cell">
            <div class="ref-original">{{ produit.referenceOriginal }}</div>
            <div *ngIf="produit.referenceFournisseur" class="ref-fournisseur">
              Fournisseur: {{ produit.referenceFournisseur }}
            </div>
          </td>
          
          <td class="price-cell">
            <div class="price-container">
              <!-- Prix initial (toujours affiché) -->
              <div class="price-initial">
                <span class="price-label">Prix initial:</span>
                <span class="price-value initial" [class.crossed]="produit.pourcentageRemiseTotale > 0">
                  {{ formatPrice(produit.prixVenteTTC) }}
                </span>
              </div>

              <!-- Prix après promotion outlet (si différent du prix initial) -->
              <div class="price-outlet" *ngIf="produit.prixApresOutletTTC !== produit.prixVenteTTC">
                <span class="price-label">Après outlet:</span>
                <span class="price-value outlet">{{ formatPrice(produit.prixApresOutletTTC) }}</span>
              </div>

              <!-- Prix final (si différent du prix outlet) -->
              <div class="price-final" *ngIf="produit.prixFinalTTC !== produit.prixApresOutletTTC">
                <span class="price-label">Prix final:</span>
                <span class="price-value final">{{ formatPrice(produit.prixFinalTTC) }}</span>
              </div>

              <!-- Badge de remise totale -->
              <div class="discount-badge" *ngIf="produit.pourcentageRemiseTotale > 0">
                -{{ produit.pourcentageRemiseTotale }}%
              </div>
            </div>
          </td>
          
          <td class="stock-cell">
            <div class="stock-container">
              <span class="stock-value" [ngClass]="{'stock-critique': produit.estStockCritique}">
                {{ produit.stock }}
              </span>
              <div class="stock-alert" *ngIf="produit.estStockCritique">
                ⚠️ Stock critique (≤10)
              </div>
              <button
                class="btn-stock"
                (click)="openStockModal(produit)"
                title="Modifier le stock">
                📦
              </button>
            </div>
          </td>
          
          <td class="supplier-cell">
            {{ produit.fournisseurNom }}
          </td>
          
          <td class="category-cell">
            <div class="category">{{ produit.categorieNom }}</div>
            <div class="subcategory">{{ produit.sousCategorieNom }}</div>
          </td>
          
          <td class="status-cell">
            <span class="status-badge" [ngClass]="getStatusClass(produit)">
              {{ produit.statutValidation }}
            </span>
            <span *ngIf="produit.estStockCritique" class="stock-warning">⚠️ Stock critique</span>
          </td>
          
          <td class="actions-cell">
            <div class="action-buttons">
              <!-- Validation -->
              <button 
                *ngIf="!produit.estValide"
                class="btn-action btn-validate" 
                (click)="validerProduit(produit)"
                title="Valider le produit">
                ✅
              </button>
              
              <button 
                *ngIf="!produit.estValide"
                class="btn-action btn-reject" 
                (click)="refuserProduit(produit)"
                title="Refuser le produit">
                ❌
              </button>
              
              <!-- Mise en avant -->
              <button 
                class="btn-action" 
                [ngClass]="produit.estEnAvant ? 'btn-unfeature' : 'btn-feature'"
                (click)="toggleMiseEnAvant(produit)"
                [title]="produit.estEnAvant ? 'Retirer la mise en avant' : 'Mettre en avant'">
                {{ produit.estEnAvant ? '⭐' : '☆' }}
              </button>
              
              <!-- Modération -->
              <button 
                class="btn-action btn-moderate" 
                (click)="openModerationModal(produit)"
                title="Modérer le contenu">
                🔍
              </button>
              
              <!-- Suppression -->
              <button 
                class="btn-action btn-delete" 
                (click)="supprimerProduit(produit)"
                title="Supprimer le produit">
                🗑️
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Message si aucun produit -->
    <div *ngIf="produits().length === 0" class="no-data">
      <p>Aucun produit trouvé avec les critères sélectionnés.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="totalPages() > 1">
    <div class="pagination-info">
      <p>Page {{ currentPage() }} sur {{ totalPages() }} ({{ totalItems() }} produits au total)</p>
    </div>
    
    <div class="pagination-controls">
      <button 
        class="btn-page" 
        [disabled]="currentPage() === 1"
        (click)="onPageChange(currentPage() - 1)">
        ← Précédent
      </button>
      
      <span class="page-numbers">
        <button
          *ngFor="let page of getPageNumbers(); let i = index"
          class="btn-page-number"
          [ngClass]="{'active': currentPage() === page}"
          (click)="onPageChange(page)">
          {{ page }}
        </button>
      </span>
      
      <button 
        class="btn-page" 
        [disabled]="currentPage() === totalPages()"
        (click)="onPageChange(currentPage() + 1)">
        Suivant →
      </button>
    </div>
  </div>
</div>

<!-- Modal de modération -->
<div *ngIf="showModerationModal()" class="modal-overlay" (click)="closeModerationModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>🔍 Modération du contenu</h3>
      <button class="btn-close" (click)="closeModerationModal()">×</button>
    </div>
    
    <div class="modal-body">
      <div class="form-group">
        <label>Nouveau nom :</label>
        <input 
          type="text" 
          [(ngModel)]="moderationData().nouveauNom"
          class="form-input">
      </div>
      
      <div class="form-group">
        <label>Nouvelle description :</label>
        <textarea 
          [(ngModel)]="moderationData().nouvelleDescription"
          class="form-textarea"
          rows="4"></textarea>
      </div>
      
      <div class="form-group">
        <label>Raison de la modération :</label>
        <textarea 
          [(ngModel)]="moderationData().raison"
          class="form-textarea"
          rows="3"></textarea>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn-secondary" (click)="closeModerationModal()">Annuler</button>
      <button class="btn-primary" (click)="submitModeration()">Appliquer</button>
    </div>
  </div>
</div>

<!-- Modal de gestion du stock -->
<div *ngIf="showStockModal()" class="modal-overlay" (click)="closeStockModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>📦 Gestion du stock</h3>
      <button class="btn-close" (click)="closeStockModal()">×</button>
    </div>
    
    <div class="modal-body">
      <div class="form-group">
        <label>Nouveau stock :</label>
        <input 
          type="number" 
          [(ngModel)]="nouveauStock"
          min="0"
          class="form-input">
      </div>
      
      <div class="stock-info">
        <p><strong>Stock actuel :</strong> {{ selectedProduit()?.stock }}</p>
        <p *ngIf="selectedProduit()?.estStockCritique" class="warning">
          ⚠️ Ce produit est actuellement en stock critique
        </p>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn-secondary" (click)="closeStockModal()">Annuler</button>
      <button class="btn-primary" (click)="submitStockUpdate()">Mettre à jour</button>
    </div>
  </div>
</div>
