import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { DashboardService, DashboardStats, RecentOrder, RecentActivity, Fournisseur } from '../../services/dashboard.service';
import { User } from '../../models/user.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;
  fournisseurInfo: Fournisseur | null = null;

  // États de chargement
  isLoading = true;
  isStatsLoading = true;
  isOrdersLoading = true;
  isActivityLoading = true;

  // Statistiques du dashboard (données réelles)
  stats: DashboardStats = {
    totalProducts: 0,
    activeOrders: 0,
    pendingDeliveries: 0,
    monthlyRevenue: 0
  };

  // Commandes récentes (données réelles)
  recentOrders: RecentOrder[] = [];

  // Activité récente (données réelles)
  recentActivity: RecentActivity[] = [];

  // Données pour le graphique des ventes (calculées dynamiquement)
  public salesData: { month: string, sales: number }[] = [];

  constructor(
    private authService: AuthService,
    private dashboardService: DashboardService
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });

    // Charger les données du dashboard
    this.loadDashboardData();
  }

  /**
   * Charger toutes les données du dashboard
   */
  private loadDashboardData(): void {
    this.isLoading = true;
    console.log('🔄 Chargement des données du dashboard...');

    // Vérifier l'utilisateur connecté
    const currentUser = this.authService.getCurrentUser();
    const supplierId = localStorage.getItem('supplierId');
    console.log('👤 Utilisateur connecté:', currentUser);
    console.log('🏪 ID Fournisseur (localStorage):', supplierId);

    // Charger les informations du fournisseur
    this.dashboardService.getFournisseurInfo().subscribe({
      next: (fournisseur) => {
        this.fournisseurInfo = fournisseur;
        console.log('✅ Informations fournisseur chargées:', fournisseur);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement du fournisseur:', error);
      }
    });

    // Charger les statistiques
    this.loadStats();

    // Charger les commandes récentes
    this.loadRecentOrders();

    // Charger l'activité récente
    this.loadRecentActivity();

    // Calculer les données de ventes
    this.calculateSalesData();

    this.isLoading = false;
  }

  /**
   * Charger les statistiques
   */
  private loadStats(): void {
    this.isStatsLoading = true;
    console.log('📊 Chargement des statistiques...');

    this.dashboardService.getDashboardStats().subscribe({
      next: (stats) => {
        this.stats = stats;
        this.isStatsLoading = false;
        console.log('✅ Statistiques chargées:', stats);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des statistiques:', error);
        this.isStatsLoading = false;
        // Garder les valeurs par défaut (0)
      }
    });
  }

  /**
   * Charger les commandes récentes
   */
  private loadRecentOrders(): void {
    this.isOrdersLoading = true;

    this.dashboardService.getRecentOrders().subscribe({
      next: (orders) => {
        this.recentOrders = orders;
        this.isOrdersLoading = false;
        console.log('Commandes récentes chargées:', orders);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des commandes:', error);
        this.isOrdersLoading = false;
        // Garder un tableau vide
      }
    });
  }

  /**
   * Charger l'activité récente
   */
  private loadRecentActivity(): void {
    this.isActivityLoading = true;

    this.dashboardService.getRecentActivity().subscribe({
      next: (activity) => {
        this.recentActivity = activity;
        this.isActivityLoading = false;
        console.log('Activité récente chargée:', activity);
      },
      error: (error) => {
        console.error('Erreur lors du chargement de l\'activité:', error);
        this.isActivityLoading = false;
        // Garder un tableau vide
      }
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Livré':
        return 'status-delivered';
      case 'En cours':
        return 'status-processing';
      case 'En préparation':
        return 'status-preparing';
      default:
        return 'status-default';
    }
  }

  /**
   * Calculer les données de ventes pour le graphique
   */
  private calculateSalesData(): void {
    this.dashboardService.getFournisseurOrders().subscribe({
      next: (orders) => {
        // Calculer les ventes des 6 derniers mois
        const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                           'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];

        const currentDate = new Date();
        const salesByMonth: { month: string, sales: number }[] = [];

        // Générer les 6 derniers mois
        for (let i = 5; i >= 0; i--) {
          const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
          const monthName = monthNames[monthDate.getMonth()];

          // Calculer les ventes pour ce mois
          const monthlySales = orders
            .filter(order => {
              const orderDate = new Date(order.dateCommande);
              return orderDate.getMonth() === monthDate.getMonth() &&
                     orderDate.getFullYear() === monthDate.getFullYear();
            })
            .reduce((total, order) => total + order.montantTotal, 0);

          salesByMonth.push({
            month: monthName,
            sales: Math.round(monthlySales) // Arrondir pour l'affichage
          });
        }

        this.salesData = salesByMonth;
        console.log('📊 Données de ventes calculées:', this.salesData);
      },
      error: (error) => {
        console.error('❌ Erreur lors du calcul des données de ventes:', error);
        // En cas d'erreur, laisser le tableau vide
        this.salesData = [];
      }
    });
  }

  /**
   * Obtenir la valeur maximale des ventes pour normaliser le graphique
   */
  getMaxSales(): number {
    if (this.salesData.length === 0) return 1;
    const maxSales = Math.max(...this.salesData.map(data => data.sales));
    return maxSales > 0 ? maxSales : 1;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(amount);
  }
}
