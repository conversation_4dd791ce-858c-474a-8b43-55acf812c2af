import { Injectable } from '@angular/core';
import { 
  HttpInterceptor, 
  HttpRequest, 
  HttpHandler, 
  HttpEvent, 
  HttpErrorResponse 
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthService } from './auth.service';

@Injectable()
export class HttpInterceptorService implements HttpInterceptor {

  constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Ajouter le token d'authentification si disponible
    const token = this.authService.getToken();
    let authReq = req;

    // Debug pour les demandes
    if (req.url.includes('DemandesCategories') || req.url.includes('DemandesSousCategories')) {
      console.log('🔍 INTERCEPTOR DEBUG - URL:', req.url);
      console.log('🔍 INTERCEPTOR DEBUG - Method:', req.method);
      console.log('🔍 INTERCEPTOR DEBUG - Token présent:', !!token);
      console.log('🔍 INTERCEPTOR DEBUG - Token value:', token ? token.substring(0, 20) + '...' : 'null');
    }

    if (token) {
      authReq = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${token}`)
      });

      if (req.url.includes('DemandesCategories') || req.url.includes('DemandesSousCategories')) {
        console.log('✅ Token ajouté à la requête');
      }
    } else {
      if (req.url.includes('DemandesCategories') || req.url.includes('DemandesSousCategories')) {
        console.log('❌ Pas de token - requête sans authentification');
      }
    }

    // Ajouter les headers par défaut
    authReq = authReq.clone({
      headers: authReq.headers
        .set('Content-Type', 'application/json')
        .set('Accept', 'application/json')
    });

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        return this.handleError(error);
      })
    );
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Une erreur est survenue';

    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Erreur: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      switch (error.status) {
        case 400:
          errorMessage = 'Requête invalide';
          break;
        case 401:
          errorMessage = 'Non autorisé - Veuillez vous reconnecter';
          this.authService.logout();
          break;
        case 403:
          errorMessage = 'Accès interdit';
          break;
        case 404:
          errorMessage = 'Ressource non trouvée';
          break;
        case 500:
          errorMessage = 'Erreur serveur interne';
          break;
        default:
          errorMessage = `Erreur ${error.status}: ${error.message}`;
      }

      // Essayer d'extraire le message d'erreur du serveur
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        } else if (error.error.title) {
          errorMessage = error.error.title;
        }
      } else if (typeof error.error === 'string') {
        errorMessage = error.error;
      }
    }

    console.error('Erreur HTTP:', error);
    return throwError(() => new Error(errorMessage));
  }
}
