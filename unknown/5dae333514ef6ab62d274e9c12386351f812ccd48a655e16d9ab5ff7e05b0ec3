﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MarquesController : ControllerBase
    {
        private readonly IMarqueService _marqueService;

        public MarquesController(IMarqueService marqueService)
        {
            _marqueService = marqueService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Marque>>> GetAll()
        {
            return Ok(await _marqueService.GetAllAsync());
        }

        [HttpGet("enriched")]
        public async Task<ActionResult<IEnumerable<MarqueDto>>> GetAllEnriched()
        {
            var marques = await _marqueService.GetAllAsync();
            var marquesDto = marques.Select(m => new MarqueDto
            {
                Id = m.Id,
                Name = m.Name,
                Logo = m.Logo
            });

            // Log pour déboguer
            foreach (var m in marquesDto)
            {
                Console.WriteLine($"Marque: {m.Name}, Logo: {m.Logo}");
            }

            return Ok(marquesDto);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Marque>> GetById(int id)
        {
            var marque = await _marqueService.GetByIdAsync(id);
            return marque != null ? Ok(marque) : NotFound();
        }

        [HttpGet("dropdown")]
        public async Task<ActionResult<Dictionary<int, string>>> GetForDropdown()
        {
            return Ok(await _marqueService.GetMarquesForDropdownAsync());
        }

        [HttpPost]
        public async Task<ActionResult<Marque>> Create([FromBody] Marque marque)
        {
            try
            {
                var created = await _marqueService.CreateAsync(marque);
                return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] Marque marque)
        {
            if (id != marque.Id) return BadRequest("ID mismatch");

            try
            {
                await _marqueService.UpdateAsync(marque);
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _marqueService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
