<div class="product-form-container">
  <div class="form-header">
    <h2>{{ isEdit ? 'Modifier le produit' : 'Ajouter un nouveau produit' }}</h2>
    <button type="button" class="btn-close" (click)="onCancel()">✕</button>
  </div>

  <form [formGroup]="productForm" (ngSubmit)="onSubmit()" class="product-form">
    
    <!-- Section Images (max 10) -->
    <div class="form-section">
      <h3>📷 Images du produit (max {{ maxImages }})</h3>

      <div class="images-upload-section">
        <!-- Zone d'ajout d'images -->
        <div class="image-upload" *ngIf="selectedImageFiles.length < maxImages">
          <label for="images" class="upload-label">
            <div class="upload-placeholder">
              <div class="upload-icon">📷</div>
              <div class="upload-text">Cliquez pour ajouter des images</div>
              <div class="upload-hint">JPG, PNG, GIF - Max 5MB par image</div>
              <div class="upload-count">{{ selectedImageFiles.length }}/{{ maxImages }} images</div>
            </div>
          </label>
          <input
            type="file"
            id="images"
            accept="image/*"
            multiple
            (change)="onImagesChange($event)"
            style="display: none;"
          />
        </div>

        <!-- Aperçu des images sélectionnées -->
        <div class="images-preview" *ngIf="selectedImageFiles.length > 0">
          <div class="image-item" *ngFor="let preview of imagePreviewUrls; let i = index">
            <div class="image-container">
              <img [src]="preview" [alt]="'Image ' + (i + 1)" />
              <div class="image-badge" *ngIf="i === 0">Principale</div>

              <div class="image-actions">
                <button type="button" class="btn-action btn-up"
                        (click)="moveImageUp(i)"
                        [disabled]="i === 0"
                        title="Déplacer vers le haut">
                  ⬆️
                </button>
                <button type="button" class="btn-action btn-down"
                        (click)="moveImageDown(i)"
                        [disabled]="i === selectedImageFiles.length - 1"
                        title="Déplacer vers le bas">
                  ⬇️
                </button>
                <button type="button" class="btn-action btn-remove"
                        (click)="removeImage(i)"
                        title="Supprimer">
                  🗑️
                </button>
              </div>
            </div>
            <div class="image-info">
              <span class="image-name">{{ selectedImageFiles[i].name }}</span>
              <span class="image-size">{{ (selectedImageFiles[i].size / 1024 / 1024).toFixed(2) }} MB</span>
            </div>
          </div>
        </div>

        <div class="upload-help" *ngIf="selectedImageFiles.length === 0">
          <p>💡 <strong>Conseils pour de meilleures images :</strong></p>
          <ul>
            <li>La première image sera l'image principale</li>
            <li>Utilisez des images de haute qualité (min 800x600px)</li>
            <li>Montrez le produit sous différents angles</li>
            <li>Évitez les images floues ou mal éclairées</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Section Informations de base -->
    <div class="form-section">
      <h3>📝 Informations de base</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="nom">Nom du produit *</label>
          <input
            type="text"
            id="nom"
            formControlName="nom"
            class="form-control"
            [class.is-invalid]="hasFieldError('nom')"
            placeholder="Ex: Ray-Ban Aviator Classic"
          />
          <div class="invalid-feedback" *ngIf="hasFieldError('nom')">
            {{ getFieldError('nom') }}
          </div>
        </div>

        <div class="form-group">
          <label for="marque">Marque *</label>
          <select
            id="marque"
            formControlName="marque"
            class="form-control"
            [class.is-invalid]="hasFieldError('marque')"
          >
            <option value="">Sélectionner une marque</option>
            <option *ngFor="let brand of brands" [value]="brand.value">
              {{ brand.label }}
            </option>
          </select>
          <div class="invalid-feedback" *ngIf="hasFieldError('marque')">
            {{ getFieldError('marque') }}
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="description">Description *</label>
        <textarea
          id="description"
          formControlName="description"
          class="form-control"
          [class.is-invalid]="hasFieldError('description')"
          rows="4"
          placeholder="Décrivez les caractéristiques des lunettes (forme, matériau, protection UV, etc.)..."
        ></textarea>
        <div class="invalid-feedback" *ngIf="hasFieldError('description')">
          {{ getFieldError('description') }}
        </div>
      </div>

    </div>

    <!-- Section Classification -->
    <div class="form-section">
      <h3>🏷️ Classification</h3>

      <div class="form-row">
        <div class="form-group">
          <label for="categorieId">Catégorie *</label>
          <select
            id="categorieId"
            formControlName="categorieId"
            class="form-control"
            [class.is-invalid]="hasFieldError('categorieId')"
            (change)="onCategorieChange($event)"
          >
            <option value="">Sélectionner une catégorie</option>
            <option *ngFor="let categorie of categoriesDropdown" [value]="categorie.id">
              {{ categorie.nom }}
            </option>
          </select>
          <div class="invalid-feedback" *ngIf="hasFieldError('categorieId')">
            {{ getFieldError('categorieId') }}
          </div>
        </div>

        <div class="form-group">
          <label for="sousCategorieId">Sous-catégorie *</label>
          <select
            id="sousCategorieId"
            formControlName="sousCategorieId"
            class="form-control"
            [class.is-invalid]="hasFieldError('sousCategorieId')"
            [disabled]="sousCategoriesDropdown.length === 0"
          >
            <option value="">Sélectionner une sous-catégorie</option>
            <option *ngFor="let sousCategorie of sousCategoriesDropdown" [value]="sousCategorie.id">
              {{ sousCategorie.nom }}
            </option>
          </select>
          <div class="invalid-feedback" *ngIf="hasFieldError('sousCategorieId')">
            {{ getFieldError('sousCategorieId') }}
          </div>
          <small class="form-text text-muted" *ngIf="sousCategoriesDropdown.length === 0">
            Veuillez d'abord sélectionner une catégorie
          </small>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="marqueId">Marque *</label>
          <select
            id="marqueId"
            formControlName="marqueId"
            class="form-control"
            [class.is-invalid]="hasFieldError('marqueId')"
          >
            <option value="">Sélectionner une marque</option>
            <option *ngFor="let marque of marquesDropdown" [value]="marque.id">
              {{ marque.nom }}
            </option>
          </select>
          <div class="invalid-feedback" *ngIf="hasFieldError('marqueId')">
            {{ getFieldError('marqueId') }}
          </div>
        </div>

        <div class="form-group">
          <label for="formeId">Forme *</label>
          <select
            id="formeId"
            formControlName="formeId"
            class="form-control"
            [class.is-invalid]="hasFieldError('formeId')"
            [disabled]="formesDropdown.length === 0"
          >
            <option value="">Sélectionner une forme</option>
            <option *ngFor="let forme of formesDropdown" [value]="forme.id">
              {{ forme.nom }}
            </option>
          </select>
          <div class="invalid-feedback" *ngIf="hasFieldError('formeId')">
            {{ getFieldError('formeId') }}
          </div>
          <small class="form-text text-muted" *ngIf="formesDropdown.length === 0">
            Veuillez d'abord sélectionner une catégorie
          </small>
        </div>
      </div>

      <div class="form-group">
        <label for="tauxTVAId">Taux TVA * <small class="text-muted">(Automatique selon la catégorie)</small></label>
        <select
          id="tauxTVAId"
          formControlName="tauxTVAId"
          class="form-control"
          [class.is-invalid]="hasFieldError('tauxTVAId')"
          [class.bg-light]="productForm.get('tauxTVAId')?.disabled"
        >
          <option value="">Sélectionner une catégorie d'abord</option>
          <option *ngFor="let taux of tauxTVADropdown" [value]="taux.id">
            {{ taux.libelle }} ({{ taux.taux }}%)
          </option>
        </select>
        <div class="invalid-feedback" *ngIf="hasFieldError('tauxTVAId')">
          {{ getFieldError('tauxTVAId') }}
        </div>
        <small class="form-text text-muted" *ngIf="productForm.get('tauxTVAId')?.disabled">
          Le taux TVA est automatiquement sélectionné selon la catégorie choisie.
        </small>

        <!-- Debug temporaire -->
        <div class="mt-2">
          <button type="button" class="btn btn-sm btn-info" (click)="debugTauxTVA()">
            🐛 Debug TVA ({{ tauxTVADropdown.length }} taux)
          </button>
          <small class="text-muted ml-2">
            Taux chargés: {{ tauxTVADropdown.length }}
          </small>
        </div>
      </div>
    </div>

    <!-- Section Prix -->
    <div class="form-section">
      <h3>💰 Prix et promotion</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="prixAchatHT">Prix d'achat HT *</label>
          <div class="input-group">
            <input
              type="number"
              id="prixAchatHT"
              formControlName="prixAchatHT"
              class="form-control"
              [class.is-invalid]="hasFieldError('prixAchatHT')"
              placeholder="0.00"
              step="0.01"
              min="0"
            />
            <span class="input-suffix">DT</span>
          </div>
          <div class="invalid-feedback" *ngIf="hasFieldError('prixAchatHT')">
            {{ getFieldError('prixAchatHT') }}
          </div>
        </div>

        <div class="form-group">
          <label for="prixVenteHT">Prix de vente HT *</label>
          <div class="input-group">
            <input
              type="number"
              id="prixVenteHT"
              formControlName="prixVenteHT"
              class="form-control"
              [class.is-invalid]="hasFieldError('prixVenteHT')"
              placeholder="0.00"
              step="0.01"
              min="0"
            />
            <span class="input-suffix">DT</span>
          </div>
          <div class="invalid-feedback" *ngIf="hasFieldError('prixVenteHT')">
            {{ getFieldError('prixVenteHT') }}
          </div>
          <small class="form-text text-muted">
            Prix TTC calculé : <strong>{{ getPrixTTC() | number:'1.2-2' }} DT</strong>
            <span *ngIf="productForm.get('tauxTVAId')?.value">({{ getTauxTVALibelle() }})</span>
          </small>
        </div>
      </div>

      <div class="form-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            formControlName="estEnPromotion"
            class="checkbox"
          />
          <span class="checkmark"></span>
          Produit en promotion
        </label>
      </div>

      <!-- Aperçu du prix -->
      <div class="price-preview" *ngIf="productForm.get('prix')?.value">
        <div class="price-info">
          <div class="final-price">
            Prix final : <strong>{{ formatPrice(getFinalPrice()) }}</strong>
          </div>
          <div class="discount" *ngIf="getDiscountPercentage() > 0">
            Réduction : <span class="discount-badge">-{{ getDiscountPercentage() }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Section Stock -->
    <div class="form-section">
      <h3>📦 Gestion du stock</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="quantiteStock">Quantité en stock *</label>
          <input
            type="number"
            id="quantiteStock"
            formControlName="quantiteStock"
            class="form-control"
            [class.is-invalid]="hasFieldError('quantiteStock')"
            placeholder="0"
            min="0"
          />
          <div class="invalid-feedback" *ngIf="hasFieldError('quantiteStock')">
            {{ getFieldError('quantiteStock') }}
          </div>
        </div>

        <div class="form-group">
          <label for="seuilAlerte">Seuil d'alerte *</label>
          <input
            type="number"
            id="seuilAlerte"
            formControlName="seuilAlerte"
            class="form-control"
            [class.is-invalid]="hasFieldError('seuilAlerte')"
            placeholder="5"
            min="0"
          />
          <div class="invalid-feedback" *ngIf="hasFieldError('seuilAlerte')">
            {{ getFieldError('seuilAlerte') }}
          </div>
          <small class="form-hint">Alerte quand le stock descend en dessous de cette valeur</small>
        </div>
      </div>

      <!-- Indicateur de stock -->
      <div class="stock-indicator" *ngIf="productForm.get('quantiteStock')?.value !== null">
        <div class="stock-status" 
             [class.stock-ok]="productForm.get('quantiteStock')?.value > productForm.get('seuilAlerte')?.value"
             [class.stock-warning]="productForm.get('quantiteStock')?.value <= productForm.get('seuilAlerte')?.value && productForm.get('quantiteStock')?.value > 0"
             [class.stock-empty]="productForm.get('quantiteStock')?.value === 0">
          
          <span *ngIf="productForm.get('quantiteStock')?.value > productForm.get('seuilAlerte')?.value" class="status-icon">✅</span>
          <span *ngIf="productForm.get('quantiteStock')?.value <= productForm.get('seuilAlerte')?.value && productForm.get('quantiteStock')?.value > 0" class="status-icon">⚠️</span>
          <span *ngIf="productForm.get('quantiteStock')?.value === 0" class="status-icon">❌</span>
          
          <span class="status-text">
            {{ productForm.get('quantiteStock')?.value === 0 ? 'Rupture de stock' : 
               productForm.get('quantiteStock')?.value <= productForm.get('seuilAlerte')?.value ? 'Stock faible' : 'Stock suffisant' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Section Détails techniques -->
    <div class="form-section">
      <h3>🔧 Détails techniques</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="couleur">Couleur</label>
          <input
            type="text"
            id="couleur"
            formControlName="couleur"
            class="form-control"
            placeholder="Ex: Noir brillant, Écaille, Doré, Transparent"
          />
        </div>

        <div class="form-group">
          <label for="poids">Poids (g)</label>
          <input
            type="number"
            id="poids"
            formControlName="poids"
            class="form-control"
            placeholder="0"
            min="0"
          />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="dimensions">Dimensions</label>
          <input
            type="text"
            id="dimensions"
            formControlName="dimensions"
            class="form-control"
            placeholder="Ex: 58-14-135 mm (largeur-pont-branche)"
          />
        </div>

        <div class="form-group">
          <label for="garantie">Garantie (mois)</label>
          <input
            type="number"
            id="garantie"
            formControlName="garantie"
            class="form-control"
            placeholder="12"
            min="0"
            max="120"
          />
        </div>
      </div>
    </div>

    <!-- Section Références -->
    <div class="form-section">
      <h3>🏷️ Références et mots-clés</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="numeroSerie">Numéro de série</label>
          <input
            type="text"
            id="numeroSerie"
            formControlName="numeroSerie"
            class="form-control"
            placeholder="Ex: ABC123456"
          />
        </div>

        <div class="form-group">
          <label for="codeBarres">Code-barres</label>
          <input
            type="text"
            id="codeBarres"
            formControlName="codeBarres"
            class="form-control"
            placeholder="Ex: 1234567890123"
          />
        </div>
      </div>

      <div class="form-group">
        <label for="motsCles">Mots-clés (séparés par des virgules)</label>
        <input
          type="text"
          id="motsCles"
          formControlName="motsCles"
          class="form-control"
          placeholder="Ex: lunettes, soleil, polarisé, UV400, aviateur"
        />
        <small class="form-hint">Aide à améliorer la recherche du produit</small>
      </div>
    </div>

    <!-- Section Statut -->
    <div class="form-section">
      <h3>⚙️ Statut du produit</h3>
      
      <div class="form-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            formControlName="estActif"
            class="checkbox"
          />
          <span class="checkmark"></span>
          Produit actif (visible sur le site)
        </label>
      </div>
    </div>

    <!-- Actions -->
    <div class="form-actions">
      <button type="button" class="btn btn-secondary" (click)="onCancel()" [disabled]="isSubmitting">
        Annuler
      </button>
      <button type="submit" class="btn btn-primary" [disabled]="productForm.invalid || isSubmitting">
        <span *ngIf="isSubmitting" class="spinner"></span>
        {{ isEdit ? 'Mettre à jour' : 'Ajouter le produit' }}
      </button>
    </div>
  </form>
</div>
