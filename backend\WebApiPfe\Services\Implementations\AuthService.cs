﻿using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.AuthDTO;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Interfaces;
using WebApiPfe.Services.Token;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using WebApiPfe.Models.Entity;
using WebApiPfe.DTOs.CreateDTOs;

namespace WebApiPfe.Services.Implementations
{
    public class AuthService : IAuthService
    {
        private readonly AppDbContext _context;
        private readonly ITokenService _tokenService;
        private readonly IMapper _mapper;
        private readonly IPasswordHasher<Utilisateur> _passwordHasher;
        private readonly UserManager<Utilisateur> _userManager;
        private readonly IConfiguration _configuration;

        public AuthService(
            AppDbContext context,
            ITokenService tokenService,
            IMapper mapper,
            IPasswordHasher<Utilisateur> passwordHasher,
            UserManager<Utilisateur> userManager,
            IConfiguration configuration)
        {
            _context = context;
            _tokenService = tokenService;
            _mapper = mapper;
            _passwordHasher = passwordHasher;
            _userManager = userManager;
            _configuration = configuration;
        }

        public async Task<AuthResponseDto> AuthentifierAsync(LoginDto dto)
        {
            Console.WriteLine($"🔍 Tentative de connexion - Email: {dto?.Email}, Password: {(dto?.Password != null ? "***" : "null")}");

            if (dto == null)
                throw new ArgumentNullException(nameof(dto), "Les données de connexion sont requises.");

            if (string.IsNullOrEmpty(dto.Email))
                throw new ArgumentException("L'email est requis.", nameof(dto.Email));

            if (string.IsNullOrEmpty(dto.Password))
                throw new ArgumentException("Le mot de passe est requis.", nameof(dto.Password));

            var utilisateur = await _userManager.Users
                .FirstOrDefaultAsync(u => u.Email == dto.Email);

            if (utilisateur == null)
                throw new UnauthorizedAccessException("Email ou mot de passe invalide.");

            var isValidPassword = await _userManager.CheckPasswordAsync(utilisateur, dto.Password);
            if (!isValidPassword)
                throw new UnauthorizedAccessException("Email ou mot de passe invalide.");

            // Mettre à jour la dernière connexion
            utilisateur.DerniereConnexion = DateTime.UtcNow;
            await _userManager.UpdateAsync(utilisateur);

            var token = await _tokenService.CreateTokenAsync(utilisateur);

            var utilisateurDto = new UtilisateurReadDto
            {
                Id = utilisateur.Id,
                Nom = utilisateur.Nom,
                Prenom = utilisateur.Prenom,
                Email = utilisateur.Email!,
                PhoneNumber = utilisateur.PhoneNumber ?? "",
                Role = utilisateur.GetRoleSpecifique(),
                DerniereConnexion = utilisateur.DerniereConnexion
            };

            return new AuthResponseDto
            {
                Token = token,
                Expiration = DateTime.UtcNow.AddHours(_configuration.GetValue<int>("JWT:ExpireHours")),
                Utilisateur = utilisateurDto
            };
        }
        public async Task<ClientDto> CreerClientAsync(ClientCreateDto dto)
        {
            var client = _mapper.Map<Client>(dto);
            client.UserName = dto.Email;

            var result = await _userManager.CreateAsync(client, dto.Password);
            if (!result.Succeeded)
                throw new ApplicationException("Échec de la création du client : " + string.Join(", ", result.Errors.Select(e => e.Description)));

            await _userManager.AddToRoleAsync(client, "Client");

            return _mapper.Map<ClientDto>(client);
        }

        public async Task<FournisseurDto> CreerFournisseurAsync(FournisseurCreateDto dto)
        {
            var fournisseur = _mapper.Map<Fournisseur>(dto);
            fournisseur.UserName = dto.Email;

            var result = await _userManager.CreateAsync(fournisseur, dto.Password);
            if (!result.Succeeded)
                throw new ApplicationException("Échec de la création du fournisseur : " + string.Join(", ", result.Errors.Select(e => e.Description)));

            await _userManager.AddToRoleAsync(fournisseur, "Fournisseur");

            // Créer l'adresse du fournisseur
            if (!string.IsNullOrEmpty(dto.Rue) && !string.IsNullOrEmpty(dto.Ville))
            {
                var adresse = new Adresse
                {
                    Rue = dto.Rue,
                    Ville = dto.Ville,
                    CodePostal = dto.CodePostal,
                    Pays = dto.Pays,
                    EstPrincipale = true,
                    UtilisateurId = fournisseur.Id
                };

                _context.Adresses.Add(adresse);
                await _context.SaveChangesAsync();
            }

            return _mapper.Map<FournisseurDto>(fournisseur);
        }

        public async Task<UtilisateurReadDto> CreerAdminAsync(AdminCreateDto dto)
        {
            // Vérifier si l'email existe déjà
            var existingUser = await _userManager.FindByEmailAsync(dto.Email);
            if (existingUser != null)
                throw new ArgumentException("Un utilisateur avec cet email existe déjà.");

            // Créer l'utilisateur admin
            var admin = new Admin
            {
                UserName = dto.Email,
                Email = dto.Email,
                Nom = dto.Nom,
                Prenom = dto.Prenom,
                PhoneNumber = dto.Telephone,
                EstActif = dto.EstActif,
                DateInscription = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(admin, dto.MotDePasse);
            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                throw new ArgumentException($"Erreur lors de la création de l'admin: {errors}");
            }

            // Assigner le rôle Admin
            await _userManager.AddToRoleAsync(admin, "Admin");

            return _mapper.Map<UtilisateurReadDto>(admin);
        }

        public async Task<UtilisateurReadDto> InitialiserAdminParDefautAsync()
        {
            // Vérifier si un admin existe déjà
            var existingAdmin = await _userManager.GetUsersInRoleAsync("Admin");
            if (existingAdmin.Any())
            {
                return _mapper.Map<UtilisateurReadDto>(existingAdmin.First());
            }

            // Créer l'admin par défaut
            var adminDto = new AdminCreateDto
            {
                Nom = "Administrateur",
                Prenom = "Système",
                Email = "<EMAIL>",
                MotDePasse = "Admin123!",
                Telephone = "+33123456789",
                EstActif = true
            };

            return await CreerAdminAsync(adminDto);
        }

        public async Task<string> GenerateJwtTokenAsync(string email)
        {
            var utilisateur = await _userManager.FindByEmailAsync(email);
            if (utilisateur == null)
                throw new ArgumentException("Utilisateur non trouvé");

            return await _tokenService.CreateTokenAsync(utilisateur);
        }

    }
}
