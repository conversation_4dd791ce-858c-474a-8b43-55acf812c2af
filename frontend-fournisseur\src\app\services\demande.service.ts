import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface DemandeCategorieDto {
  id: number;
  nom: string;
  description: string;
  imageUrl?: string;
  fournisseurId: number;
  fournisseurNom: string;
  statut: StatutDemande;
  dateDemande: Date;
  dateTraitement?: Date;
  adminTraitantId?: number;
  commentaireAdmin?: string;
  categorieCreeeId?: number;
  nomCategorieCreee?: string;
}

export interface CreateDemandeCategorieDto {
  nom: string;
  description: string;
}

export interface TraiterDemandeCategorieDto {
  statut: StatutDemande;
  commentaireAdmin?: string;
}

export interface DemandeSousCategorieDto {
  id: number;
  nom: string;
  description: string;
  imageUrl?: string;
  categorieId: number;
  categorieNom: string;
  fournisseurId: number;
  fournisseurNom: string;
  statut: StatutDemande;
  dateDemande: Date;
  dateTraitement?: Date;
  adminTraitantId?: number;
  commentaireAdmin?: string;
  sousCategorieCreeeId?: number;
}

export interface CreateDemandeSousCategorieDto {
  nom: string;
  description: string;
  categorieId: number;
}

export interface TraiterDemandeSousCategorieDto {
  statut: StatutDemande;
  commentaireAdmin?: string;
}

export enum StatutDemande {
  EnAttente = 0,
  Approuvee = 1,
  Rejetee = 2
}

@Injectable({
  providedIn: 'root'
})
export class DemandeService {
  private apiUrl = `${environment.apiUrl}/api`;

  constructor(private http: HttpClient) {}

  // Demandes de catégories
  getAllDemandesCategories(): Observable<DemandeCategorieDto[]> {
    return this.http.get<DemandeCategorieDto[]>(`${this.apiUrl}/DemandesCategories`);
  }

  getDemandesCategoriesByStatut(statut: StatutDemande): Observable<DemandeCategorieDto[]> {
    return this.http.get<DemandeCategorieDto[]>(`${this.apiUrl}/DemandesCategories/statut/${statut}`);
  }

  getMesDemandesCategories(): Observable<DemandeCategorieDto[]> {
    return this.http.get<DemandeCategorieDto[]>(`${this.apiUrl}/DemandesCategories/mes-demandes`);
  }

  getDemandeCategorie(id: number): Observable<DemandeCategorieDto> {
    return this.http.get<DemandeCategorieDto>(`${this.apiUrl}/DemandesCategories/${id}`);
  }

  createDemandeCategorie(demande: CreateDemandeCategorieDto): Observable<DemandeCategorieDto> {
    return this.http.post<DemandeCategorieDto>(`${this.apiUrl}/DemandesCategories`, demande);
  }

  traiterDemandeCategorie(id: number, traitement: TraiterDemandeCategorieDto): Observable<DemandeCategorieDto> {
    return this.http.put<DemandeCategorieDto>(`${this.apiUrl}/DemandesCategories/${id}/traiter`, traitement);
  }

  deleteDemandeCategorie(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/DemandesCategories/${id}`);
  }

  // Demandes de sous-catégories
  getAllDemandesSousCategories(): Observable<DemandeSousCategorieDto[]> {
    return this.http.get<DemandeSousCategorieDto[]>(`${this.apiUrl}/DemandesSousCategories`);
  }

  getDemandesSousCategoriesByStatut(statut: StatutDemande): Observable<DemandeSousCategorieDto[]> {
    return this.http.get<DemandeSousCategorieDto[]>(`${this.apiUrl}/DemandesSousCategories/statut/${statut}`);
  }

  getMesDemandesSousCategories(): Observable<DemandeSousCategorieDto[]> {
    return this.http.get<DemandeSousCategorieDto[]>(`${this.apiUrl}/DemandesSousCategories/mes-demandes`);
  }

  getDemandeSousCategorie(id: number): Observable<DemandeSousCategorieDto> {
    return this.http.get<DemandeSousCategorieDto>(`${this.apiUrl}/DemandesSousCategories/${id}`);
  }

  createDemandeSousCategorie(demande: CreateDemandeSousCategorieDto): Observable<DemandeSousCategorieDto> {
    return this.http.post<DemandeSousCategorieDto>(`${this.apiUrl}/DemandesSousCategories`, demande);
  }

  traiterDemandeSousCategorie(id: number, traitement: TraiterDemandeSousCategorieDto): Observable<DemandeSousCategorieDto> {
    return this.http.put<DemandeSousCategorieDto>(`${this.apiUrl}/DemandesSousCategories/${id}/traiter`, traitement);
  }

  deleteDemandeSousCategorie(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/DemandesSousCategories/${id}`);
  }

  // Méthodes utilitaires
  getStatutLabel(statut: StatutDemande): string {
    switch (statut) {
      case StatutDemande.EnAttente:
        return 'En attente';
      case StatutDemande.Approuvee:
        return 'Approuvée';
      case StatutDemande.Rejetee:
        return 'Rejetée';
      default:
        return 'Inconnu';
    }
  }

  getStatutColor(statut: StatutDemande): string {
    switch (statut) {
      case StatutDemande.EnAttente:
        return '#ff9800';
      case StatutDemande.Approuvee:
        return '#4caf50';
      case StatutDemande.Rejetee:
        return '#f44336';
      default:
        return '#666';
    }
  }
}
