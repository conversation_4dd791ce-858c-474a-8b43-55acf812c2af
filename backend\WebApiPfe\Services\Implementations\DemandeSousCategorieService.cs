using AutoMapper;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class DemandeSousCategorieService : IDemandeSousCategorieService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly INotificationService _notificationService;

        public DemandeSousCategorieService(AppDbContext context, IMapper mapper, INotificationService notificationService)
        {
            _context = context;
            _mapper = mapper;
            _notificationService = notificationService;
        }

        public async Task<IEnumerable<DemandeSousCategorieDto>> GetAllDemandesAsync()
        {
            var demandes = await _context.DemandesSousCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.Categorie)
                .Include(d => d.SousCategorieCreee)
                .OrderByDescending(d => d.DateDemande)
                .ToListAsync();

            return demandes.Select(d => new DemandeSousCategorieDto
            {
                Id = d.Id,
                Nom = d.Nom,
                Description = d.Description,
                CategorieId = d.CategorieId,
                NomCategorie = d.Categorie?.Nom ?? "Inconnue",
                FournisseurId = d.FournisseurId,
                NomFournisseur = d.Fournisseur?.UserName ?? "Inconnu",
                Statut = d.Statut,
                DateDemande = d.DateDemande,
                DateTraitement = d.DateTraitement,
                AdminTraitantId = d.AdminTraitantId,
                NomAdminTraitant = d.AdminTraitant?.UserName,
                CommentaireAdmin = d.CommentaireAdmin,
                SousCategorieCreeeId = d.SousCategorieCreeeId,
                NomSousCategorieCreee = d.SousCategorieCreee?.Nom
            });
        }

        public async Task<IEnumerable<DemandeSousCategorieDto>> GetDemandesByFournisseurAsync(int fournisseurId)
        {
            var demandes = await _context.DemandesSousCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.Categorie)
                .Include(d => d.SousCategorieCreee)
                .Where(d => d.FournisseurId == fournisseurId)
                .OrderByDescending(d => d.DateDemande)
                .ToListAsync();

            return demandes.Select(d => new DemandeSousCategorieDto
            {
                Id = d.Id,
                Nom = d.Nom,
                Description = d.Description,
                CategorieId = d.CategorieId,
                NomCategorie = d.Categorie?.Nom ?? "Inconnue",
                FournisseurId = d.FournisseurId,
                NomFournisseur = d.Fournisseur?.UserName ?? "Inconnu",
                Statut = d.Statut,
                DateDemande = d.DateDemande,
                DateTraitement = d.DateTraitement,
                AdminTraitantId = d.AdminTraitantId,
                NomAdminTraitant = d.AdminTraitant?.UserName,
                CommentaireAdmin = d.CommentaireAdmin,
                SousCategorieCreeeId = d.SousCategorieCreeeId,
                NomSousCategorieCreee = d.SousCategorieCreee?.Nom
            });
        }

        public async Task<IEnumerable<DemandeSousCategorieDto>> GetDemandesByStatutAsync(StatutDemande statut)
        {
            var demandes = await _context.DemandesSousCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.Categorie)
                .Include(d => d.SousCategorieCreee)
                .Where(d => d.Statut == statut)
                .OrderByDescending(d => d.DateDemande)
                .ToListAsync();

            return demandes.Select(d => new DemandeSousCategorieDto
            {
                Id = d.Id,
                Nom = d.Nom,
                Description = d.Description,
                CategorieId = d.CategorieId,
                NomCategorie = d.Categorie?.Nom ?? "Inconnue",
                FournisseurId = d.FournisseurId,
                NomFournisseur = d.Fournisseur?.UserName ?? "Inconnu",
                Statut = d.Statut,
                DateDemande = d.DateDemande,
                DateTraitement = d.DateTraitement,
                AdminTraitantId = d.AdminTraitantId,
                NomAdminTraitant = d.AdminTraitant?.UserName,
                CommentaireAdmin = d.CommentaireAdmin,
                SousCategorieCreeeId = d.SousCategorieCreeeId,
                NomSousCategorieCreee = d.SousCategorieCreee?.Nom
            });
        }

        public async Task<DemandeSousCategorieDto?> GetDemandeByIdAsync(int id)
        {
            var demande = await _context.DemandesSousCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.AdminTraitant)
                .Include(d => d.Categorie)
                .Include(d => d.SousCategorieCreee)
                .FirstOrDefaultAsync(d => d.Id == id);

            if (demande == null) return null;

            return new DemandeSousCategorieDto
            {
                Id = demande.Id,
                Nom = demande.Nom,
                Description = demande.Description,
                CategorieId = demande.CategorieId,
                NomCategorie = demande.Categorie?.Nom ?? "Inconnue",
                FournisseurId = demande.FournisseurId,
                NomFournisseur = demande.Fournisseur?.UserName ?? "Inconnu",
                Statut = demande.Statut,
                DateDemande = demande.DateDemande,
                DateTraitement = demande.DateTraitement,
                AdminTraitantId = demande.AdminTraitantId,
                NomAdminTraitant = demande.AdminTraitant?.UserName,
                CommentaireAdmin = demande.CommentaireAdmin,
                SousCategorieCreeeId = demande.SousCategorieCreeeId,
                NomSousCategorieCreee = demande.SousCategorieCreee?.Nom
            };
        }

        public async Task<DemandeSousCategorieDto> CreateDemandeAsync(CreateDemandeSousCategorieDto createDto, int fournisseurId)
        {
            // Vérifier que la catégorie existe
            var categorieExiste = await _context.Categories.AnyAsync(c => c.Id == createDto.CategorieId);
            if (!categorieExiste)
                throw new ArgumentException("Catégorie non trouvée");

            var demande = new DemandeSousCategorie
            {
                Nom = createDto.Nom,
                Description = createDto.Description,
                CategorieId = createDto.CategorieId,
                FournisseurId = fournisseurId,
                Statut = StatutDemande.EnAttente,
                DateDemande = DateTime.UtcNow
            };

            _context.DemandesSousCategories.Add(demande);
            await _context.SaveChangesAsync();

            // Envoyer notification aux admins
            var admins = await _context.Users.OfType<Admin>()
                .Where(a => a.RoleDiscriminator == RoleUtilisateur.Admin)
                .ToListAsync();

            var categorie = await _context.Categories.FindAsync(createDto.CategorieId);

            foreach (var admin in admins)
            {
                await _notificationService.CreateNotificationAsync(new WebApiPfe.DTOs.CreateNotificationDto
                {
                    UtilisateurId = admin.Id,
                    Titre = "Nouvelle demande de sous-catégorie",
                    Message = $"Le fournisseur a demandé la création de la sous-catégorie '{createDto.Nom}' dans la catégorie '{categorie?.Nom}'",
                    Type = TypeNotification.DemandeSousCategorie,

                });
            }

            return await GetDemandeByIdAsync(demande.Id) ?? throw new InvalidOperationException("Erreur lors de la création de la demande");
        }

        public async Task<DemandeSousCategorieDto> TraiterDemandeAsync(int id, TraiterDemandeSousCategorieDto traiterDto, int adminId)
        {
            var demande = await _context.DemandesSousCategories
                .Include(d => d.Fournisseur)
                .Include(d => d.Categorie)
                .FirstOrDefaultAsync(d => d.Id == id);

            if (demande == null)
                throw new ArgumentException("Demande non trouvée");

            if (demande.Statut != StatutDemande.EnAttente)
                throw new InvalidOperationException("Cette demande a déjà été traitée");

            demande.Statut = traiterDto.Statut;
            demande.DateTraitement = DateTime.UtcNow;
            demande.AdminTraitantId = adminId;
            demande.CommentaireAdmin = traiterDto.CommentaireAdmin;

            // Si approuvée, créer la sous-catégorie
            if (traiterDto.Statut == StatutDemande.Approuvee)
            {
                var nouvelleSousCategorie = new SousCategorie
                {
                    Nom = demande.Nom,
                    Description = demande.Description,
                    CategorieId = demande.CategorieId,
                    EstValidee = true
                };

                _context.SousCategories.Add(nouvelleSousCategorie);
                await _context.SaveChangesAsync();

                demande.SousCategorieCreeeId = nouvelleSousCategorie.Id;
            }

            await _context.SaveChangesAsync();

            // Envoyer notification au fournisseur
            var messageNotification = traiterDto.Statut == StatutDemande.Approuvee
                ? $"Votre demande de sous-catégorie '{demande.Nom}' a été approuvée"
                : $"Votre demande de sous-catégorie '{demande.Nom}' a été rejetée";

            if (!string.IsNullOrEmpty(traiterDto.CommentaireAdmin))
            {
                messageNotification += $". Commentaire: {traiterDto.CommentaireAdmin}";
            }

            await _notificationService.CreateNotificationAsync(new WebApiPfe.DTOs.CreateNotificationDto
            {
                UtilisateurId = demande.FournisseurId,
                Titre = "Réponse à votre demande de sous-catégorie",
                Message = messageNotification,
                Type = TypeNotification.ReponseDemandeSousCategorie,

            });

            return await GetDemandeByIdAsync(id) ?? throw new InvalidOperationException("Erreur lors du traitement de la demande");
        }

        public async Task<bool> DeleteDemandeAsync(int id)
        {
            var demande = await _context.DemandesSousCategories.FindAsync(id);
            if (demande == null) return false;

            _context.DemandesSousCategories.Remove(demande);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
