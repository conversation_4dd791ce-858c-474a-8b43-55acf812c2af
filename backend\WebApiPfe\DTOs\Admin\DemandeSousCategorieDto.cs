using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs.Admin
{
    public class DemandeSousCategorieDto
    {
        public int Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int CategorieId { get; set; }
        public string NomCategorie { get; set; } = string.Empty;
        public int FournisseurId { get; set; }
        public string NomFournisseur { get; set; } = string.Empty;
        public StatutDemande Statut { get; set; }
        public DateTime DateDemande { get; set; }
        public DateTime? DateTraitement { get; set; }
        public int? AdminTraitantId { get; set; }
        public string? NomAdminTraitant { get; set; }
        public string? CommentaireAdmin { get; set; }
        public int? SousCategorieCreeeId { get; set; }
        public string? NomSousCategorieCreee { get; set; }
    }

    public class CreateDemandeSousCategorieDto
    {
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int CategorieId { get; set; }
    }

    public class TraiterDemandeSousCategorieDto
    {
        public StatutDemande Statut { get; set; }
        public string? CommentaireAdmin { get; set; }
    }
}
