@import '../../../../styles.scss';

.avis-moderation-container {
  padding: 1.5rem;
  
  .header-section {
    margin-bottom: 2rem;
    
    h2 {
      color: $primary-color;
      font-weight: 600;
      margin-bottom: 0.5rem;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .filters-section {
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    
    .card-body {
      padding: 1.5rem;
    }
    
    .form-label {
      font-weight: 500;
      color: $text-color;
      margin-bottom: 0.5rem;
    }
    
    .form-select,
    .form-control {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
  }
  
  .avis-list {
    .card {
      border: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 8px;
      
      .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        padding: 1rem 1.5rem;
        
        h5 {
          color: $text-color;
          font-weight: 600;
        }
        
        .btn-group {
          .btn {
            border-color: #e0e0e0;
            
            &.active {
              background-color: $primary-color;
              border-color: $primary-color;
              color: white;
            }
            
            &:hover:not(.active) {
              background-color: #f8f9fa;
            }
          }
        }
      }
    }
    
    .avis-item {
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #f8f9fa;
      }
      
      &:last-child {
        border-bottom: none !important;
      }
      
      .product-info {
        h6 {
          color: $text-color;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }
      }
      
      .client-info {
        padding-top: 0.5rem;
        border-top: 1px solid #f0f0f0;
      }
      
      .rating {
        .bi-star-fill {
          color: #ffc107 !important;
        }
        
        .bi-star {
          color: #e0e0e0 !important;
        }
      }
      
      .badge {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        
        i {
          margin-right: 0.25rem;
        }
        
        &.bg-warning {
          background-color: #ffc107 !important;
          color: #000;
        }
        
        &.bg-success {
          background-color: #28a745 !important;
        }
        
        &.bg-danger {
          background-color: #dc3545 !important;
        }
        
        &.bg-info {
          background-color: #17a2b8 !important;
        }
      }
      
      .btn-outline-primary {
        border-color: $primary-color;
        color: $primary-color;
        
        &:hover {
          background-color: $primary-color;
          border-color: $primary-color;
        }
      }
    }
  }
  
  .pagination-section {
    .pagination {
      .page-link {
        color: $primary-color;
        border-color: #e0e0e0;
        
        &:hover {
          background-color: #f8f9fa;
          border-color: $primary-color;
        }
      }
      
      .page-item.active .page-link {
        background-color: $primary-color;
        border-color: $primary-color;
      }
      
      .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #e0e0e0;
      }
    }
  }
  
  .alert {
    border: none;
    border-radius: 6px;
    
    &.alert-danger {
      background-color: #f8d7da;
      color: #721c24;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

// Modal styles
.modal {
  .modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  }
  
  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    
    .modal-title {
      color: $text-color;
      font-weight: 600;
      
      i {
        margin-right: 0.5rem;
        color: $primary-color;
      }
    }
  }
  
  .modal-body {
    padding: 1.5rem;
    
    .avis-details {
      h6 {
        color: $text-color;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
      
      .rating {
        .bi-star-fill {
          color: #ffc107 !important;
        }
        
        .bi-star {
          color: #e0e0e0 !important;
        }
      }
      
      .bg-light {
        background-color: #f8f9fa !important;
      }
    }
    
    .form-label {
      font-weight: 500;
      color: $text-color;
    }
    
    .form-select,
    .form-control {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
  }
  
  .modal-footer {
    border-top: 1px solid #e0e0e0;
    padding: 1rem 1.5rem;
    
    .btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.btn-primary {
        background-color: $primary-color;
        border-color: $primary-color;
        
        &:hover {
          background-color: darken($primary-color, 10%);
          border-color: darken($primary-color, 10%);
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .avis-moderation-container {
    padding: 1rem;
    
    .filters-section .card-body {
      padding: 1rem;
    }
    
    .avis-item {
      .row {
        flex-direction: column;
      }
      
      .col-md-3 {
        text-align: left !important;
        margin-top: 1rem;
      }
    }
  }
  
  .modal-dialog {
    margin: 1rem;
  }
}
