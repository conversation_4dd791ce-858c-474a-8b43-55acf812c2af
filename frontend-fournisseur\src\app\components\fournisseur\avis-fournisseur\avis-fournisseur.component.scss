@import '../../../../styles.scss';

.avis-fournisseur-container {
  padding: 1.5rem;
  
  .header-section {
    margin-bottom: 2rem;
    
    h2 {
      color: $primary-color;
      font-weight: 600;
      margin-bottom: 0.5rem;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .stats-section {
    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border: 1px solid #e0e0e0;
      height: 100%;
      display: flex;
      align-items: center;
      
      .stat-icon {
        font-size: 2rem;
        color: $primary-color;
        margin-right: 1rem;
        
        &.text-warning {
          color: #ffc107 !important;
        }
        
        &.text-success {
          color: #28a745 !important;
        }
        
        &.text-primary {
          color: $primary-color !important;
        }
      }
      
      .stat-content {
        h3 {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 0.25rem;
          color: $text-color;
        }
        
        p {
          margin: 0;
          color: #6c757d;
          font-size: 0.9rem;
        }
      }
    }
    
    .rating-distribution {
      .card {
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        
        .card-header {
          background-color: #f8f9fa;
          border-bottom: 1px solid #e0e0e0;
          
          h5 {
            color: $text-color;
            font-weight: 600;
            margin: 0;
            
            i {
              margin-right: 0.5rem;
              color: $primary-color;
            }
          }
        }
        
        .rating-row {
          display: flex;
          align-items: center;
          padding: 0.75rem 0;
          
          &:not(:last-child) {
            border-bottom: 1px solid #f0f0f0;
          }
          
          .rating-label {
            width: 60px;
            display: flex;
            align-items: center;
            font-weight: 500;
            
            span {
              margin-right: 0.25rem;
            }
            
            i {
              font-size: 0.8rem;
            }
          }
          
          .rating-bar {
            flex: 1;
            margin: 0 1rem;
            
            .progress {
              height: 8px;
              background-color: #f0f0f0;
              
              .progress-bar {
                transition: width 0.3s ease;
              }
            }
          }
          
          .rating-count {
            width: 80px;
            text-align: right;
            font-size: 0.9rem;
            color: #6c757d;
          }
        }
      }
    }
  }
  
  .filters-section {
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    
    .card-body {
      padding: 1.5rem;
    }
    
    .form-label {
      font-weight: 500;
      color: $text-color;
      margin-bottom: 0.5rem;
    }
    
    .form-select,
    .form-control {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
  }
  
  .avis-list {
    .card {
      border: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 8px;
      
      .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        padding: 1rem 1.5rem;
        
        h5 {
          color: $text-color;
          font-weight: 600;
        }
        
        .btn-group {
          .btn {
            border-color: #e0e0e0;
            
            &.active {
              background-color: $primary-color;
              border-color: $primary-color;
              color: white;
            }
            
            &:hover:not(.active) {
              background-color: #f8f9fa;
            }
          }
        }
      }
    }
    
    .avis-item {
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #f8f9fa;
      }
      
      &:last-child {
        border-bottom: none !important;
      }
      
      .product-info {
        h6 {
          color: $text-color;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }
      }
      
      .client-info {
        padding-top: 0.5rem;
        border-top: 1px solid #f0f0f0;
      }
      
      .rating {
        .bi-star-fill {
          color: #ffc107 !important;
        }
        
        .bi-star {
          color: #e0e0e0 !important;
        }
      }
      
      .badge {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        
        i {
          margin-right: 0.25rem;
        }
        
        &.bg-warning {
          background-color: #ffc107 !important;
          color: #000;
        }
        
        &.bg-success {
          background-color: #28a745 !important;
        }
        
        &.bg-danger {
          background-color: #dc3545 !important;
        }
        
        &.bg-info {
          background-color: #17a2b8 !important;
        }
      }
      
      .btn-outline-primary {
        border-color: $primary-color;
        color: $primary-color;
        
        &:hover {
          background-color: $primary-color;
          border-color: $primary-color;
        }
      }
      
      .bg-light {
        background-color: #f8f9fa !important;
        border: 1px solid #e9ecef;
      }
    }
  }
  
  .pagination-section {
    .pagination {
      .page-link {
        color: $primary-color;
        border-color: #e0e0e0;
        
        &:hover {
          background-color: #f8f9fa;
          border-color: $primary-color;
        }
      }
      
      .page-item.active .page-link {
        background-color: $primary-color;
        border-color: $primary-color;
      }
      
      .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #e0e0e0;
      }
    }
  }
  
  .alert {
    border: none;
    border-radius: 6px;
    
    &.alert-danger {
      background-color: #f8d7da;
      color: #721c24;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

// Modal styles
.modal {
  .modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  }
  
  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    
    .modal-title {
      color: $text-color;
      font-weight: 600;
      
      i {
        margin-right: 0.5rem;
        color: $primary-color;
      }
    }
  }
  
  .modal-body {
    padding: 1.5rem;
    
    .avis-details {
      h6 {
        color: $text-color;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
      
      .rating {
        .bi-star-fill {
          color: #ffc107 !important;
        }
        
        .bi-star {
          color: #e0e0e0 !important;
        }
      }
      
      .bg-light {
        background-color: #f8f9fa !important;
      }
    }
    
    .form-label {
      font-weight: 500;
      color: $text-color;
    }
    
    .form-control {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
    
    .form-text {
      color: #6c757d;
      font-size: 0.875rem;
    }
  }
  
  .modal-footer {
    border-top: 1px solid #e0e0e0;
    padding: 1rem 1.5rem;
    
    .btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.btn-primary {
        background-color: $primary-color;
        border-color: $primary-color;
        
        &:hover:not(:disabled) {
          background-color: darken($primary-color, 10%);
          border-color: darken($primary-color, 10%);
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .avis-fournisseur-container {
    padding: 1rem;
    
    .stats-section {
      .stat-card {
        margin-bottom: 1rem;
      }
    }
    
    .filters-section .card-body {
      padding: 1rem;
    }
    
    .avis-item {
      .row {
        flex-direction: column;
      }
      
      .col-md-3 {
        text-align: left !important;
        margin-top: 1rem;
      }
    }
  }
  
  .modal-dialog {
    margin: 1rem;
  }
}
