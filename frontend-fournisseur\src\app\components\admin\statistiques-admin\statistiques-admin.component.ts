import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  StatistiquesService, 
  StatistiquesGeneralesDto, 
  StatistiquesFilterDto,
  TopProduitDto,
  VentesParPeriodeDto,
  CommandesParJourDto
} from '../../../services/statistiques.service';

@Component({
  selector: 'app-statistiques-admin',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './statistiques-admin.component.html',
  styleUrls: ['./statistiques-admin.component.scss']
})
export class StatistiquesAdminComponent implements OnInit {
  stats: StatistiquesGeneralesDto | null = null;
  topProduits: TopProduitDto[] = [];
  evolutionVentes: VentesParPeriodeDto[] = [];
  evolutionCommandes: CommandesParJourDto[] = [];
  
  loading = false;
  error: string | null = null;

  // Filtres
  filter: StatistiquesFilterDto = {
    periode: 'mois'
  };

  // Périodes prédéfinies
  periodesPredefinies = [
    { label: 'Cette semaine', value: 'semaine' },
    { label: 'Ce mois', value: 'mois' },
    { label: 'Cette année', value: 'annee' }
  ];

  // Onglets
  activeTab = 'overview'; // 'overview', 'ventes', 'produits', 'clients'

  constructor(private statistiquesService: StatistiquesService) { }

  ngOnInit(): void {
    this.loadStatistiques();
  }

  loadStatistiques(): void {
    this.loading = true;
    this.error = null;

    // Définir les dates selon la période sélectionnée
    this.setDatesFromPeriode();

    this.statistiquesService.getStatistiquesGenerales(this.filter).subscribe({
      next: (data: StatistiquesGeneralesDto) => {
        this.stats = data;
        this.loading = false;
        this.loadComplementaryData();
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des statistiques:', error);
        this.error = 'Erreur lors du chargement des statistiques';
        this.loading = false;
      }
    });
  }

  private loadComplementaryData(): void {
    // Charger les données complémentaires
    this.loadTopProduits();
    this.loadEvolutionVentes();
    this.loadEvolutionCommandes();
  }

  private loadTopProduits(): void {
    this.statistiquesService.getTopProduits(10, this.filter).subscribe({
      next: (data: TopProduitDto[]) => {
        this.topProduits = data;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des top produits:', error);
      }
    });
  }

  private loadEvolutionVentes(): void {
    this.statistiquesService.getEvolutionVentes(this.filter).subscribe({
      next: (data: VentesParPeriodeDto[]) => {
        this.evolutionVentes = data;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement de l\'évolution des ventes:', error);
      }
    });
  }

  private loadEvolutionCommandes(): void {
    this.statistiquesService.getEvolutionCommandes(this.filter).subscribe({
      next: (data: CommandesParJourDto[]) => {
        this.evolutionCommandes = data;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement de l\'évolution des commandes:', error);
      }
    });
  }

  private setDatesFromPeriode(): void {
    const now = new Date();
    
    switch (this.filter.periode) {
      case 'semaine':
        const periodesSemaine = this.statistiquesService.getPeriodesSemaine();
        this.filter.dateDebut = periodesSemaine.dateDebut;
        this.filter.dateFin = periodesSemaine.dateFin;
        break;
        
      case 'mois':
        const periodesMois = this.statistiquesService.getPeriodesMois();
        this.filter.dateDebut = periodesMois.dateDebut;
        this.filter.dateFin = periodesMois.dateFin;
        break;
        
      case 'annee':
        const periodesAnnee = this.statistiquesService.getPeriodesAnnee();
        this.filter.dateDebut = periodesAnnee.dateDebut;
        this.filter.dateFin = periodesAnnee.dateFin;
        break;
    }
  }

  onPeriodeChange(): void {
    this.loadStatistiques();
  }

  onTabChange(tab: string): void {
    this.activeTab = tab;
  }

  onFilterChange(): void {
    this.loadStatistiques();
  }

  resetFilters(): void {
    this.filter = {
      periode: 'mois'
    };
    this.loadStatistiques();
  }

  exportStatistiques(format: 'pdf' | 'excel' | 'csv'): void {
    const exportDto = {
      format,
      donnees: 'complet' as const,
      periode: this.filter
    };

    this.statistiquesService.exportStatistiques(exportDto).subscribe({
      next: (blob: Blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `statistiques-${format}-${new Date().toISOString().split('T')[0]}.${format}`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error: any) => {
        console.error('Erreur lors de l\'export:', error);
        this.error = 'Erreur lors de l\'export des statistiques';
      }
    });
  }

  // Méthodes utilitaires
  formatMontant(montant: number): string {
    return this.statistiquesService.formatMontant(montant);
  }

  formatNombre(nombre: number): string {
    return this.statistiquesService.formatNombre(nombre);
  }

  formatEvolution(evolution: number): string {
    return this.statistiquesService.formatEvolution(evolution);
  }

  getEvolutionColor(evolution: number): string {
    return this.statistiquesService.getEvolutionColor(evolution);
  }

  getEvolutionIcon(evolution: number): string {
    return this.statistiquesService.getEvolutionIcon(evolution);
  }

  // Calculs pour les graphiques
  getMaxVentesValue(): number {
    if (this.evolutionVentes.length === 0) return 0;
    return Math.max(...this.evolutionVentes.map(v => v.montant));
  }

  getMaxCommandesValue(): number {
    if (this.evolutionCommandes.length === 0) return 0;
    return Math.max(...this.evolutionCommandes.map(c => c.nombreCommandes));
  }

  getVentesPercentage(montant: number): number {
    const max = this.getMaxVentesValue();
    return max > 0 ? (montant / max) * 100 : 0;
  }

  getCommandesPercentage(nombre: number): number {
    const max = this.getMaxCommandesValue();
    return max > 0 ? (nombre / max) * 100 : 0;
  }

  // Calculs de moyennes
  getPanierMoyenEvolution(): number {
    if (!this.stats || this.stats.totalCommandes === 0) return 0;
    return this.stats.totalVentes / this.stats.totalCommandes;
  }

  getTauxConversion(): number {
    // Simulation - à adapter selon vos métriques réelles
    return 2.5;
  }

  getClientsActifs(): number {
    // Simulation - à adapter selon vos métriques réelles
    return Math.round((this.stats?.totalClients || 0) * 0.7);
  }

  // Formatage des dates pour les graphiques
  formatPeriodeLabel(periode: string): string {
    const date = new Date(periode);
    
    switch (this.filter.periode) {
      case 'semaine':
      case 'jour':
        return date.toLocaleDateString('fr-FR', { day: '2-digit', month: 'short' });
      case 'mois':
        return date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });
      case 'annee':
        return date.getFullYear().toString();
      default:
        return periode;
    }
  }
}
