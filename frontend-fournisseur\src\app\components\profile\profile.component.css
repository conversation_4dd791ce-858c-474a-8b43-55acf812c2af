/* 🎨 CSS Créatif Angular 19 - Profil Fournisseur Moderne */

.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.98) 0%, 
    rgba(248, 250, 252, 0.95) 100%);
  min-height: 100vh;
}

/* 🎯 Header Moderne */
.profile-header {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.08) 0%, 
    rgba(99, 102, 241, 0.05) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.profile-title {
  margin: 0;
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));
}

.profile-subtitle {
  margin: 0.5rem 0 0 0;
  color: #64748b;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.edit-actions {
  display: flex;
  gap: 1rem;
}

/* 📝 Formulaire Élégant */
.profile-content {
  animation: fadeInUp 0.6s ease forwards;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.form-section {
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 0.8) 0%, 
    rgba(255, 255, 255, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  padding: 2rem;
  position: relative;
  transition: all 0.3s ease;
}

.form-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.section-title {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3));
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.form-group label {
  font-size: 0.95rem;
  font-weight: 600;
  color: #374151;
  position: relative;
  padding-left: 1rem;
}

.form-group label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  border-radius: 50%;
  opacity: 0.7;
}

.form-control {
  padding: 1rem 1.25rem;
  border: 2px solid rgba(203, 213, 225, 0.6);
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 500;
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 0.9) 0%, 
    rgba(255, 255, 255, 0.8) 100%);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.05);
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.98) 0%, 
    rgba(248, 250, 252, 0.95) 100%);
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.15),
    0 8px 16px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-control[readonly] {
  background: rgba(248, 250, 252, 0.6);
  color: #64748b;
  cursor: not-allowed;
}

.form-control.is-invalid {
  border-color: #ef4444;
  background: rgba(254, 242, 242, 0.8);
}

.invalid-feedback {
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.invalid-feedback::before {
  content: '⚠️';
  font-size: 0.875rem;
}

.form-hint {
  color: #64748b;
  font-size: 0.8125rem;
  font-style: italic;
  margin-top: 0.25rem;
}

/* 💰 Input Groups */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-suffix {
  position: absolute;
  right: 1rem;
  color: #64748b;
  font-size: 0.95rem;
  font-weight: 600;
  pointer-events: none;
}

/* 📊 Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-item span {
  font-size: 0.95rem;
  font-weight: 500;
  color: #374151;
}

/* 🏷️ Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge.status-active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.badge.status-inactive {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.badge.badge-role {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

/* 🎯 Boutons */
.btn {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn:hover:not(:disabled)::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* 🔄 Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner.large {
  width: 40px;
  height: 40px;
  border-width: 4px;
  border-color: rgba(59, 130, 246, 0.3);
  border-top-color: #3b82f6;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🚨 Alertes */
.alert {
  padding: 1rem 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.alert-error {
  background: linear-gradient(135deg, rgba(254, 242, 242, 0.9), rgba(252, 231, 243, 0.8));
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
}

.alert-success {
  background: linear-gradient(135deg, rgba(236, 253, 245, 0.9), rgba(240, 253, 244, 0.8));
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #059669;
}

/* 📱 Responsive */
@media (max-width: 768px) {
  .profile-container {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .profile-title {
    font-size: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-section {
    padding: 1.5rem;
  }

  .edit-actions {
    flex-direction: column;
    width: 100%;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

/* ✨ Animations */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🔄 Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  text-align: center;
  color: #64748b;
}

.loading-content p {
  margin-top: 1rem;
  font-size: 1.1rem;
}

/* 📍 Styles pour les adresses */
.address-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.address-item:hover {
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.address-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.address-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.address-text {
  color: #374151;
  font-weight: 500;
}

.no-address {
  color: #9ca3af;
  font-style: italic;
}

.address-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.address-edit {
  background: rgba(249, 250, 251, 0.8);
  border-radius: 12px;
  padding: 1rem;
  margin-top: 1rem;
}

.no-addresses {
  text-align: center;
  padding: 2rem;
  background: rgba(249, 250, 251, 0.5);
  border: 2px dashed rgba(209, 213, 219, 0.8);
  border-radius: 16px;
  margin-top: 1rem;
}

.badge-primary {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Responsive pour les adresses */
@media (max-width: 768px) {
  .address-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .address-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .address-edit .row {
    margin: 0;
  }

  .address-edit .col-md-6,
  .address-edit .col-md-12 {
    padding: 0 0.5rem;
  }
}
