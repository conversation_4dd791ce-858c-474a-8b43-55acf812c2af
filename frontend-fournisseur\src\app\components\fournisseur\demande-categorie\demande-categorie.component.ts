import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DemandeService, CreateDemandeCategorieDto, CreateDemandeSousCategorieDto, DemandeCategorieDto, DemandeSousCategorieDto, StatutDemande } from '../../../services/demande.service';
import { CategorieService } from '../../../services/categorie.service';
import { Categorie } from '../../../models/categorie.model';

@Component({
  selector: 'app-demande-categorie',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  template: `
    <div class="demande-categorie">
      <div class="page-header">
        <h1>📝 Demandes de Catégories et Sous-catégories</h1>
        <p>Demandez la création de nouvelles catégories ou sous-catégories</p>
      </div>

      <div class="tabs">
        <button 
          class="tab-btn" 
          [class.active]="activeTab === 'nouvelle-demande'"
          (click)="activeTab = 'nouvelle-demande'">
          ➕ Nouvelle Demande
        </button>
        <button 
          class="tab-btn" 
          [class.active]="activeTab === 'mes-demandes'"
          (click)="activeTab = 'mes-demandes'; loadMesDemandesCategories(); loadMesDemandesSousCategories()">
          📋 Mes Demandes
          <span *ngIf="totalDemandes > 0" class="badge">{{ totalDemandes }}</span>
        </button>
      </div>

      <!-- Nouvelle demande -->
      <div *ngIf="activeTab === 'nouvelle-demande'" class="tab-content">
        <div class="form-tabs">
          <button 
            class="form-tab-btn" 
            [class.active]="formType === 'categorie'"
            (click)="formType = 'categorie'">
            🏷️ Demander une Catégorie
          </button>
          <button 
            class="form-tab-btn" 
            [class.active]="formType === 'sous-categorie'"
            (click)="formType = 'sous-categorie'; loadCategories()">
            🏷️ Demander une Sous-catégorie
          </button>
        </div>

        <!-- Formulaire catégorie -->
        <div *ngIf="formType === 'categorie'" class="form-container">
          <form [formGroup]="categorieForm" (ngSubmit)="submitDemandeCategorie()">
            <div class="form-group">
              <label for="nom">Nom de la catégorie *</label>
              <input 
                type="text" 
                id="nom" 
                formControlName="nom" 
                placeholder="Ex: Lunettes de soleil premium"
                maxlength="100">
              <div *ngIf="categorieForm.get('nom')?.invalid && categorieForm.get('nom')?.touched" class="error">
                Le nom est requis (max 100 caractères)
              </div>
            </div>

            <div class="form-group">
              <label for="description">Description</label>
              <textarea 
                id="description" 
                formControlName="description" 
                placeholder="Décrivez la catégorie que vous souhaitez créer..."
                rows="4"
                maxlength="500">
              </textarea>
              <div class="char-count">{{ categorieForm.get('description')?.value?.length || 0 }}/500</div>
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" (click)="resetCategorieForm()">
                Annuler
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="categorieForm.invalid || isSubmitting">
                <span *ngIf="isSubmitting">⏳ Envoi...</span>
                <span *ngIf="!isSubmitting">📤 Envoyer la demande</span>
              </button>
            </div>
          </form>
        </div>

        <!-- Formulaire sous-catégorie -->
        <div *ngIf="formType === 'sous-categorie'" class="form-container">
          <form [formGroup]="sousCategorieForm" (ngSubmit)="submitDemandeSousCategorie()">
            <div class="form-group">
              <label for="categorieId">Catégorie parent *</label>
              <select id="categorieId" formControlName="categorieId">
                <option value="">Sélectionnez une catégorie</option>
                <option *ngFor="let categorie of categories" [value]="categorie.id">
                  {{ categorie.nom }}
                </option>
              </select>
              <div *ngIf="sousCategorieForm.get('categorieId')?.invalid && sousCategorieForm.get('categorieId')?.touched" class="error">
                Veuillez sélectionner une catégorie parent
              </div>
            </div>

            <div class="form-group">
              <label for="nomSous">Nom de la sous-catégorie *</label>
              <input 
                type="text" 
                id="nomSous" 
                formControlName="nom" 
                placeholder="Ex: Lunettes polarisées"
                maxlength="100">
              <div *ngIf="sousCategorieForm.get('nom')?.invalid && sousCategorieForm.get('nom')?.touched" class="error">
                Le nom est requis (max 100 caractères)
              </div>
            </div>

            <div class="form-group">
              <label for="descriptionSous">Description</label>
              <textarea 
                id="descriptionSous" 
                formControlName="description" 
                placeholder="Décrivez la sous-catégorie que vous souhaitez créer..."
                rows="4"
                maxlength="500">
              </textarea>
              <div class="char-count">{{ sousCategorieForm.get('description')?.value?.length || 0 }}/500</div>
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" (click)="resetSousCategorieForm()">
                Annuler
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="sousCategorieForm.invalid || isSubmitting">
                <span *ngIf="isSubmitting">⏳ Envoi...</span>
                <span *ngIf="!isSubmitting">📤 Envoyer la demande</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Mes demandes -->
      <div *ngIf="activeTab === 'mes-demandes'" class="tab-content">
        <div class="demandes-section">
          <h3>🏷️ Mes demandes de catégories</h3>
          <div *ngIf="mesDemandesCategories.length === 0" class="empty-state">
            <p>Aucune demande de catégorie</p>
          </div>
          <div class="demandes-grid">
            <div *ngFor="let demande of mesDemandesCategories" class="demande-card">
              <div class="demande-header">
                <h4>{{ demande.nom }}</h4>
                <span class="statut-badge" [style.background-color]="getStatutColor(demande.statut)">
                  {{ getStatutLabel(demande.statut) }}
                </span>
              </div>
              <div class="demande-content">
                <p *ngIf="demande.description"><strong>Description:</strong> {{ demande.description }}</p>
                <p><strong>Demandé le:</strong> {{ formatDate(demande.dateDemande) }}</p>
                <div *ngIf="demande.statut !== 0 && demande.dateTraitement" class="traitement-info">
                  <p><strong>Traité le:</strong> {{ formatDate(demande.dateTraitement) }}</p>
                  <p *ngIf="demande.commentaireAdmin"><strong>Commentaire:</strong> {{ demande.commentaireAdmin }}</p>
                  <p *ngIf="demande.statut === 1 && demande.nomCategorieCreee">
                    <strong>Catégorie créée:</strong> {{ demande.nomCategorieCreee }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="demandes-section">
          <h3>🏷️ Mes demandes de sous-catégories</h3>
          <div *ngIf="mesDemandesSousCategories.length === 0" class="empty-state">
            <p>Aucune demande de sous-catégorie</p>
          </div>
          <div class="demandes-grid">
            <div *ngFor="let demande of mesDemandesSousCategories" class="demande-card">
              <div class="demande-header">
                <h4>{{ demande.nom }}</h4>
                <span class="statut-badge" [style.background-color]="getStatutColor(demande.statut)">
                  {{ getStatutLabel(demande.statut) }}
                </span>
              </div>
              <div class="demande-content">
                <p><strong>Catégorie parent:</strong> {{ demande.categorieNom }}</p>
                <p *ngIf="demande.description"><strong>Description:</strong> {{ demande.description }}</p>
                <p><strong>Demandé le:</strong> {{ formatDate(demande.dateDemande) }}</p>
                <div *ngIf="demande.statut !== 0 && demande.dateTraitement" class="traitement-info">
                  <p><strong>Traité le:</strong> {{ formatDate(demande.dateTraitement) }}</p>
                  <p *ngIf="demande.commentaireAdmin"><strong>Commentaire:</strong> {{ demande.commentaireAdmin }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Messages de succès/erreur -->
      <div *ngIf="successMessage" class="alert alert-success">
        {{ successMessage }}
      </div>
      <div *ngIf="errorMessage" class="alert alert-error">
        {{ errorMessage }}
      </div>
    </div>
  `,
  styles: [`
    .demande-categorie {
      padding: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      margin-bottom: 2rem;
    }

    .page-header h1 {
      margin: 0 0 0.5rem 0;
      color: #1e293b;
    }

    .page-header p {
      margin: 0;
      color: #64748b;
    }

    .tabs {
      display: flex;
      border-bottom: 2px solid #e2e8f0;
      margin-bottom: 2rem;
    }

    .tab-btn {
      padding: 1rem 2rem;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 1rem;
      color: #64748b;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
      position: relative;
    }

    .tab-btn.active {
      color: #3b82f6;
      border-bottom-color: #3b82f6;
    }

    .badge {
      background: #ef4444;
      color: white;
      border-radius: 50%;
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      margin-left: 0.5rem;
    }

    .form-tabs {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .form-tab-btn {
      padding: 0.75rem 1.5rem;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .form-tab-btn.active {
      background: #3b82f6;
      color: white;
      border-color: #3b82f6;
    }

    .form-container {
      background: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #374151;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.3s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .char-count {
      text-align: right;
      font-size: 0.875rem;
      color: #6b7280;
      margin-top: 0.25rem;
    }

    .error {
      color: #ef4444;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 2rem;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #2563eb;
    }

    .btn-primary:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }

    .btn-secondary {
      background: #6b7280;
      color: white;
    }

    .btn-secondary:hover {
      background: #4b5563;
    }

    .demandes-section {
      margin-bottom: 3rem;
    }

    .demandes-section h3 {
      margin-bottom: 1rem;
      color: #1e293b;
    }

    .empty-state {
      text-align: center;
      padding: 2rem;
      color: #64748b;
      background: #f8fafc;
      border-radius: 8px;
      margin-bottom: 2rem;
    }

    .demandes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 1.5rem;
    }

    .demande-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
    }

    .demande-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .demande-header h4 {
      margin: 0;
      color: #1e293b;
    }

    .statut-badge {
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      color: white;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .demande-content p {
      margin: 0.5rem 0;
      color: #475569;
    }

    .traitement-info {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #e2e8f0;
      background: #f8fafc;
      padding: 1rem;
      border-radius: 8px;
    }

    .alert {
      padding: 1rem;
      border-radius: 8px;
      margin-top: 1rem;
    }

    .alert-success {
      background: #dcfce7;
      color: #166534;
      border: 1px solid #bbf7d0;
    }

    .alert-error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
  `]
})
export class DemandeCategorieComponent implements OnInit {
  activeTab: 'nouvelle-demande' | 'mes-demandes' = 'nouvelle-demande';
  formType: 'categorie' | 'sous-categorie' = 'categorie';
  
  categorieForm: FormGroup;
  sousCategorieForm: FormGroup;
  
  categories: Categorie[] = [];
  mesDemandesCategories: DemandeCategorieDto[] = [];
  mesDemandesSousCategories: DemandeSousCategorieDto[] = [];
  
  isSubmitting = false;
  successMessage = '';
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private demandeService: DemandeService,
    private categorieService: CategorieService
  ) {
    this.categorieForm = this.fb.group({
      nom: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]]
    });

    this.sousCategorieForm = this.fb.group({
      categorieId: ['', [Validators.required]],
      nom: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]]
    });
  }

  ngOnInit() {
    // Initialisation
  }

  get totalDemandes(): number {
    return this.mesDemandesCategories.length + this.mesDemandesSousCategories.length;
  }

  loadCategories() {
    this.categorieService.getAll().subscribe({
      next: (categories: Categorie[]) => {
        this.categories = categories;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des catégories:', error);
      }
    });
  }

  loadMesDemandesCategories() {
    this.demandeService.getMesDemandesCategories().subscribe({
      next: (demandes) => {
        this.mesDemandesCategories = demandes;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des demandes de catégories:', error);
      }
    });
  }

  loadMesDemandesSousCategories() {
    this.demandeService.getMesDemandesSousCategories().subscribe({
      next: (demandes) => {
        this.mesDemandesSousCategories = demandes;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des demandes de sous-catégories:', error);
      }
    });
  }

  submitDemandeCategorie() {
    if (this.categorieForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      this.clearMessages();

      const demande: CreateDemandeCategorieDto = {
        nom: this.categorieForm.value.nom,
        description: this.categorieForm.value.description || ''
      };

      this.demandeService.createDemandeCategorie(demande).subscribe({
        next: (response) => {
          this.successMessage = 'Votre demande de catégorie a été envoyée avec succès !';
          this.resetCategorieForm();
          this.isSubmitting = false;
        },
        error: (error) => {
          this.errorMessage = 'Erreur lors de l\'envoi de la demande. Veuillez réessayer.';
          this.isSubmitting = false;
        }
      });
    }
  }

  submitDemandeSousCategorie() {
    if (this.sousCategorieForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      this.clearMessages();

      const demande: CreateDemandeSousCategorieDto = {
        nom: this.sousCategorieForm.value.nom,
        description: this.sousCategorieForm.value.description || '',
        categorieId: parseInt(this.sousCategorieForm.value.categorieId)
      };

      this.demandeService.createDemandeSousCategorie(demande).subscribe({
        next: (response) => {
          this.successMessage = 'Votre demande de sous-catégorie a été envoyée avec succès !';
          this.resetSousCategorieForm();
          this.isSubmitting = false;
        },
        error: (error) => {
          this.errorMessage = 'Erreur lors de l\'envoi de la demande. Veuillez réessayer.';
          this.isSubmitting = false;
        }
      });
    }
  }

  resetCategorieForm() {
    this.categorieForm.reset();
    this.clearMessages();
  }

  resetSousCategorieForm() {
    this.sousCategorieForm.reset();
    this.clearMessages();
  }

  clearMessages() {
    this.successMessage = '';
    this.errorMessage = '';
  }

  getStatutLabel(statut: StatutDemande): string {
    return this.demandeService.getStatutLabel(statut);
  }

  getStatutColor(statut: StatutDemande): string {
    return this.demandeService.getStatutColor(statut);
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
