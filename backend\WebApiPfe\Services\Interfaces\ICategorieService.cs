﻿using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.Models.Entity;
using WebApiPfe.DTOs.ReadDTOs;
namespace WebApiPfe.Services.Interfaces
{
    public interface ICategorieService
    {
        // CRUD de base
        Task<CategorieDto> GetByIdAsync(int id);
        Task<IEnumerable<Categorie>> GetAllAsync();
        Task<IEnumerable<Categorie>> GetAllForAdminAsync();
        Task<Categorie> CreateAsync(Categorie categorie);
        Task UpdateAsync(Categorie categorie);
        Task DeleteAsync(int id);

        // Méthodes spécifiques marketplace
        Task<bool> ExistsAsync(int id);
        Task<IEnumerable<SousCategorie>> GetSousCategoriesAsync(int categorieId);
        Task<int> GetProduitsCountAsync(int categorieId);
        Task<Dictionary<int, string>> GetCategoriesForDropdownAsync();
        Task ToggleVisibilityAsync(int id);
    }
}
