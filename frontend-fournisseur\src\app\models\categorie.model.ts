// Modèle Categorie selon l'API backend
export interface Categorie {
  id: number;
  nom: string;
  description?: string;
  estValidee: boolean;
  sousCategoriesCount?: number;
}

// Interface pour créer une catégorie
export interface CategorieCreate {
  nom: string;
  description?: string;
}

// Interface pour mettre à jour une catégorie
export interface CategorieUpdate {
  nom?: string;
  description?: string;
  estValidee?: boolean;
}

// Interface pour les réponses API
export interface CategorieResponse {
  success: boolean;
  data?: Categorie;
  message?: string;
}

export interface CategorieListResponse {
  success: boolean;
  data?: Categorie[];
  total?: number;
  message?: string;
}

// Interface pour dropdown (compatible avec le backend)
export interface CategorieDropdown {
  id: number;
  nom: string;
  sousCategoriesCount?: number;
}

// Interface pour les catégories admin (avec sous-catégories)
export interface CategorieAdmin {
  id: number;
  nom: string;
  description?: string;
  estValidee: boolean;
  sousCategories: SousCategorieAdmin[];
}

export interface SousCategorieAdmin {
  id: number;
  nom: string;
  description?: string;
  estValidee: boolean;
  categorieId: number;
}
