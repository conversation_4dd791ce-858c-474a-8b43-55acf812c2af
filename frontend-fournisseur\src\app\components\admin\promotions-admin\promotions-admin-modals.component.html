<!-- Modal de création -->
<div *ngIf="showCreateModal" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-plus"></i> Nouvelle Promotion
        </h5>
        <button type="button" class="btn-close" (click)="closeCreateModal()"></button>
      </div>
      
      <form [formGroup]="createForm" (ngSubmit)="createPromotion()">
        <div class="modal-body">
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label">Code promotion *</label>
              <input type="text" class="form-control" formControlName="code" 
                     placeholder="Ex: ADMIN2024" (blur)="checkCodeUniqueness()">
              <div class="form-text">Code unique pour identifier la promotion</div>
              <div *ngIf="createForm.get('code')?.errors?.['codeExists']" class="text-danger">
                Ce code existe déjà
              </div>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Type de promotion *</label>
              <select class="form-select" formControlName="type">
                <option [value]="TypePromotion.Pourcentage">Pourcentage</option>
                <option [value]="TypePromotion.MontantFixe">Montant fixe</option>
                <option [value]="TypePromotion.Outlet">Outlet</option>
              </select>
            </div>
            
            <div class="col-12">
              <label class="form-label">Nom de la promotion *</label>
              <input type="text" class="form-control" formControlName="nom" 
                     placeholder="Ex: Promotion spéciale admin">
            </div>
            
            <div class="col-12">
              <label class="form-label">Description</label>
              <textarea class="form-control" rows="3" formControlName="description"
                        placeholder="Description de la promotion..."></textarea>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Valeur *</label>
              <div class="input-group">
                <input type="number" class="form-control" formControlName="valeur" 
                       min="0" max="100" step="0.01">
                <span class="input-group-text">
                  {{ createForm.get('type')?.value === TypePromotion.MontantFixe ? '€' : '%' }}
                </span>
              </div>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Montant minimum</label>
              <div class="input-group">
                <input type="number" class="form-control" formControlName="montantMinimum" 
                       min="0" step="0.01">
                <span class="input-group-text">€</span>
              </div>
              <div class="form-text">Montant minimum de commande requis</div>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Date de début *</label>
              <input type="date" class="form-control" formControlName="dateDebut">
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Date de fin *</label>
              <input type="date" class="form-control" formControlName="dateFin">
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Nombre d'utilisations max</label>
              <input type="number" class="form-control" formControlName="utilisationsMax" 
                     min="1" placeholder="Illimité si vide">
              <div class="form-text">Laissez vide pour un usage illimité</div>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">ID Fournisseur</label>
              <input type="number" class="form-control" formControlName="fournisseurId" 
                     placeholder="Laissez vide pour promotion globale">
              <div class="form-text">Promotion spécifique à un fournisseur</div>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">ID Produit</label>
              <input type="number" class="form-control" formControlName="produitId" 
                     placeholder="Laissez vide pour tous les produits">
              <div class="form-text">Promotion spécifique à un produit</div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
            Annuler
          </button>
          <button type="submit" class="btn btn-primary" [disabled]="!createForm.valid">
            <i class="bi bi-check"></i> Créer la promotion
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal d'édition -->
<div *ngIf="showEditModal" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-pencil"></i> Modifier la promotion
        </h5>
        <button type="button" class="btn-close" (click)="closeEditModal()"></button>
      </div>
      
      <form [formGroup]="editForm" (ngSubmit)="updatePromotion()">
        <div class="modal-body">
          <div class="row g-3">
            <div class="col-12">
              <label class="form-label">Nom de la promotion *</label>
              <input type="text" class="form-control" formControlName="nom">
            </div>
            
            <div class="col-12">
              <label class="form-label">Description</label>
              <textarea class="form-control" rows="3" formControlName="description"></textarea>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Valeur *</label>
              <div class="input-group">
                <input type="number" class="form-control" formControlName="valeur" 
                       min="0" max="100" step="0.01">
                <span class="input-group-text">
                  {{ selectedPromotion?.type === TypePromotion.MontantFixe ? '€' : '%' }}
                </span>
              </div>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Montant minimum</label>
              <div class="input-group">
                <input type="number" class="form-control" formControlName="montantMinimum" 
                       min="0" step="0.01">
                <span class="input-group-text">€</span>
              </div>
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Date de début *</label>
              <input type="date" class="form-control" formControlName="dateDebut">
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Date de fin *</label>
              <input type="date" class="form-control" formControlName="dateFin">
            </div>
            
            <div class="col-md-6">
              <label class="form-label">Nombre d'utilisations max</label>
              <input type="number" class="form-control" formControlName="utilisationsMax" 
                     min="1" placeholder="Illimité si vide">
            </div>
            
            <div class="col-md-6">
              <label class="form-label">ID Fournisseur</label>
              <input type="number" class="form-control" formControlName="fournisseurId" 
                     placeholder="Promotion globale si vide">
            </div>
            
            <div class="col-12">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" formControlName="estActive">
                <label class="form-check-label">
                  Promotion active
                </label>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
            Annuler
          </button>
          <button type="submit" class="btn btn-primary" [disabled]="!editForm.valid">
            <i class="bi bi-check"></i> Mettre à jour
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal de détails -->
<div *ngIf="showDetailsModal && selectedPromotion" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-eye"></i> Détails de la promotion
        </h5>
        <button type="button" class="btn-close" (click)="closeDetailsModal()"></button>
      </div>
      
      <div class="modal-body">
        <div class="row g-4">
          <!-- Informations générales -->
          <div class="col-md-6">
            <h6>Informations générales</h6>
            <table class="table table-sm">
              <tr>
                <td><strong>Nom:</strong></td>
                <td>{{ selectedPromotion.nom }}</td>
              </tr>
              <tr>
                <td><strong>Code:</strong></td>
                <td><code>{{ selectedPromotion.code }}</code></td>
              </tr>
              <tr>
                <td><strong>Type:</strong></td>
                <td>
                  <span class="badge" [class]="'bg-' + getTypeColor(selectedPromotion.type)">
                    {{ getTypeLibelle(selectedPromotion.type) }}
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>Valeur:</strong></td>
                <td>{{ formatValeur(selectedPromotion.type, selectedPromotion.valeur) }}</td>
              </tr>
              <tr>
                <td><strong>Statut:</strong></td>
                <td>
                  <span class="badge" [class]="'bg-' + getStatutColor(selectedPromotion)">
                    {{ getStatutLibelle(selectedPromotion) }}
                  </span>
                </td>
              </tr>
            </table>
          </div>
          
          <!-- Conditions et limites -->
          <div class="col-md-6">
            <h6>Conditions et limites</h6>
            <table class="table table-sm">
              <tr>
                <td><strong>Date début:</strong></td>
                <td>{{ selectedPromotion.dateDebut | date:'dd/MM/yyyy HH:mm' }}</td>
              </tr>
              <tr>
                <td><strong>Date fin:</strong></td>
                <td>{{ selectedPromotion.dateFin | date:'dd/MM/yyyy HH:mm' }}</td>
              </tr>
              <tr>
                <td><strong>Montant minimum:</strong></td>
                <td>{{ selectedPromotion.montantMinimum ? (selectedPromotion.montantMinimum + '€') : 'Aucun' }}</td>
              </tr>
              <tr>
                <td><strong>Utilisations max:</strong></td>
                <td>{{ selectedPromotion.utilisationsMax || 'Illimitées' }}</td>
              </tr>
              <tr>
                <td><strong>Utilisations actuelles:</strong></td>
                <td>{{ selectedPromotion.utilisationsActuelles }}</td>
              </tr>
            </table>
          </div>
          
          <!-- Description -->
          <div class="col-12" *ngIf="selectedPromotion.description">
            <h6>Description</h6>
            <p class="border p-3 rounded bg-light">{{ selectedPromotion.description }}</p>
          </div>
          
          <!-- Fournisseur et produit -->
          <div class="col-md-6" *ngIf="selectedPromotion.fournisseurRaisonSociale">
            <h6>Fournisseur</h6>
            <p>
              <i class="bi bi-building"></i> {{ selectedPromotion.fournisseurRaisonSociale }}<br>
              <small class="text-muted">{{ selectedPromotion.fournisseurNom }}</small>
            </p>
          </div>
          
          <div class="col-md-6" *ngIf="selectedPromotion.produitNom">
            <h6>Produit spécifique</h6>
            <p>
              <i class="bi bi-box"></i> {{ selectedPromotion.produitNom }}
            </p>
          </div>
          
          <!-- Statistiques -->
          <div class="col-12">
            <h6>Statistiques d'utilisation</h6>
            <div class="row g-3">
              <div class="col-md-4">
                <div class="stat-box">
                  <div class="stat-value">{{ selectedPromotion.montantTotalEconomise | number:'1.2-2' }}€</div>
                  <div class="stat-label">Économies totales</div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="stat-box">
                  <div class="stat-value">{{ selectedPromotion.nombreCommandesImpactees }}</div>
                  <div class="stat-label">Commandes impactées</div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="stat-box">
                  <div class="stat-value">{{ getTauxUtilisation(selectedPromotion) | number:'1.1-1' }}%</div>
                  <div class="stat-label">Taux d'utilisation</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeDetailsModal()">
          Fermer
        </button>
        <button type="button" class="btn btn-primary" (click)="openEditModal(selectedPromotion); closeDetailsModal()">
          <i class="bi bi-pencil"></i> Modifier
        </button>
      </div>
    </div>
  </div>
</div>
