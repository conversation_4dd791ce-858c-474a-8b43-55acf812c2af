﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class SousCategorieService : ISousCategorieService
    {
        private readonly AppDbContext _context;
        private readonly ICategorieService _categorieService;

        public SousCategorieService(AppDbContext context, ICategorieService categorieService)
        {
            _context = context;
            _categorieService = categorieService;
        }

        public async Task<SousCategorie> CreateAsync(SousCategorie sousCategorie)
        {
            // Vérification que la catégorie existe
            if (!await _categorieService.ExistsAsync(sousCategorie.CategorieId))
                throw new ArgumentException("La catégorie spécifiée n'existe pas");

            // Vérification unicité du nom dans la même catégorie
            if (!await IsNameUniqueInCategorieAsync(sousCategorie.Nom, sousCategorie.CategorieId))
                throw new ArgumentException("Ce nom est déjà utilisé dans cette catégorie");

            await _context.SousCategories.AddAsync(sousCategorie);
            await _context.SaveChangesAsync();
            return sousCategorie;
        }

        public async Task DeleteAsync(int id)
        {
            var sousCategorie = await _context.SousCategories
                .Include(sc => sc.Produits)
                .FirstOrDefaultAsync(sc => sc.Id == id);

            if (sousCategorie == null)
                throw new KeyNotFoundException("Sous-catégorie non trouvée");

            if (sousCategorie.Produits?.Any() == true)
                throw new InvalidOperationException("Impossible de supprimer : des produits sont associés");

            _context.SousCategories.Remove(sousCategorie);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(int id) =>
            await _context.SousCategories.AnyAsync(sc => sc.Id == id);

        public async Task<IEnumerable<SousCategorie>> GetAllAsync() =>
            await _context.SousCategories
                .Include(sc => sc.Categorie)
                .Include(sc => sc.Produits.Where(p => p.FournisseurId != null))
                .AsNoTracking()
                .OrderBy(sc => sc.Nom)
                .ToListAsync();

        public async Task<SousCategorie> GetByIdAsync(int id) =>
            await _context.SousCategories
                .FirstOrDefaultAsync(sc => sc.Id == id);

        public async Task<IEnumerable<SousCategorie>> GetByCategorieAsync(int categorieId) =>
            await _context.SousCategories
                .Where(sc => sc.CategorieId == categorieId)
                .OrderBy(sc => sc.Nom)
                .ToListAsync();

        public async Task<int> GetProduitsCountAsync(int sousCategorieId) =>
            await _context.Produits
                .CountAsync(p => p.SousCategorieId == sousCategorieId);

        public async Task<Dictionary<int, string>> GetSousCategoriesForDropdownAsync(int categorieId) =>
            await _context.SousCategories
                .Where(sc => sc.CategorieId == categorieId)
                .OrderBy(sc => sc.Nom)
                .ToDictionaryAsync(sc => sc.Id, sc => sc.Nom);

        public async Task UpdateAsync(SousCategorie sousCategorie)
        {
            if (!await ExistsAsync(sousCategorie.Id))
                throw new KeyNotFoundException("Sous-catégorie non trouvée");

            if (!await IsNameUniqueInCategorieAsync(sousCategorie.Nom, sousCategorie.CategorieId, sousCategorie.Id))
                throw new ArgumentException("Ce nom est déjà utilisé dans cette catégorie");

            _context.SousCategories.Update(sousCategorie);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> IsNameUniqueInCategorieAsync(string name, int categorieId, int? ignoreId = null)
        {
            var query = _context.SousCategories
                .Where(sc => sc.CategorieId == categorieId && sc.Nom == name);

            if (ignoreId.HasValue)
                query = query.Where(sc => sc.Id != ignoreId.Value);

            return !await query.AnyAsync();
        }
    }
}
